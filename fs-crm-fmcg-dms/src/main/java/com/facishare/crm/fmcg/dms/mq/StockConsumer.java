package com.facishare.crm.fmcg.dms.mq;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.common.apiname.AccountsReceivableDetailFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.business.DMSScriptHandlerFactory;
import com.facishare.crm.fmcg.dms.business.PaasLicenseBusinessService;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.RetryActionException;
import com.facishare.crm.fmcg.dms.mq.model.StockBizConfEventObj;
import com.facishare.crm.fmcg.dms.web.abstraction.IAccountsReceivableNoteService;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;


@Component
public class StockConsumer {

    private static final Logger logger = LoggerFactory.getLogger(StockConsumer.class);
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_08,dms_stock_biz_conf";
    private AutoConfMQPushConsumer processor;

    @Resource
    private PaasLicenseBusinessService paasLicenseBusinessService;
    @Resource
    private IAccountsReceivableNoteService accountsReceivableNoteService;
    @Resource
    private DMSScriptHandlerFactory dmsScriptHandlerFactory;
    @Resource
    private ServiceFacade serviceFacade;

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.dms.flag"))) {
            return;
        }
        logger.info("PaasLicenseConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[PaasLicenseConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init PaasLicenseConsumer mq consumer failed.", e);
        }
    }

    private boolean tobeContinue(Map<String, IObjectDescribe> describes) {
        if (describes == null || describes.isEmpty()) {
            return false;
        }
        IObjectDescribe objectDescribe = describes.get(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ);
        if (objectDescribe == null) {
            throw new AbandonActionException("AccountsReceivableDetailObj describe is null");
        }
        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            if (AccountsReceivableDetailFields.DELIVERY_NOTE_ID.equals(fieldDescribe.getApiName())) {
                return false;
            }
        }
        return true;
    }

    private void process(MessageExt messageExt) {
        StockBizConfEventObj stockBizConfEventObj = JSON.parseObject(messageExt.getBody(), StockBizConfEventObj.class);

        if (stockBizConfEventObj == null) {
            return;
        }
        logger.info("StockConsumer consume start.TenantId:{},enable:{}", stockBizConfEventObj.getUser().getTenantId(), stockBizConfEventObj.getNewValue());
        if (!Objects.equals(stockBizConfEventObj.getNewValue(), "2")) {
            return;
        }

        String tenantId = stockBizConfEventObj.getUser().getTenantId();
        Map<String, IObjectDescribe> describes;
        try {
            describes =
                    serviceFacade.findObjects(tenantId, Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));
        } catch (ObjectDefNotFoundError ex) {
            throw new AbandonActionException("AccountsReceivableDetailObj describe is null");
        } catch (Exception ex) {
            throw new RetryActionException("AccountsReceivableDetailObj describe is null");
        }
        if (!tobeContinue(describes)) {
            return;
        }
        boolean enableAccountsReceivable = !accountsReceivableNoteService.denyAccountsReceivableEnable(tenantId);
        boolean enableDealerKX = paasLicenseBusinessService.checkKXEnableByModuleCode(tenantId, PaasLicenseConsumer.DEALER_EDITION);
        boolean enableAdvanceDealerKX = paasLicenseBusinessService.checkKXEnableByModuleCode(tenantId, PaasLicenseConsumer.ADVANCED_DEALER_EDITION);
        logger.info("enableAccountsReceivable:{},enableDealerKX:{},enableAdvanceDealerKX:{},tenantId:{}", enableDealerKX, enableAdvanceDealerKX, enableAccountsReceivable, tenantId);

        if (enableAccountsReceivable && (enableDealerKX || enableAdvanceDealerKX)) {

            IDMSScriptHandler handler = dmsScriptHandlerFactory.resolve(ScriptHandlerNameEnum.KX_DEALER_EDITION_ENABLE.getHandlerName());
            handler.addFields(tenantId, describes);

        }

    }

    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
