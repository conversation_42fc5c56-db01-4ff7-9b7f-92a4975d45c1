package com.facishare.crm.fmcg.dms.service.abastraction;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.RetryActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FmcgSalesProxy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@SuppressWarnings("Duplicates")
@Slf4j
public abstract class MatchableBillMapService implements IMatchableBillMapService {

    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected FmcgSalesProxy fmcgSalesProxy;

    protected abstract void beforeMap(FinancialBill bill);

    protected abstract void validate(FinancialBill bill);

    protected abstract List<FinancialBill> mapToMatchableBills(FinancialBill bill);

    protected List<String> findReceivablesBySalesOrderId(String tenantId, String orderId) {
        return findReceivablesBySalesOrderIds(tenantId, Lists.newArrayList(orderId));
    }

    protected List<String> findReceivablesBySalesOrderIds(String tenantId, List<String> orderIds) {
        List<String> all = Lists.newArrayList();
        for (String salesOrderId : orderIds) {
            List<IObjectData> details = queryReceivableDetailsOfDeliveryNote(tenantId, salesOrderId);

            all.addAll(details.stream()
                    .map(detail -> detail.get(AccountsReceivableDetailFields.AR_ID, String.class))
                    .distinct()
                    .collect(Collectors.toList()));
        }
        return all;
    }

    protected List<String> findReceivablesByReturnedNoteIds(String tenantId, List<String> returnNoteIds) {
        List<String> all = Lists.newArrayList();
        for (String returnNoteId : returnNoteIds) {
            List<IObjectData> details = queryReceivableDetailsOfReturnNote(tenantId, returnNoteId);

            all.addAll(details.stream()
                    .map(detail -> detail.get(AccountsReceivableDetailFields.AR_ID, String.class))
                    .distinct()
                    .collect(Collectors.toList()));
        }
        return all;
    }

    private List<String> queryDeliveryNoteIds(String tenantId, String orderId) {
        //兼容未开启发货单
        try {
            serviceFacade.findObject(tenantId, ApiNames.DELIVERY_NOTE_OBJ);
        } catch (ObjectDefNotFoundError ex) {
            log.info("queryDeliveryNoteIds DeliveryNoteObj not found : order id - {}", orderId);
            return Lists.newArrayList();
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName(DeliveryNoteFields.SALES_ORDER_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(orderId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.DELIVERY_NOTE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID)
        ).stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<String> queryGoodsReceivedNoteIds(String tenantId, String returnNoteId) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(GoodsReceivedNoteFields.RETURN_NOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(returnNoteId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.GOODS_RECEIVED_NOTE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID)
        ).stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<IObjectData> queryReceivableDetailsOfReturnNote(String tenantId, String returnNoteId) {
        List<String> ids = queryGoodsReceivedNoteIds(tenantId, returnNoteId);

        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery stq;
        if (!TPMGrayUtils.accountReceivableDetailUseWhatField(tenantId)) {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(ids);

            stq = QueryDataUtil.minimumQuery(idFilter);
        } else {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(ids);

            IFilter apiNameFilter = new Filter();
            apiNameFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME);
            apiNameFilter.setOperator(Operator.EQ);
            apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.GOODS_RECEIVED_NOTE_OBJ));

            stq = QueryDataUtil.minimumQuery(idFilter, apiNameFilter);
        }


        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, AccountsReceivableDetailFields.AR_ID)
        );

//        log.info("related accounts receivable details : return note id - {}, details - {}", returnNoteId, JSON.toJSONString(data));

        return data;
    }

    private List<IObjectData> queryReceivableDetailsOfDeliveryNote(String tenantId, String orderId) {
        //兼容未开启应收单
        try {
            serviceFacade.findObject(tenantId, ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ);
        } catch (ObjectDefNotFoundError ex) {
            return Lists.newArrayList();
        }
        List<String> ids = queryDeliveryNoteIds(tenantId, orderId);

        log.info("related delivery note id list : order id - {}, delivery note id list - {}", orderId, ids);

        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery stq;
        if (!TPMGrayUtils.accountReceivableDetailUseWhatField(tenantId)) {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(AccountsReceivableDetailFields.DELIVERY_NOTE_ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(ids);

            stq = QueryDataUtil.minimumQuery(idFilter);
        } else {
            IFilter idFilter = new Filter();
            idFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(ids);

            IFilter apiNameFilter = new Filter();
            apiNameFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME);
            apiNameFilter.setOperator(Operator.EQ);
            apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.DELIVERY_NOTE_OBJ));


            stq = QueryDataUtil.minimumQuery(idFilter, apiNameFilter);
        }


        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, AccountsReceivableDetailFields.AR_ID)
        );

//        log.info("related accounts receivable details : order_id - {}, details - {}", orderId, JSON.toJSONString(data));

        return data;
    }

    protected List<FinancialBill> convertToAccountsReceivableNote(FinancialBill bill, List<String> ids) {
        return ids.stream().map(id -> FinancialBill.builder()
                .tenantId(bill.getTenantId())
                .apiName(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ)
                .id(id)
                .build()).collect(Collectors.toList());
    }

    protected List<FinancialBill> convertToAccountsPayableNote(FinancialBill bill, List<String> ids) {
        return ids.stream().map(id -> FinancialBill.builder()
                .tenantId(bill.getTenantId())
                .apiName(ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ)
                .id(id)
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<FinancialBill> map(FinancialBill bill) {
        log.info("map started : {}", bill);

        StopWatch watch = StopWatch.create("FINANCIAL_BILL_MATCH_TRIGGER." + bill.getApiName() + "." + bill.getId());

        beforeMap(bill);
        watch.lap("beforeMap");

        try {
            validate(bill);
            watch.lap("validate");

            List<FinancialBill> bills = mapToMatchableBills(bill);
            watch.lap("mapToMatchBill");

            log.info("map success : {} -> {}", JSON.toJSONString(bill), JSON.toJSONString(bills));

            return bills;
        } catch (AbandonActionException ex) {
            log.warn("financial bill map abandon : ", ex);
            return Lists.newArrayList();
        } catch (RetryActionException ex) {
            log.error("financial bill map cause retry exception : ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("financial bill map cause unknown exception : ", ex);
            throw ex;
        } finally {
            watch.logSlow(300);
        }
    }


    protected List<FinancialBill> querySwapOutReceivableNoteByReturnGoodsInvoiceId(String tenantId, String returnGoodsInvoiceId, String returnGoodsInvoiceRecordType) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceId));

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(AccountsReceivableNoteFields.OBJECT_RECEIVABLE_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.RETURNED_GOODS_INVOICE_OBJ));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter, apiNameFilter);
        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME
                )
        );

        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }

        List<IObjectData> details = queryReceivableDetails(tenantId, data.stream().map(DBRecord::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        Map<String, String> arMap = details.stream().collect(Collectors.toMap(k -> k.get(AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID, String.class),
                v -> v.get(AccountsReceivableDetailFields.AR_ID, String.class), (o, n) -> n));

        List<String> returnGoodsDetails = queryReturnGoodsDetailIdsWithRecordType(tenantId, Lists.newArrayList(arMap.keySet()), returnGoodsInvoiceRecordType);

        List<FinancialBill> accountsReceivables = Lists.newArrayList();
        arMap.forEach((detailId, arId) -> {
            if (returnGoodsDetails.contains(detailId)) {
                accountsReceivables.add(FinancialBill.builder()
                        .tenantId(tenantId)
                        .apiName(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ)
                        .id(arId)
                        .build());
            }
        });
        return accountsReceivables;
    }


    private List<IObjectData> queryReceivableDetails(String tenantId, List<String> arIds) {
        IFilter detailIdFilter = new Filter();
        detailIdFilter.setFieldName(AccountsReceivableDetailFields.AR_ID);
        detailIdFilter.setOperator(Operator.IN);
        detailIdFilter.setFieldValues(Lists.newArrayList(arIds));

        SearchTemplateQuery detailStq = QueryDataUtil.minimumQuery(detailIdFilter);
        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                detailStq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        AccountsReceivableDetailFields.AR_ID,
                        AccountsReceivableDetailFields.SOURCE_DETAIL_DATA_ID
                )
        );
    }

    private List<String> queryReturnGoodsDetailIdsWithRecordType(String tenantId, List<String> returnGoodsInvoiceIds, String returnGoodsInvoiceRecordType) {
        IFilter returnGoodsInvoiceDetailIdFilter = new Filter();
        returnGoodsInvoiceDetailIdFilter.setFieldName(CommonFields.ID);
        returnGoodsInvoiceDetailIdFilter.setOperator(Operator.IN);
        returnGoodsInvoiceDetailIdFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceIds));

        IFilter recordTypeFilter = new Filter();
        recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        recordTypeFilter.setOperator(Operator.EQ);
        recordTypeFilter.setFieldValues(Lists.newArrayList(returnGoodsInvoiceRecordType));

        SearchTemplateQuery returnGoodsInvoiceDetailStq = QueryDataUtil.minimumQuery(returnGoodsInvoiceDetailIdFilter, recordTypeFilter);
        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ,
                returnGoodsInvoiceDetailStq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME
                )
        );
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        return data.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

}
