package com.facishare.crm.fmcg.dms.service.mapper;

import com.alibaba.fastjson2.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.MatchableBillMapService;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.contract.sales.QueryRelatedOrder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PaymentMatchableBillMapService extends MatchableBillMapService {

    @Override
    protected void beforeMap(FinancialBill bill) {
        if (Objects.isNull(bill.getData())) {
            IObjectData data;
            try {
                data = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.PAYMENT_OBJ);
            } catch (ObjectDataNotFoundException ex) {
                log.info("data not found,tenantId:{},apiName:{},id:{}", bill.getTenantId(), bill.getApiName(), bill.getId());
                throw new AbandonActionException("data not found");
            } catch (MetaDataBusinessException ex) {
                if ("数据已作废或已删除".equals(ex.getMessage()) && 320002500 == ex.getErrorCode()) { //ignorei18n
                    log.info("data not found,tenantId:{},apiName:{},id:{}", bill.getTenantId(), bill.getApiName(), bill.getId());
                    throw new AbandonActionException("data not found");
                }
                log.error("find object error ", ex);
                throw ex;
            }
            if (!Objects.equals(data.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
                throw new AbandonActionException("payment approval ：" + bill.getId());
            }
            bill.setData(data);
        }
        if (CollectionUtils.isEmpty(bill.getDetails())) {
            bill.setDetails(queryPaymentDetails(bill.getTenantId(), bill.getData().getId()));
        }
    }

    @Override
    protected void validate(FinancialBill bill) {
        if (Strings.isNullOrEmpty(bill.getData().get(PaymentFields.ACCOUNT_ID, String.class))) {
            throw new AbandonActionException("payment bill account id cat not be null or empty.");
        }
        if (Objects.equals(Boolean.TRUE, bill.getData().get(PaymentFields.OPENING_BALANCE, Boolean.class))) {
            throw new AbandonActionException("opening balance payment abandon.");
        }
        bill.setDetails(bill.getDetails().stream().filter(detail ->
                !Strings.isNullOrEmpty(detail.get(PaymentDetailFields.ORDER_ID, String.class)) ||
                        !Strings.isNullOrEmpty(detail.get(PaymentDetailFields.RETURNED_GOODS_INVOICE_ID, String.class))
        ).collect(Collectors.toList()));
    }

    @Override
    protected List<FinancialBill> mapToMatchableBills(FinancialBill bill) {
        String returnGoodsInvoiceId = bill.getData().get(PaymentFields.RETURNED_GOODS_INVOICE_ID, String.class);
        log.info("returnGoodsInvoiceId:{}", returnGoodsInvoiceId);
        if (!StringUtils.isEmpty(returnGoodsInvoiceId)) {
            IObjectData data = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), returnGoodsInvoiceId, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
            log.info("returnGoodsInvoice recordType:{}", data.getRecordType());
            if (ReturnedGoodsInvoiceFields.EXCHANGE_RECORD.equals(data.getRecordType())) {
                return buildFinancialBillWithPayment(bill, returnGoodsInvoiceId);
            } else {
                return buildFinancialBillWithPaymentDetails(bill);
            }
        } else {
            return buildFinancialBillWithPaymentDetails(bill);
        }
    }

    private List<FinancialBill> buildFinancialBillWithPayment(FinancialBill bill, String returnGoodsInvoiceId) {
        List<FinancialBill> financialBillsWithSwapOut = querySwapOutReceivableNoteByReturnGoodsInvoiceId(bill.getTenantId(), returnGoodsInvoiceId, ReturnedGoodsInvoiceProductFields.SWAP_OUT);
        List<FinancialBill> financialBillsWithDefault = querySwapOutReceivableNoteByReturnGoodsInvoiceId(bill.getTenantId(), returnGoodsInvoiceId, CommonFields.RECORD_TYPE__DEFAULT);
        financialBillsWithSwapOut.addAll(financialBillsWithDefault);
        log.info("financialBillsWithSwapOut：{}", JSON.toJSONString(financialBillsWithSwapOut));
        return financialBillsWithSwapOut.stream().map(FinancialBill::getId).distinct().map(id -> FinancialBill.builder()
                .tenantId(bill.getTenantId())
                .apiName(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ)
                .id(id)
                .build()).collect(Collectors.toList());
    }

    private List<FinancialBill> buildFinancialBillWithPaymentDetails(FinancialBill bill) {
        List<String> salesOrderIds = bill.getDetails().stream()
                .map(detail -> detail.get(PaymentDetailFields.ORDER_ID, String.class))
                .filter(orderId -> !Strings.isNullOrEmpty(orderId))
                .collect(Collectors.toList());

        List<String> returnedNoteIds = bill.getDetails().stream()
                .map(detail -> detail.get(PaymentDetailFields.RETURNED_GOODS_INVOICE_ID, String.class))
                .filter(orderId -> !Strings.isNullOrEmpty(orderId))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(returnedNoteIds)) {

            List<IObjectData> returnedNotes = serviceFacade.findObjectDataByIdsIgnoreAll(bill.getTenantId(), returnedNoteIds, ApiNames.RETURNED_GOODS_INVOICE_OBJ);
            salesOrderIds.addAll(returnedNotes.stream()
                    .map(returnedNote -> returnedNote.get(ReturnedGoodsInvoiceFields.ORDER_ID, String.class))
                    .filter(f -> !Strings.isNullOrEmpty(f))
                    .collect(Collectors.toList()));

            List<IObjectData> returnedNoteDetails = queryReturnedNoteDetails(bill.getTenantId(), returnedNoteIds);
            for (IObjectData returnedNoteDetail : returnedNoteDetails) {
                String innerOrderId = returnedNoteDetail.get(ReturnedGoodsInvoiceProductFields.ORDER_ID, String.class);
                if (!Strings.isNullOrEmpty(innerOrderId)) {
                    salesOrderIds.add(innerOrderId);
                }
            }

            for (String returnedNoteId : returnedNoteIds) {
                int tenantIdInt = Integer.parseInt(bill.getTenantId());

                QueryRelatedOrder.Arg arg = new QueryRelatedOrder.Arg();
                arg.setTenantId(tenantIdInt);
                arg.setApiName(ApiNames.RETURNED_GOODS_INVOICE_OBJ);
                arg.setDataId(returnedNoteId);
                arg.setRelatedApiName(ApiNames.SALES_ORDER_OBJ);

                QueryRelatedOrder.Result relatedOrderResult = fmcgSalesProxy.queryRelatedOrder(tenantIdInt, -10000, arg);

                if (ApiNames.SALES_ORDER_OBJ.equals(relatedOrderResult.getRelatedApiName()) && CollectionUtils.isNotEmpty(relatedOrderResult.getDataIdList())) {
                    salesOrderIds.addAll(relatedOrderResult.getDataIdList());
                }
            }
        }

        List<String> receivableIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(salesOrderIds)) {
            receivableIds.addAll(findReceivablesBySalesOrderIds(bill.getTenantId(), salesOrderIds));
        }
        if (CollectionUtils.isNotEmpty(returnedNoteIds)) {
            receivableIds.addAll(findReceivablesByReturnedNoteIds(bill.getTenantId(), returnedNoteIds));
        }

        receivableIds = receivableIds.stream().distinct().collect(Collectors.toList());

        return convertToAccountsReceivableNote(bill, receivableIds);
    }

    private List<IObjectData> queryReturnedNoteDetails(String tenantId, List<String> returnedNoteIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(ReturnedGoodsInvoiceProductFields.RETURNED_GOODS_INV_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(returnedNoteIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        ReturnedGoodsInvoiceProductFields.ORDER_ID
                )
        );
    }

    private List<IObjectData> queryPaymentDetails(String tenantId, String paymentId) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(PaymentDetailFields.PAYMENT_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(paymentId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ORDER_PAYMENT_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        PaymentDetailFields.PAYMENT_AMOUNT,
                        PaymentDetailFields.PAYMENT_ID,
                        PaymentDetailFields.ORDER_ID,
                        PaymentDetailFields.RETURNED_GOODS_INVOICE_ID
                )
        );
    }
}