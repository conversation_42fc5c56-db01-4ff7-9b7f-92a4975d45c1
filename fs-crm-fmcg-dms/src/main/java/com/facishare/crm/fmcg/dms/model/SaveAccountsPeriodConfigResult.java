package com.facishare.crm.fmcg.dms.model;

import lombok.*;

import java.io.Serializable;
import java.util.List;

@Data
public class SaveAccountsPeriodConfigResult {
    private List<ObjectVO> objects;
    private boolean open;

    public SaveAccountsPeriodConfigResult(List<ObjectVO> objects, boolean open) {
        this.objects = objects;
        this.open = open;
    }

    public SaveAccountsPeriodConfigResult() {
    }

    @Data
    @ToString
    @Builder
    @AllArgsConstructor
    public static class ObjectVO implements Serializable {
        private String apiName;
        private String label;
        private RecordTypeVO recordType;
        private boolean block;
    }

    @Data
    @ToString
    @Builder
    @AllArgsConstructor
    public static class RecordTypeVO implements Serializable {
        private String apiName;
        private String label;
    }
}


