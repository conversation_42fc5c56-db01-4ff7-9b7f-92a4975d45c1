package com.facishare.crm.fmcg.dms.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.PurchaseOrderObjFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.dms.business.PaasLicenseBusinessService;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.errors.ModuleRetryException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.mq.model.TriggerMessageObj;
import com.facishare.crm.fmcg.dms.service.converter.AutoConverterActuator;
import com.facishare.crm.fmcg.dms.service.mapper.MatchableBillMapper;
import com.facishare.crm.fmcg.dms.service.matcher.AutoMatcher;
import com.facishare.crm.fmcg.dms.util.CastUtil;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@Component
@SuppressWarnings("Duplicates")
public class ObjectDataMqConsumer implements ApplicationListener<ContextRefreshedEvent> {

    private static final String CONFIG_NAME = "fs-fmcg-object-mq";
    private static final String NAME_SERVER_KEY = "OBJECT_V2_NAMESERVER";
    private static final String TOPIC_KEY = "OBJECT_V2_TOPICS";
    private static final String GROUP_KEY = "OBJECT_V2_GROUP_CONSUMER_DMS";

    public static final String METE_DATA_OP_ACTION_INSERT = "i";
    public static final String METE_DATA_OP_ACTION_UPDATE = "u";
    private static final int MAX_RECONSUME_TIMES = 10;

    private AutoConfMQPushConsumer consumer;

    @Resource
    private AutoMatcher autoMatcher;
    @Resource
    private MatchableBillMapper matchableBillMapper;
    @Resource
    private AutoConverterActuator autoConverterActuator;
    @Resource
    private PaasLicenseBusinessService paasLicenseBusinessService;

    public static final List<String> FMCG_TPM_DMS_BLACK_API_NAMES = Lists.newArrayList();

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", conf -> {

            String json = conf.get("fmcg_tpm_dms_black_api_names");
            if (!Strings.isNullOrEmpty(json)) {
                try {
                    FMCG_TPM_DMS_BLACK_API_NAMES.addAll(JSON.parseArray(json, String.class));
                } catch (Exception ex) {
                    log.error("mengniu FMCG_TPM_DMS_BLACK_API_NAMES parse error : ", ex);
                }
            }

        });

    }

    @PostConstruct
    public void init() {
        if (!Objects.equals("1", System.getProperty("mn.dms.flag"))) {
            return;
        }

        log.info("object-data consumer start init.");

        MessageListenerConcurrently listener = (messages, context) -> {

            boolean reconsume = false;
            for (MessageExt message : messages) {
                try {
                    // message.getReconsumeTimes() > 10 就不在处理了
                    if (message.getReconsumeTimes() > MAX_RECONSUME_TIMES) {
                        log.warn("object-data reward process message reconsumeTimes > {}, no longer process, msgId: {}", MAX_RECONSUME_TIMES, message.getMsgId());
                        continue;
                    }
                    this.process(message);
                } catch (AbandonActionException ex) {
                    log.info("dms-object-data consumer abandon : ", ex);
                } catch (ModuleRetryException ex) {
                    log.info("dms-object-data consumer error : ", ex);
                    reconsume = true;
                } catch (Exception ex) {
                    log.error("dms-object-data consumer error : ", ex);
                    reconsume = true;
                } finally {
                    TraceContext.remove();
                }
            }

            if (reconsume) {
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };

        try {
            this.consumer = new AutoConfMQPushConsumer(CONFIG_NAME, listener);
            this.consumer.setGroupNameKey(GROUP_KEY);
            this.consumer.setConsumeTopicKey(TOPIC_KEY);
            this.consumer.setNameServerKey(NAME_SERVER_KEY);
        } catch (Exception ex) {
            log.error("init object-data consumer error : ", ex);
        }
    }

    public void process(MessageExt message) {
        TraceContext trace = TraceContext.get();
        if (StringUtils.isBlank(trace.getTraceId())) {
            trace.setTraceId("DMS_OBJECT_MQ." + message.getMsgId());
        }

        TriggerMessageObj data = JSON.parseObject(new String(message.getBody()), TriggerMessageObj.class);

        if (TPMGrayUtils.enableDMSAutoMatch(data.getTenantId()) && data.getOp().equals(METE_DATA_OP_ACTION_INSERT)) {
            processDMS(message, data);
        }

        if (TPMGrayUtils.enableDMSAutoMatch(data.getTenantId()) && data.getOp().equals(METE_DATA_OP_ACTION_UPDATE)) {
            processDMS(message, data);
        }
    }

    private FinancialBill buildBillFromPurchaseUpdateMessage(String tenantId, JSONObject object) {
        String lifeStatusBefore = "";
        String enterpriseFundAccountBefore = "";
        String lifeStatusAfter = "";
        String enterpriseFundAccountAfter = "";
        if (object.containsKey("beforeTriggerData")) {
            JSONObject beforeTriggerData = object.getJSONObject("beforeTriggerData");
            lifeStatusBefore = beforeTriggerData.containsKey(CommonFields.LIFE_STATUS) ? beforeTriggerData.getString(CommonFields.LIFE_STATUS) : "";
            enterpriseFundAccountBefore = beforeTriggerData.containsKey(PurchaseOrderObjFields.ENTERPRISE_FUND_ACCOUNT) ?
                    beforeTriggerData.getString(PurchaseOrderObjFields.ENTERPRISE_FUND_ACCOUNT) : "";
        }
        if (object.containsKey("afterTriggerData")) {
            JSONObject afterTriggerData = object.getJSONObject("afterTriggerData");
            lifeStatusAfter = afterTriggerData.containsKey(CommonFields.LIFE_STATUS) ? afterTriggerData.getString(CommonFields.LIFE_STATUS) : "";
            enterpriseFundAccountAfter = afterTriggerData.containsKey(PurchaseOrderObjFields.ENTERPRISE_FUND_ACCOUNT) ?
                    afterTriggerData.getString(PurchaseOrderObjFields.ENTERPRISE_FUND_ACCOUNT) : "";
        }

        if (Objects.nonNull(lifeStatusBefore) && "normal".equals(lifeStatusAfter)) {
            return FinancialBill.builder().tenantId(tenantId).apiName(object.getString("entityId")).id(object.getString("objectId")).action(FinancialBill.ACTION_LIFE_STATUS_UPDATE_NORMAL).build();
        }


        if (Objects.nonNull(enterpriseFundAccountAfter) && !Objects.equals(enterpriseFundAccountBefore, enterpriseFundAccountAfter)) {
            return FinancialBill.builder().tenantId(tenantId).apiName(object.getString("entityId")).id(object.getString("objectId"))
                    .action(FinancialBill.ACTION_ENTERPRISE_FUND_ACCOUNT_INFO_UPDATE)
                    .enterpriseFundAccountInfoUpdateBefore(CastUtil.castToEnterpriseFundAccountInfoDTO(enterpriseFundAccountBefore))
                    .enterpriseFundAccountInfoUpdateAfter(CastUtil.castToEnterpriseFundAccountInfoDTO(enterpriseFundAccountAfter)).build();
        }
        return FinancialBill.builder().build();
    }

    private boolean skip(List<JSONObject> bodies) {
        if (CollectionUtils.isEmpty(bodies)) {
            return false; // 空集合不跳过
        }
        boolean skip = true;
        for (JSONObject body : bodies) {
            String apiName = body.getString("entityId");
            if (!FMCG_TPM_DMS_BLACK_API_NAMES.contains(apiName)) {
                return false;
            }
        }
        return skip;
    }

    public void processDMS(MessageExt message, TriggerMessageObj data) {
        if (skip(data.getBody())) {
            log.info("skip processDMS message id:{}", message.getMsgId());
            return;
        }
        if (Boolean.FALSE.equals(paasLicenseBusinessService.checkEnableByModuleCode(data.getTenantId(), "accounts_receivable_app"))) {
            return;
        }

        for (JSONObject object : data.getBody()) {
            FinancialBill bill = FinancialBill.builder().build();
            if (Objects.equals(data.getOp(), METE_DATA_OP_ACTION_INSERT)) {
                bill = FinancialBill.builder().tenantId(data.getTenantId()).apiName(object.getString("entityId")).id(object.getString("objectId")).build();
            } else if (Objects.equals(object.getString("entityId"), ApiNames.PURCHASE_ORDER_OBJ) && Objects.equals(data.getOp(), METE_DATA_OP_ACTION_UPDATE)) {
                bill = buildBillFromPurchaseUpdateMessage(data.getTenantId(), object);
            }
            log.info("bill:{},id:{},apiName:{},op:{}", JSON.toJSONString(bill), object.getString("objectId"), object.getString("entityId"), data.getOp());
            if (Strings.isNullOrEmpty(bill.getApiName()) || Strings.isNullOrEmpty(bill.getId())) {
                continue;
            }

            TraceContext context = TraceContext.get();
            if (!Strings.isNullOrEmpty(context.getTraceId())) {
                context.setTraceId(String.format("%s.%s.%s.%s.%s", context.getTraceId(), data.getTenantId(), bill.getApiName(), bill.getId(), message.getReconsumeTimes()));
            }


            autoConverterActuator.convert(bill);

            //回款单不在此触发自动核销了，挪到了#ApprovalEventConsumer
            if (ApiNames.PAYMENT_OBJ.equals(bill.getApiName())) {
                log.info("payment continue id:{},apiName:{}", bill.getId(), bill.getApiName());
                continue;
            }

            List<FinancialBill> matchableBills = matchableBillMapper.map(bill);
            for (FinancialBill matchableBill : matchableBills) {
                autoMatcher.match(matchableBill);
            }
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (Objects.nonNull(this.consumer) && event.getApplicationContext().getParent() == null) {
            this.consumer.start();
            log.info("object-data consumer started.");
        }
    }

    @PreDestroy
    public void shutDown() {
        if (Objects.nonNull(consumer)) {
            this.consumer.close();
            log.info("object-data consumer closed.");
        }
    }
}