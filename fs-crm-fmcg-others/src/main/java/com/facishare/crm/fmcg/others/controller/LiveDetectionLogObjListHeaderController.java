package com.facishare.crm.fmcg.others.controller;

import com.facishare.crm.fmcg.others.business.ListHeaderBusiness;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

public class LiveDetectionLogObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        ListHeaderBusiness.listHeaderFilter(controllerContext.getTenantId(), arg.getApiName(), result);
        return super.after(arg, result);
    }
}
