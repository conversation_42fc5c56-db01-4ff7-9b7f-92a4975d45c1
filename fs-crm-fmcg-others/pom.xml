<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>fs-crm-fmcg</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>

    <version>9.6.0-SNAPSHOT</version>
    <artifactId>fs-crm-fmcg-others</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-pod-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ehcache</artifactId>
                    <groupId>org.ehcache</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-dubbo-rest-plugin</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>morphia</artifactId>
                    <groupId>org.mongodb.morphia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-result</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-dataformat-yaml</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-id-generator</artifactId>
                    <groupId>com.fxiaoke.api</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-configuration</artifactId>
                    <groupId>commons-configuration</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-codec-http</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jopt-simple</artifactId>
                    <groupId>net.sf.jopt-simple</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-pod-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>i18n-client</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.13</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-gnomon-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-restdriver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-fcp</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jboss-logging</artifactId>
                    <groupId>org.jboss.logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege-temp</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-qixin-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-id-generator</artifactId>
                    <groupId>com.fxiaoke.api</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-id-account-converter</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-union-account-outapi</artifactId>
            <version>1.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-dataformat-yaml</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-id-generator</artifactId>
                    <groupId>com.fxiaoke.api</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>retrofit-spring</artifactId>
                    <groupId>com.github.zhxing</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logging-interceptor</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-text</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-union-core-api</artifactId>
            <version>0.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-spring-support</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ehcache</artifactId>
                    <groupId>org.ehcache</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-pool</artifactId>
                    <groupId>commons-pool</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-qixin-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-result</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-storage</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-utils</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-msg-api</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-api</artifactId>
                    <groupId>com.dyuproject.protostuff</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-proxy-core-api</artifactId>
            <version>0.0.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>resteasy-client</artifactId>
                    <groupId>org.jboss.resteasy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-open-common-result</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>retrofit-spring</artifactId>
                    <groupId>com.github.zhxing</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-wechat-proxy-common</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-wechat-rest-api</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--灰度接入包-->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>gray-release</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-runtime</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.3.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-coordination</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-wechat-dubbo-rest-outer-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-dubbo-rest-plugin</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-coreutils</artifactId>
                    <groupId>com.github.fge</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-pool</artifactId>
                    <groupId>commons-pool</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mysql-connector-java</artifactId>
                    <groupId>mysql</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>byte-buddy</artifactId>
                    <groupId>net.bytebuddy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>classmate</artifactId>
                    <groupId>com.fasterxml</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>