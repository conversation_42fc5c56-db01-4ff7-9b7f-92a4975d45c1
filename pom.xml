<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.facishare</groupId>
    <artifactId>fs-crm-fmcg</artifactId>
    <version>9.6.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>fs-crm-fmcg-tpm</module>
        <module>fs-crm-fmcg-service</module>
        <module>fs-crm-fmcg-integral</module>
        <module>fs-crm-fmcg-others</module>
        <module>fs-crm-fmcg-upgrade</module>
        <module>fs-crm-fmcg-ocr</module>
        <module>fs-crm-fmcg-common</module>
        <module>fs-crm-fmcg-mengniu</module>
        <module>fs-crm-fmcg-yqsl</module>
        <module>fs-crm-fmcg-dms</module>
        <module>fs-crm-fmcg-fesco</module>
        <module>fs-crm-fmcg-tongfu</module>
    </modules>

    <properties>
        <appserver-common-tools.version>1.2-SNAPSHOT</appserver-common-tools.version>
        <appframework.version>9.6.5-SNAPSHOT</appframework.version>
        <fs-metadata.version>9.6.0-SNAPSHOT</fs-metadata.version>
        <checkins-v2-api.version>9.0.5-SNAPSHOT</checkins-v2-api.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-compress.version>1.22</commons-compress.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-logging.version>1.2</commons-logging.version>
        <crm.fmcg.version>9.6.0-SNAPSHOT</crm.fmcg.version>
        <fs-crm-rest-api.version>2.0.12-SNAPSHOT</fs-crm-rest-api.version>
        <fs-enterpriserelation-rest-api2.version>2.1.8-SNAPSHOT</fs-enterpriserelation-rest-api2.version>
        <fs-hosts-record.version>1.0.0-SNAPSHOT</fs-hosts-record.version>
        <fs-job-core.version>1.0.0-SNAPSHOT</fs-job-core.version>
        <fs-message-api.version>2.0.0-SNAPSHOT</fs-message-api.version>
        <fs-metadata-option-api.version>1.0.0-SNAPSHOT</fs-metadata-option-api.version>
        <fs-open-common-result.version>0.0.7</fs-open-common-result.version>
        <fs-open-common-utils.version>1.0.1-SNAPSHOT</fs-open-common-utils.version>
        <fs-paas-bizconf-api.version>2.0.0-SNAPSHOT</fs-paas-bizconf-api.version>
        <fs-paas-gnomon-api.version>1.0.0-SNAPSHOT</fs-paas-gnomon-api.version>
        <fs-qixin-api.version>0.1.1-SNAPSHOT</fs-qixin-api.version>
        <fs-sandbox-api.version>2.0.4-SNAPSHOT</fs-sandbox-api.version>
        <fs-sql2esdsl.version>9.4.0-SNAPSHOT</fs-sql2esdsl.version>
        <fs-rest-es7-support.version>3.6.0-SNAPSHOT</fs-rest-es7-support.version>
        <fs-wechat-proxy-core-api.version>0.0.8-SNAPSHOT</fs-wechat-proxy-core-api.version>
        <fs-wechat-union-core-api.version>0.0.3-SNAPSHOT</fs-wechat-union-core-api.version>
        <global-transaction-http-support.version>1.0.0-SNAPSHOT</global-transaction-http-support.version>
        <groovy-all.version>2.4.11</groovy-all.version>
        <i18n-util.version>1.4-SNAPSHOT</i18n-util.version>
        <java.version>1.8</java.version>
        <javax.ws.rs-api.version>2.1.1</javax.ws.rs-api.version>
        <jdbc-sql-stat.version>1.1.0-SNAPSHOT</jdbc-sql-stat.version>
        <kryo.version>5.3.0</kryo.version>
        <logconfig-core.version>3.0.0-SNAPSHOT</logconfig-core.version>
        <mongo-java-driver.version>3.10.2</mongo-java-driver.version>
        <morphia.version>1.1.1</morphia.version>
        <mybatis-spring.version>2.0.1</mybatis-spring.version>
        <mybatis.version>3.4.5</mybatis.version>
        <netty3.version>3.10.6.Final</netty3.version>
        <okio.version>3.2.0</okio.version>
        <paas-auth.version>2.6.0-SNAPSHOT</paas-auth.version>
        <reflectasm.version>1.11.9</reflectasm.version>
        <retrofit-spring.version>1.3.2-SNAPSHOT</retrofit-spring.version>
        <retrofit-spring2.version>2.0.3-SNAPSHOT</retrofit-spring2.version>
        <rocketmq-spring-support.version>1.0.1-SNAPSHOT</rocketmq-spring-support.version>
        <snakeyaml.version>1.33</snakeyaml.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <war.name>fs</war.name>
        <webApp.contextPath/>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>retrofit-spring2</artifactId>
                <version>${retrofit-spring2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.zhxing</groupId>
                <artifactId>retrofit-spring</artifactId>
                <version>${retrofit-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.paas.transaction</groupId>
                <artifactId>global-transaction-http-support</artifactId>
                <version>${global-transaction-http-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-api</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-metadata-api</artifactId>
                <version>${fs-metadata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-common</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-flow</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-log</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-license</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-fcp</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-coordination</artifactId>
                <version>${appframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-pod-client</artifactId>
                <version>${fs-pod-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>logconfig-core</artifactId>
                <version>${logconfig-core.version}</version>
            </dependency>
            <!--租户级配置-->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-bizconf-api</artifactId>
                <version>${fs-paas-bizconf-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>rocketmq-spring-support</artifactId>
                <version>${rocketmq-spring-support.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-union-core-api</artifactId>
                <version>${fs-wechat-union-core-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-proxy-core-api</artifactId>
                <version>${fs-wechat-proxy-core-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-uc-api</artifactId>
                <version>${fs-uc-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-cache</artifactId>
                <version>${fs-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-metadata-option-api</artifactId>
                <version>${fs-metadata-option-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-sandbox-api</artifactId>
                <version>${sandbox-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-paas-gnomon-api</artifactId>
                <version>${fs-paas-gnomon-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>i18n-client</artifactId>
                <version>${i18n-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>i18n-util</artifactId>
                <version>${i18n-util.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fxiaoke</groupId>
                        <artifactId>i18n-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>${groovy-all.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-api</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-hosts-record</artifactId>
                <version>${fs-hosts-record.version}</version>
            </dependency>
            <!-- 指定 okio 的版本 -->
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <!-- 指定 fs-paas-auth-api，fs-paas-auth-client 的版本 -->
            <dependency>
                <groupId>com.facishare.paas</groupId>
                <artifactId>fs-paas-auth-api</artifactId>
                <version>${paas-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.paas</groupId>
                <artifactId>fs-paas-auth-client</artifactId>
                <version>${paas-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${mongo-java-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb.morphia</groupId>
                <artifactId>morphia</artifactId>
                <version>${morphia.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.mybatis</groupId>-->
            <!--                <artifactId>mybatis</artifactId>-->
            <!--                <version>${mybatis.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>org.mybatis</groupId>-->
            <!--                <artifactId>mybatis-spring</artifactId>-->
            <!--                <version>${mybatis-spring.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.github.colin-lee</groupId>
                <artifactId>mybatis-spring-support</artifactId>
                <version>${mybatis-spring-support.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cglib-nodep</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>objenesis</artifactId>
                        <groupId>org.objenesis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                <version>${fs-enterpriserelation-rest-api2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-result</artifactId>
                <version>${fs-open-common-result.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>jdbc-sql-stat</artifactId>
                <version>${jdbc-sql-stat.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-fsi-proxy</artifactId>
                <version>${fs-fsi-proxy.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spotbugs-annotations</artifactId>
                        <groupId>com.github.spotbugs</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.1-jre</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
