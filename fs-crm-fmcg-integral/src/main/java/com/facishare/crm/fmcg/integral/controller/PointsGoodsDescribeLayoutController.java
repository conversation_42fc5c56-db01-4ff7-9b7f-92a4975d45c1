package com.facishare.crm.fmcg.integral.controller;

import com.facishare.crm.fmcg.common.apiname.PointsGoodsFields;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public class PointsGoodsDescribeLayoutController extends AbstractStandardDescribeLayoutController<PointsGoodsDescribeLayoutController.Arg> {
    private boolean isPhysical = false;

    @Override
    protected void before(PointsGoodsDescribeLayoutController.Arg arg) {
        super.before(arg);
        initData();
    }

    private void initData() {
        String recordTypeApiName = arg.getRecordType_apiName();
        log.info("PointsGoodsObjDescribe recordTypeApiName is : {}", recordTypeApiName);
        isPhysical = "physical_type__c".equals(recordTypeApiName);
    }

    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {
        if (isPhysical) {
            setRequiredField(result, Lists.newArrayList(PointsGoodsFields.STOCK, PointsGoodsFields.CONSUMPTION_POINTS));
        }
        return super.after(arg, result);

    }

    private void setRequiredField(StandardDescribeLayoutController.Result result, List<String> requiredFields) {
        try {
            log.info("set read only field is {}", requiredFields);
            LayoutExt layout = LayoutExt.of(result.getLayout().toLayout());
            FormComponentExt form = layout.getFormComponent().orElse(null);
            if (!Objects.isNull(form)) {
                List<IFieldSection> sections = form.getFieldSections();
                for (IFieldSection section : sections) {
                    section.getFields().forEach(field -> {
                        if (requiredFields.contains(field.getFieldName())) {
                            field.setRequired(false);
                        }
                    });
                }
            }

        } catch (Exception ex) {
            log.info("override PointsGoodsObj layout cause unknown exception  : ", ex);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    public static class Arg extends StandardDescribeLayoutController.Arg implements Serializable {
    }
}
