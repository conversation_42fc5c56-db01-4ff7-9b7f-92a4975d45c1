package com.facishare.crm.fmcg.integral.action;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.PointsExchangeRecordFields;
import com.facishare.crm.fmcg.integral.ApiNames;
import com.facishare.crm.fmcg.integral.i18n.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPhysicalRewardService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;


public class PointsExchangeRecordGoodsDeliveryAction extends AbstractStandardAction<PointsExchangeRecordGoodsDeliveryAction.Arg, PointsExchangeRecordGoodsDeliveryAction.Result> {

    private IObjectData objectData;

    private final IPhysicalRewardService physicalRewardService = SpringUtil.getContext().getBean(IPhysicalRewardService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("GoodsDelivery");
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected void before(Arg arg) {
        log.info(String.format("arg is %s", JSONObject.toJSONString(arg)));
        super.before(arg);
        validatePhysicalRewardGoodsDelivery(arg);
        String orderType = objectData.get(PointsExchangeRecordFields.ORDER_TYPE, String.class);
        if (!PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER.equals(orderType)) {
            String productId = objectData.get("product_id", String.class);

            List<IObjectData> products = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), Lists.newArrayList(productId), ApiNames.POINTS_GOODS_OBJ.getApiName());
            if (CollectionUtils.isEmpty(products)) {
                throw new ValidateException(I18N.text(I18NKeys.POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_0));
            }

            IObjectData product = products.get(0);

            Double availableStock = product.get("stock", Double.class);
            Double convertedQuantity = product.get("converted_quantity", Double.class);

            if (availableStock == null) {
                throw new ValidateException(I18N.text(I18NKeys.POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_1));
            }

            double flag = availableStock - (convertedQuantity == null ? 0D : convertedQuantity);
            if (flag <= 0) {
                throw new ValidateException(I18N.text(I18NKeys.POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_2));
            }
        }

    }

    private void validatePhysicalRewardGoodsDelivery(Arg arg) {

        String orderType = objectData.get(PointsExchangeRecordFields.ORDER_TYPE, String.class);
        String pickUpMethod = objectData.get(PointsExchangeRecordFields.PICKUP_METHOD, String.class);
        if (PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER.equals(orderType)
                && !PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickUpMethod)){
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_0));
        }

        //发货单据类型 = 活动实物订单 ，且 领取方式 = 邮寄到家 时
        if (PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER.equals(orderType)
                && PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickUpMethod)) {
            //判断web端请求 WEB.chrome
            String clientInfo = actionContext.getRequestContext().getClientInfo();
            if (!clientInfo.startsWith("WEB")) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_1));
            }
            String deliveryCompany = arg.getDeliveryCompany();
            String expressDeliveryNumber = arg.getExpressionsDeliveryNumber();
            if (deliveryCompany == null) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_2));
            }
            if (expressDeliveryNumber == null) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_POINTS_EXCHANGE_RECORD_GOODS_DELIVERY_ACTION_3));
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Map<String, Object> updateMap = Maps.newHashMap();
        buildDeliveryMap(arg, updateMap);
        updateMap.put("order_state", "delivered");
        IObjectData data = serviceFacade.updateWithMap(actionContext.getUser(), objectData, updateMap);
        return Result.of(data);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        // 实物激励发货成功后，执行返回值处理
        physicalRewardReturnValue(after);
        return after;
    }

    private void physicalRewardReturnValue(Result after) {
        IObjectData record = serviceFacade.findObjectData(actionContext.getUser(), after.getObjectData().getId(), ApiNames.POINTS_EXCHANGE_RECORD_OBJ.getApiName());
        String orderType = record.get(PointsExchangeRecordFields.ORDER_TYPE, String.class);
        String pickUpMethod = record.get(PointsExchangeRecordFields.PICKUP_METHOD, String.class);
        String orderStatus = record.get(PointsExchangeRecordFields.ORDER_STATE, String.class);
        //发货单据类型 = 活动实物订单 ，且 领取方式 = 邮寄到家 且 发货状态 = 已发货
        if (PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER.equals(orderType)
                && PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickUpMethod)
                && PointsExchangeRecordFields.OrderState.DELIVERED.equals(orderStatus)) {
            try {
                physicalRewardService.returnValueForPhysicalItem(actionContext.getTenantId(), record);
            } catch (ValidateException exception) {
                log.error("physicalRewardReturnValue error :", exception);
            }
        }
    }


    private void buildDeliveryMap(Arg arg, Map<String, Object> updateMap) {
        String orderType = objectData.get(PointsExchangeRecordFields.ORDER_TYPE, String.class);
        String pickUpMethod = objectData.get(PointsExchangeRecordFields.PICKUP_METHOD, String.class);
        //发货单据类型 = 活动实物订单 ，且 领取方式 = 邮寄到家 时
        if (PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER.equals(orderType)
                && PointsExchangeRecordFields.PickUpMethod.MAIL.equals(pickUpMethod)) {
            String deliveryCompany = arg.getDeliveryCompany();
            String expressDeliveryNumber = arg.getExpressionsDeliveryNumber();
            if (deliveryCompany != null) {
                updateMap.put("delivery_company", deliveryCompany);
            }
            if (expressDeliveryNumber != null) {
                updateMap.put("express_delivery_number", expressDeliveryNumber);
            }
            updateMap.put("distribution_time", System.currentTimeMillis());
        }
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return "GoodsDelivery_button_default";
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.isNotEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        @SerializedName("delivery_company")
        @JSONField(name = "delivery_company")
        @JsonProperty("delivery_company")
        private String deliveryCompany;

        @SerializedName("express_delivery_number")
        @JSONField(name = "express_delivery_number")
        @JsonProperty("express_delivery_number")
        private String expressionsDeliveryNumber;
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
