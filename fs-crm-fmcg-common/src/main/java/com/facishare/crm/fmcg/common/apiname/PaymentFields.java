package com.facishare.crm.fmcg.common.apiname;

public abstract class PaymentFields {

    private PaymentFields() {
    }

    public static final String SALES_ORDER_ID = "salesorder_id";
    public static final String ACCOUNT_ID = "account_id";
    public static final String AMOUNT = "amount";
    public static final String PAYMENT_AMOUNT = "payment_amount";
    public static final String PAYMENT_TIME = "payment_time";
    public static final String PURPOSE = "purpose";
    public static final String PURPOSE__SALES = "1";
    public static final String PAYMENT_TERM = "payment_term";
    public static final String PAYMENT_TERM__CASH = "2";
    public static final String COLLECTION_TYPE = "collection_type";
    public static final String COLLECTION_TYPE__RED = "Red";
    public static final String COLLECTION_TYPE__BLUE = "Blue";
    public static final String PAY_TYPE = "pay_type";
    public static final String PAY_TYPE__OFF_LINE = "1";
    public static final String CONTACT_OBJECT = "contact_object";
    public static final String CONTACT_OBJECT__ACCOUNT = "AccountObj";
    public static final String MATCH_STATUS = "match_status";
    public static final String MATCH_STATUS__NO = "no";
    public static final String ENTER_INTO_ACCOUNT = "enter_into_account";
    public static final String ENTERPRISE_FUND_ACCOUNT_ID = "enterprise_fund_account_id";
    public static final String OPENING_BALANCE = "opening_balance";
    public static final String REMARKS = "remarks";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String RETURNED_GOODS_INVOICE_ID = "returned_goods_invoice_id";

}