package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.metadata.api.IMultiLevelSelectOption;
import com.facishare.paas.metadata.impl.describe.MultiLevelSelectOneFieldDescribe;

public class MultiLevelSelectOneFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        String fieldValue = String.valueOf(context.getFieldValue());
        MultiLevelSelectOneFieldDescribe mutilSelectDescribe = (MultiLevelSelectOneFieldDescribe) context.getFieldDescribe();
        for (IMultiLevelSelectOption parentSelection : mutilSelectDescribe.getSelectOptions()) {
            for (IMultiLevelSelectOption childOptions : parentSelection.getChildOptions()) {
                if (childOptions.getValue().equals(fieldValue)) {
                    String childLabel = childOptions.getLabel();
                    String parentLabel = parentSelection.getLabel();
                    return parentLabel + "/" + childLabel;
                }
            }
        }
        return "";
    }
}