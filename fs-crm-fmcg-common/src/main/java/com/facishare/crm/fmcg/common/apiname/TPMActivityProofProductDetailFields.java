package com.facishare.crm.fmcg.common.apiname;

import java.util.List;

import com.google.common.collect.Lists;

public abstract class TPMActivityProofProductDetailFields {

    private TPMActivityProofProductDetailFields() {
    }

    public static final String AI_NUMBER = "ai_number";

    public static final String NUMBER_STANDARD = "number_standard";

    public static final String PRODUCT_ID = "product_id";

    public static final String ACTIVITY_ITEM_ID = "activity_item_id";

    public static final String ACTIVITY_PROOF_DISPLAY_IMG_ID = "activity_proof_display_img_id";

    public static final String ACTIVITY_PROOF_ID = "activity_proof_id";

    public static final String PRODUCT_CATEGORY_ID = "product_category_id";

    public static final String DISPLAY_FORM_ID = "display_form_id";

    public static final String LAYER = "layer";

    public static final String MANUAL_CORRECTION_NUMBER = "manual_correction_number";

    public static final String PRODUCT_PACKAGE_WIDTH_METE = "product_package_width_mete__c";

    public static final List<String> ALL = Lists.newArrayList(
            AI_NUMBER,
            NUMBER_STANDARD,
            PRODUCT_ID,
            ACTIVITY_ITEM_ID,
            ACTIVITY_PROOF_DISPLAY_IMG_ID,
            ACTIVITY_PROOF_ID,
            PRODUCT_CATEGORY_ID,
            DISPLAY_FORM_ID
    );
}