package com.facishare.crm.fmcg.common.adapter;

import com.facishare.crm.fmcg.common.adapter.abstraction.IReceiveMoneyService;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.CloseWXOrder;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.FormWxPayMini;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryEnterpriseUnionISV;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryTransferDetailForReceipts;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.EnterprisePayOrderFields;
import com.facishare.crm.fmcg.common.apiname.PayChannelFields;
import com.facishare.crm.fmcg.common.constant.PayConstants;
import com.facishare.crm.fmcg.common.i18n.I18NKeys;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.pay.api.arg.CloseOrderModel;
import com.facishare.crm.pay.api.arg.UnifiedOrderModel;
import com.facishare.crm.pay.rest.api.common.HeaderObj;
import com.facishare.crm.pay.rest.api.common.RestResult;
import com.facishare.crm.pay.rest.api.service.EnteprisePayService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/12 19:36
 */
@Service
public class ReceiveMoneyService implements IReceiveMoneyService {

    @Autowired
    private EnteprisePayService eaPayService;


    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public FormWxPayMini.Result formWxPayMini(FormWxPayMini.Arg arg) {

        UnifiedOrderModel.Arg unifiedOrderModel = new UnifiedOrderModel.Arg();

        unifiedOrderModel.setAmount(arg.getAmount().multiply(new BigDecimal("100")).longValue());
        unifiedOrderModel.setRemark(arg.getDescribeContent());
        unifiedOrderModel.setOrderName(arg.getDescribeTitle());
        unifiedOrderModel.setMerchantOrderNo(arg.getBusinessId());
        unifiedOrderModel.setPayUsername(arg.getPayer().getDisplayName());
        unifiedOrderModel.setAppId(arg.getPayer().getWxAppId());
        unifiedOrderModel.setOpenId(arg.getPayer().getWxOpenId());
        unifiedOrderModel.setToEa(arg.getReceiver().getReceiverTenantAccount());
        unifiedOrderModel.setToUserId(arg.getReceiver().getReceiverUserId());
        unifiedOrderModel.setResource(PayConstants.PAY_RESOURCE);
        unifiedOrderModel.setAgentType("wechatMini");


        RestResult<UnifiedOrderModel.Result<UnifiedOrderModel.WechatMiniResult>> payResult = eaPayService.unifiedOrderForWechatMini(HeaderObj.newInstance(arg.getReceiver().getReceiverTenantId()), unifiedOrderModel);

        FormWxPayMini.Result result = new FormWxPayMini.Result();
        if (payResult.isSuccess()) {
            UnifiedOrderModel.WechatMiniResult wechatMiniResult =payResult.getResult().getPayData() ;
            result.setSign(wechatMiniResult.getPaySign());
            result.setPackages(wechatMiniResult.getPackageVal());
            result.setBusinessId(arg.getBusinessId());
            result.setTransferId(payResult.getResult().getOrderId());
            result.setSignType(wechatMiniResult.getSignType());
            result.setAppId(wechatMiniResult.getAppId());
            result.setNonceStr(wechatMiniResult.getNonceStr());
            result.setTimeStamp(Long.parseLong(wechatMiniResult.getTimestamp()));
            result.setPartnerId(wechatMiniResult.getPartnerId());
        } else {
            throw new RewardFmcgException(String.valueOf(payResult.getErrCode()), "生成小程序微信支付信息失败：" + payResult.getErrMessage());
        }
        return result;
    }

    @Override
    public QueryTransferDetailForReceipts.Result queryTransferDetailForReceipts(QueryTransferDetailForReceipts.Arg arg) {

        QueryTransferDetailForReceipts.Result result = new QueryTransferDetailForReceipts.Result();

        result.setTransferDetail(getTransferDetail(arg.getTenantId(), arg.getBusinessId()));
        return result;
    }

    private TransferDetail getTransferDetail(String tenantId, String businessId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
            SearchQueryUtil.filter(EnterprisePayOrderFields.MERCHANT_ORDER_NO, Operator.EQ, Lists.newArrayList(businessId))
        ));
        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_PAY_ORDER_OBJ, query, Lists.newArrayList(CommonFields.ID, EnterprisePayOrderFields.ORDER_STATUS, EnterprisePayOrderFields.MERCHANT_ORDER_NO, EnterprisePayOrderFields.AMOUNT));
        if (CollectionUtils.isNotEmpty(data)) {
            IObjectData detail = data.get(0);
            TransferDetail transferDetail = new TransferDetail();
            transferDetail.setBusinessId(businessId);
            transferDetail.setStatus(detail.get(EnterprisePayOrderFields.ORDER_STATUS, String.class));
            transferDetail.setPay(String.valueOf(detail.get(EnterprisePayOrderFields.AMOUNT, BigDecimal.class)));
            return transferDetail;
        } else {
            throw new RewardFmcgException("10001", I18N.text(I18NKeys.REWARD2_RECEIVE_MONEY_SERVICE_0));
        }
    }

    @Override
    public CloseWXOrder.Result closeWXOrder(CloseWXOrder.Arg arg) {
        //todo:close
        CloseOrderModel.Arg unionPayCloseOrderArg = new CloseOrderModel.Arg();
        unionPayCloseOrderArg.setMerchantOrderNo(arg.getBusinessId());
        CloseWXOrder.Result result = new CloseWXOrder.Result();

        RestResult<Boolean> closeResult = eaPayService.closeOrder(HeaderObj.newInstance(Integer.valueOf(arg.getTenantId())), unionPayCloseOrderArg);
        if (closeResult.isSuccess() && Boolean.TRUE.equals(closeResult.getResult())) {
            result.setTransferDetail(getTransferDetail(arg.getTenantId(), arg.getBusinessId()));
        } else {
            throw new RewardFmcgException(String.valueOf(closeResult.getErrCode()), "关闭收款明细失败:" + closeResult.getErrMessage());
        }
        return result;
    }

    @Override
    public QueryEnterpriseUnionISV.Result queryEnterpriseUnionISV(QueryEnterpriseUnionISV.Arg arg) {
        QueryEnterpriseUnionISV.Result finalResult = new QueryEnterpriseUnionISV.Result();
        finalResult.setIsvList(Lists.newArrayList());

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
            SearchQueryUtil.filter(PayChannelFields.IS_OPEN_USE, Operator.EQ, Lists.newArrayList("true")),
            SearchQueryUtil.filter(CommonFields.RECORD_TYPE, Operator.EQ, Lists.newArrayList("chinaums__c"))
        ));
        List<IObjectData> data = null;
        try {
            data = QueryDataUtil.find(serviceFacade, arg.getTenantId(), ApiNames.PAY_CHANNEL_OBJ, query, Lists.newArrayList(CommonFields.ID, PayChannelFields.TID, PayChannelFields.MID, PayChannelFields.PARENT_TID, PayChannelFields.PARENT_MID));
        } catch (ObjectDefNotFoundError e) {
            return finalResult;
        }

        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(isv -> {
                QueryEnterpriseUnionISV.EnterpriseUnionISV enterpriseUnionISV = new QueryEnterpriseUnionISV.EnterpriseUnionISV();
                enterpriseUnionISV.setTid(isv.get(PayChannelFields.TID, String.class));
                enterpriseUnionISV.setMid(isv.get(PayChannelFields.MID, String.class));
                enterpriseUnionISV.setParentMid(isv.get(PayChannelFields.PARENT_MID, String.class));
                enterpriseUnionISV.setParentTid(isv.get(PayChannelFields.PARENT_TID, String.class));
                finalResult.getIsvList().add(enterpriseUnionISV);
            });
        }

        return finalResult;
    }


}
