package com.facishare.crm.fmcg.common.utils;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
public class SecretAESUtil {

    public static final String AES = "AES";
    public static final String AES_TRANSFORMATION = "AES/ECB/PKCS5Padding";


    public static String secretEncode(String content, String key) {
        if (Strings.isNullOrEmpty(content)) {
            return content;
        } else {
            try {
                Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
                SecretKey secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), AES);
                cipher.init(1, secretKey);
                return Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes(StandardCharsets.UTF_8)));
            } catch (Exception e) {
                log.info("secretEncode fail ", e);
            }
        }
        return null;
    }

    public static String secretDecode(String content, String key) {
        if (Strings.isNullOrEmpty(content)) {
            return content;
        } else {
            try {
                Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
                SecretKey secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), AES);
                cipher.init(2, secretKey);
                return new String(cipher.doFinal(Base64.getDecoder().decode(content.getBytes(StandardCharsets.UTF_8))), Charset.defaultCharset());
            } catch (Exception e) {
                log.info("secretDecode fail ", e);
            }
        }
        return null;
    }
}
