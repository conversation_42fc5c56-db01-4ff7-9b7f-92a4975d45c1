package com.facishare.crm.fmcg.common.converter;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class FieldValueConvertContext {
    private User user;
    private IFieldDescribe fieldDescribe;
    private List<Object> fieldValues;

    public Object getFieldValue(){
        return fieldValues.get(0);
    }
}
