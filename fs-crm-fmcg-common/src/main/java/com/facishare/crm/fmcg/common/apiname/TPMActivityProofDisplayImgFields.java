package com.facishare.crm.fmcg.common.apiname;

import com.beust.jcommander.internal.Lists;

import java.util.List;

public abstract class TPMActivityProofDisplayImgFields {

    private TPMActivityProofDisplayImgFields() {
    }

    public static final String IMAGE = "image";

    public static final String SYSTEM_JUDGMENT_STATUS = "system_judgment_status";

    public static final String ACTUAL_DISPLAY_POSITION = "actual_display_position";

    public static final String ACTIVITY_ITEM_ID = "activity_item_id";

    public static final String AUDIT_STATUS = "audit_status";

    public static final String STANDARD_DISPLAY_POSITION = "standard_display_position";

    public static final String ACTIVITY_PROOF_ID = "activity_proof_id";

    public static final String DISPLAY_FORM_ID = "display_form_id";

    public static final String AI_IDENTIFY_STATUS = "ai_identify_status";

    public static final String AI_IDENTIFY_STATUS_IDENTIFYING = "identifying";
    public static final String AI_IDENTIFY_STATUS_IDENTIFIED = "identified";
    public static final String AI_IDENTIFY_STATUS_IDENTIFY_FAILED = "identify_failed";

    public static final String AI_DISPLAY_FORM_ID = "ai_display_form_id";

    public static final String AI_LAYER_NUMBER = "ai_layer_number";

    public static final String AI_GROUP_NUMBER = "ai_group_number";

    public static final String AI_VISIBLE_NUMBER = "ai_visible_number";

    public static final String PILE_HEAD_COUNT = "__pile_head_count";

    public static final String COUNT_NUMBER = "__count_number";

    //单选
    public static final String PASS_STATUS = "pass";
    public static final String FAIL_STATUS = "fail";
    public static final String PARTIAL_PASS_STATUS = "partial_pass";
    public static final String NOT_DISPLAY_SYSTEM_JUDGMENT_STATUS = "not_display";
    public static final String PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS = "pending_approval";

    public static final String NOT_AUDIT = "not_audit";


    public static final List<String> ALL = Lists.newArrayList(
            IMAGE,
            SYSTEM_JUDGMENT_STATUS,
            ACTUAL_DISPLAY_POSITION,
            ACTIVITY_ITEM_ID,
            AUDIT_STATUS,
            STANDARD_DISPLAY_POSITION,
            ACTIVITY_PROOF_ID,
            DISPLAY_FORM_ID
    );

}