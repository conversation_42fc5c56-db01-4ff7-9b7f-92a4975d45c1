package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

public class RecordTypeFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        List<IRecordTypeOption> options = ((RecordTypeFieldDescribe) context.getFieldDescribe()).getRecordTypeOptions();
        String oldData = String.valueOf(context.getFieldValue());
        if (CollectionUtils.isEmpty(options) || Strings.isNullOrEmpty(oldData)) {
            return "";
        }
        Map<String, String> optionMap = Maps.newHashMap();
        for (IRecordTypeOption k : options) {
            optionMap.put(k.getApiName(), k.getLabel());
        }

        List<String> labelList = Lists.newArrayList();
        for (Object k : context.getFieldValues()) {
            if (k != null && StringUtils.isNotBlank(k.toString()) && optionMap.containsKey(k.toString())) {
                labelList.add(optionMap.get(k.toString()));
            }
        }
        if (!CollectionUtils.isEmpty(labelList)) {
            return Joiner.on(",").join(labelList);
        }
        return "";
    }
}