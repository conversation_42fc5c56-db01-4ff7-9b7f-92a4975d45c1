package com.facishare.crm.fmcg.common.apiname;

public abstract class TPMActivityRewardDetailFields {

    private TPMActivityRewardDetailFields() {
    }

    public static final String REWARD_TIME = "reward_time";

    public static final String REWARD_VALUE = "reward_value";

    public static final String REWARD_TYPE = "reward_type";

    public static final String PRODUCT_ID = "product_id";

    public static final String ACTIVITY_ID = "activity_id";

    public static final String REWARDED_PERSON = "rewarded_person";

    public static final String REWARD_PERSON_ID = "reward_person_id";

    public static final String RELATED_OBJECT_API_NAME = "related_object_api_name";

    public static final String SERIAL_NUMBER_ID = "serial_number_id";

    public static final String BUSINESS_ID = "business_id";

    public static final String REWARD_PART = "reward_part";

    public static final String RELATED_OBJECT_DATA_ID = "related_object_data_id";

    public static final String RELATED_OBJECT_DATA_TENANT_ID = "related_object_data_tenant_id";

    public static final String WX_UNION_ID = "wx_union_id";

    public static final String STATUS = "status";

    public static final String REWARD_PART_CODE = "reward_part_code";

    public static final String DISTRIBUTION_INSTRUCTIONS = "distribution_instructions";

    public static final String PRIZE_NAME = "prize_name";

    public static final String REWARD_DETAIL_ID = "reward_detail_id";

    public static final String ACCOUNT_ID__C = "account_id__c";
    public static final String ACCOUNT_ID = "account_id";


    public static final class RewardType {
        public static final String RED_PACKET = "redPacket";

        public static final String DISCOUNT_PAY = "discount";

        public static final String PHYSICAL_ITEM = "physicalItem";

        public static final String NONE = "none";

        public static final String REBATE = "rebate";
    }

    public static final class Status {
        public static final String UNDO = "0";

        public static final String DONE = "1";

        public static final String ERROR = "2";

        public static final String NO_NEED = "3";
    }
}