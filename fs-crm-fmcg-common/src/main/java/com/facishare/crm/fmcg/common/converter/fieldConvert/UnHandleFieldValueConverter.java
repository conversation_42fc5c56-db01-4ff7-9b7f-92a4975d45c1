package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.base.Joiner;

public class UnHandleFieldValueConverter implements AbstractFieldValueConverter {
    public static final UnHandleFieldValueConverter instance = new UnHandleFieldValueConverter();

    @Override
    public String convert(FieldValueConvertContext context) {
        if (CollectionUtils.empty(context.getFieldValues())) {
            return "";
        }
        return Joiner.on(",").join(context.getFieldValues());
    }

    public static UnHandleFieldValueConverter instance() {
        return instance;
    }
}