package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;

import java.util.List;
import java.util.stream.Collectors;

public class DepartmentFieldValueConverter implements AbstractFieldValueConverter {
    private final OrgService orgService = SpringUtil.getContext().getBean(OrgService.class);

    @Override
    public String convert(FieldValueConvertContext context) {
        List<String> fieldValues = context.getFieldValues().stream().map(o -> (String) o).collect(Collectors.toList());
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = orgService.getDeptInfoNameByIds(context.getUser().getTenantId()
                , context.getUser().getUpstreamOwnerIdOrUserId(), fieldValues);
        if (CollectionUtils.empty(deptInfos)) {
            return "";
        }
        return Joiner.on(",").join(deptInfos.stream().map(o -> o.getDeptName()).collect(Collectors.toList()));
    }
}