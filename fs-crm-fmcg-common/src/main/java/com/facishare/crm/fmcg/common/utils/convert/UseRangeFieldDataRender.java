package com.facishare.crm.fmcg.common.utils.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.crm.fmcg.common.converter.FieldValueConvertFactory;
import com.facishare.crm.fmcg.common.converter.fieldConvert.AbstractFieldValueConverter;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.DateFieldDescribe;
import com.facishare.paas.metadata.impl.describe.DateTimeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.TimeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.UseRangeFieldDescribe;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class UseRangeFieldDataRender {

    @Resource
    private ServiceFacade serviceFacade;

    public static final List<String> specialValueTypeList = Lists.newArrayList("day","week","month");

    //时间 年度  本年度等等
    public static final String SFA_TIME_TITLE_VALUE = "sfa.time.title.value.";


    public String render(Object rangeData, String targetApiName) {
        if (rangeData == null) {
            return "";
        }
        User user = new User(RequestContextManager.getContext().getTenantId(), User.SUPPER_ADMIN_USER_ID);
        UseRangeInfo useRangeInfo = JSON.parseObject(rangeData.toString(), UseRangeInfo.class);
        if (UseRangeType.CONDITION.toString().equals(useRangeInfo.getType())) {
            IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), targetApiName);
            useRangeInfo.setValue(resolveConditionByI18N(describe, useRangeInfo.getValue(), user));
        }
        return JSON.toJSONString(useRangeInfo);
    }

    public String resolveConditionByI18N(IObjectDescribe describe, String useRangeValue, User user) {
        List<FilterGroup> filterGroups = JSON.parseArray(useRangeValue, FilterGroup.class);
        filterGroups.forEach(filterGroup -> {
            Optional.ofNullable(filterGroup.getFilters()).ifPresent(data -> {
                data.forEach(x -> {
                    JSONObject jsonFilter = (JSONObject) x;
                    IFilter filter = JSONObject.parseObject(jsonFilter.toJSONString(), IFilter.class);

                    IFieldDescribe fieldDescribe = describe.getFieldDescribe(filter.getFieldName());
                    jsonFilter.put("field_name__s", fieldDescribe == null ? "--" : fieldDescribe.getLabel());
                    String fieldValueS = fieldDescribe == null ? "--" : convertFieldValue(filter.getFieldValues(), fieldDescribe, user);
                    boolean isDealOperatorNameFlag = true;
                    if (fieldDescribe instanceof DateFieldDescribe
                            || fieldDescribe instanceof DateTimeFieldDescribe
                            || fieldDescribe instanceof TimeFieldDescribe) {
                        String filterValueType = String.valueOf(filter.getValueType());
                        if ("3".equals(filterValueType) && CollectionUtils.notEmpty(filter.getFieldValues())) {
                            String typeValue = filter.getFieldValues().get(0);
                            switch (typeValue) {
                                case "6":
                                case "7":
                                case "10":
                                case "13":
                                case "14":
                                case "15":
                                case "4":
                                case "5":
                                case "8":
                                case "2":
                                case "3":
                                case "9":
                                case "11":
                                case "1":
                                case "12":
                                    fieldValueS = I18N.text(SFA_TIME_TITLE_VALUE + typeValue);
                                    break;
                                case "day":
                                    fieldValueS = filter.getFieldValues().get(1);
                                    break;
                                case "week":
                                    fieldValueS = filter.getFieldValues().get(1);
                                    break;
                                case "month":
                                    fieldValueS = filter.getFieldValues().get(1);
                                    break;
                            }
                            if(filter.getFieldValues().size()==2 && specialValueTypeList.contains(typeValue)){
                                isDealOperatorNameFlag = false;
                            }
                        }
                    }
                    jsonFilter.put("field_values__s", fieldValueS);
                    if(isDealOperatorNameFlag){
                        jsonFilter.put("operator_name", getOperatorName(Optional.of(filter).map(IFilter::getOperator).map(Enum::name).orElse("")));
                    }
                });
            });
        });
        return JSON.toJSONString(filterGroups);
    }

    private String convertFieldValue(List<String> fieldValues, IFieldDescribe fieldDescribe, User user) {
        if (CollectionUtils.empty(fieldValues)) {
            return "";
        }
        AbstractFieldValueConverter fieldValueConverter = FieldValueConvertFactory.getConverter(fieldDescribe);
        return fieldValueConverter.convert(
                FieldValueConvertContext.builder()
                        .user(user)
                        .fieldDescribe(fieldDescribe)
                        .fieldValues((List<Object>) (List) fieldValues)
                        .build());
    }

    /**
     * 处理user_range修改记录
     */
    public void userRangeLogInfoHandle(List<LogInfo.DiffObjectData> objectDataList, Map<String, IFieldDescribe> useRangeFieldMap) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        objectDataList.forEach(o -> {
            String fieldApiName = o.getFieldApiName();
            if (useRangeFieldMap.containsKey(fieldApiName)) {
                try {
                    o.getValue().put(fieldApiName, render(o.getValue().get(fieldApiName)
                            , ((UseRangeFieldDescribe) useRangeFieldMap.get(fieldApiName)).getTargetApiName()));
                } catch (Exception e) {
                    log.error("json parse error", e);
                }
                try {
                    o.getOldValue().put(fieldApiName, render(o.getOldValue().get(fieldApiName)
                            , ((UseRangeFieldDescribe) useRangeFieldMap.get(fieldApiName)).getTargetApiName()));
                } catch (Exception e) {
                    log.error("json parse error", e);
                }
            }
        });
    }

    /**
     * 处理user_range修改记录
     */
    public void userRangeSnapShotHandle(String tenantId, String objectApiName, Map<String, Object> objData) {
        if (CollectionUtils.empty(objData)) {
            return;
        }
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectApiName);
        Map<String, IFieldDescribe> useRangeFieldMap = describe.getFieldDescribes().stream().filter(k -> IFieldType.UseRange.equals(k.getType())).collect(Collectors.toMap(IFieldDescribe::getApiName, o -> o));
        for (Map.Entry<String, Object> entry : objData.entrySet()) {
            String fieldApiName = entry.getKey();
            if (useRangeFieldMap.containsKey(fieldApiName)) {
                try {
                    entry.setValue(render(entry.getValue()
                            , ((UseRangeFieldDescribe) useRangeFieldMap.get(fieldApiName)).getTargetApiName()));
                } catch (Exception e) {
                    log.error("json parse error", e);
                }
            }
        }
    }

    public enum UseRangeType {
        ALL,
        CONDITION,
        FIXED,
        ORG,
        PRICE_BOOK
    }

    @Data
    public static class UseRangeInfo {
        private String code;
        private String type;
        private String value;
    }

    @Data
    public static class FilterGroup {
        private String connector;
        private JSONArray filters;
    }

    public String getOperatorName(String operator) {
        switch (operator) {
            case "EQ":
                return I18N.text("sfa.filter.operator.equal");
            case "GT":
                return I18N.text("sfa.filter.operator.gt");
            case "LT":
                return I18N.text("sfa.filter.operator.lt");
            case "GTE":
                return I18N.text("sfa.filter.operator.gte");
            case "LTE":
                return I18N.text("sfa.filter.operator.lte");
            case "N":
            case "NEQ":
                return I18N.text("sfa.filter.operator.neq");
            case "LIKE":
                return I18N.text("sfa.filter.operator.like");
            case "IN":
            case "HASANYOF":
                return I18N.text("sfa.filter.operator.hasanyof");
            case "NLIKE":
                return I18N.text("sfa.filter.operator.nlike");
            case "NIN":
            case "NHASANYOF":
                return I18N.text("sfa.filter.operator.nhasanyof");
            case "IS":
                return I18N.text("sfa.filter.operator.isnull");
            case "ISN":
                return I18N.text("sfa.filter.operator.isnotnull");
            case "BETWEEN":
                return I18N.text("sfa.filter.operator.between");
            case "STARTWITH":
                return I18N.text("sfa.filter.operator.startwith");
            case "ENDWITH":
                return I18N.text("sfa.filter.operator.endwith");
            default:
                return operator;
        }
    }

}
