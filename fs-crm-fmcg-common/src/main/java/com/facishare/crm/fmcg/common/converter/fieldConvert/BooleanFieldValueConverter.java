package com.facishare.crm.fmcg.common.converter.fieldConvert;


import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.metadata.impl.describe.BooleanFieldDescribe;

import java.util.List;
import java.util.Map;

public class BooleanFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        String fieldValue = String.valueOf(context.getFieldValue());
        BooleanFieldDescribe booleanFieldDescribe = (BooleanFieldDescribe) context.getFieldDescribe();
        List<Map> optionsList = booleanFieldDescribe.get("options", List.class);
        if (optionsList != null) {
            for (Map option : optionsList) {
                if (fieldValue.equals(String.valueOf(option.get("value")))) {
                    return String.valueOf(option.get("label"));
                }
            }
        }

        return "";
    }
}
