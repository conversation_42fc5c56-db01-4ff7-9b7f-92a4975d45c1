package com.facishare.crm.fmcg.common.apiname;

/**
 * <AUTHOR>
 * @date 2023/7/3 16:50
 */
public abstract class PricePolicyFields {
    private PricePolicyFields() {
    }

    public static final String NAME = "name";
    public static final String END_DATE = "end_date";
    public static final String START_DATE = "start_date";
    public static final String REMARK = "remark";
    public static final String ACCOUNT_RANGE = "account_range";
    public static final String MODE_TYPE = "mode_type";
    public static final String PRIORITY = "priority";
    public static final String ACTIVE_STATUS = "active_status";
    public static final String ACTIVE_STATUS__ENABLE = "enable";
    public static final String MODIFY_TYPE = "modify_type";
    public static final String MODIFY_TYPE__MASTER = "master";
    public static final String MODIFY_TYPE__DETAIL = "detail";
    public static final String IMAGE_PATH = "image_path";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String PRODUCT_GIFT_DATA_JSON = "product_gift_data_json";
    public static final String CALCULATE_STATUS = "calculate_status";
    public static final String OWNER = "owner";
    public static final String ORDER_GOODS_MEETING_FLAG = "order_goods_meeting_flag";
    public static final String OBJECT_API_NAME = "object_api_name";
}
