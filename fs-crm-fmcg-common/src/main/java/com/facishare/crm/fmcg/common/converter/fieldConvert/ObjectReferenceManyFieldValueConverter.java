package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ObjectReferenceManyFieldValueConverter implements AbstractFieldValueConverter {

    private final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    @Override
    public String convert(FieldValueConvertContext context) {
        List<String> fieldValues = context.getFieldValues().stream().map(o -> (String) o).collect(Collectors.toList());
        String targetApiName = context.getFieldDescribe().get("target_api_name").toString();
        IObjectDescribe targetObjectDescribe = serviceFacade.findObject(context.getUser().getTenantId(), targetApiName);
        Boolean openDisplayName = targetObjectDescribe.isOpenDisplayName();
        List<IObjectData> targetDataList = serviceFacade.findObjectDataByIdsIgnoreAll(context.getUser().getTenantId(), fieldValues, targetApiName);
        if(CollectionUtils.notEmpty(targetDataList)) {
            List<String> nameList = Lists.newArrayListWithExpectedSize(fieldValues.size());
            Map<String, String> idNameMap = Maps.newHashMapWithExpectedSize(fieldValues.size());
            for (IObjectData objectData : targetDataList) {
                if (Boolean.TRUE.equals(openDisplayName)) {
                    idNameMap.put(objectData.getId(), objectData.getDisplayName());
                } else {
                    idNameMap.put(objectData.getId(), objectData.getName());
                }
            }
            for (String value : fieldValues) {
                nameList.add(idNameMap.getOrDefault(value, ""));
            }
            return String.join(",", nameList);
        }
        return null;
    }
}