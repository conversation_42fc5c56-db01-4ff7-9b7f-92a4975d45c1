package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.google.common.base.Joiner;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class SelectManyFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        SelectManyFieldDescribe selectManyFieldDescribe = (SelectManyFieldDescribe) context.getFieldDescribe();
        List<ISelectOption> optionList = selectManyFieldDescribe.getSelectOptions();
        if (CollectionUtils.isEmpty(optionList)) {
            return "";
        }
        Map optionMap = new HashMap<>();
        for (ISelectOption k : optionList) {
            optionMap.put(k.getValue(), k.getLabel());
        }

        List labelList = new LinkedList();
        for (Object k : context.getFieldValues()) {
            if (k != null && StringUtils.isNotBlank(k.toString())) {
                if (optionMap.containsKey(k.toString())) {
                    labelList.add(optionMap.get(k.toString()));
                }
            }
        }
        if(CollectionUtils.isEmpty(labelList)){
            return "";
        }
        return Joiner.on(",").join(labelList);
    }
}