package com.facishare.crm.fmcg.common.pojo;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Builder
public class SnCode implements Serializable {

    public static final String OUTER_TYPE = "outer";
    public static final String INNER_TYPE = "inner";

    private String type;

    private String manufacturerTenantId;

    private String storeTenantId;

    private String snId;

    private String activityId;

    private String storeId;

    private String activityProductId;

    private String rewardMethod;

    private String realCode;
}