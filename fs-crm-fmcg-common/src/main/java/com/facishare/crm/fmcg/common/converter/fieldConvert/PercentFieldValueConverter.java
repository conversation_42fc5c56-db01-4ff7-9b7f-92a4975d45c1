package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import org.apache.commons.lang3.StringUtils;

public class PercentFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        String fieldValue = String.valueOf(context.getFieldValue());

        return StringUtils.isNotBlank(fieldValue) ? fieldValue + "%" : "";
    }
}