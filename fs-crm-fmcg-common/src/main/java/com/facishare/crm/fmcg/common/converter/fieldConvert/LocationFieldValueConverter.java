package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.google.common.base.Strings;

public class LocationFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        String location = String.valueOf(context.getFieldValue());
        if (!Strings.isNullOrEmpty(location)) {
            String[] strArrary = location.split("\\#\\%\\$");
            if (strArrary.length > 2) {
                return strArrary[2];
            }
        }
        return location;
    }
}