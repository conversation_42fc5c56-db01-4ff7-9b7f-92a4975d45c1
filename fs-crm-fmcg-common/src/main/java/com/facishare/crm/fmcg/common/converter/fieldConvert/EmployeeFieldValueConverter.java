package com.facishare.crm.fmcg.common.converter.fieldConvert;

import com.facishare.crm.fmcg.common.converter.FieldValueConvertContext;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class EmployeeFieldValueConverter implements AbstractFieldValueConverter {
    private  OrgService orgService = SpringUtil.getContext().getBean(OrgService.class);

    @Override
    public String convert(FieldValueConvertContext context) {
        List<String> fieldValues = context.getFieldValues().stream().map(o -> (String) o).collect(Collectors.toList());
        Map<String, String> userNameMap = orgService.getUserNameMapByIds(context.getUser().getTenantId()
                , context.getUser().getUpstreamOwnerIdOrUserId(), fieldValues);
        return Joiner.on(",").join(userNameMap.values());
    }
}