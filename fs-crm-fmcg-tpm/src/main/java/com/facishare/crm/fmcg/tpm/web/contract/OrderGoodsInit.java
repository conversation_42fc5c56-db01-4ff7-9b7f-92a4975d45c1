package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * author: wuyx
 * description:
 * createTime: 2024/11/22 20:01
 */
public interface OrderGoodsInit {

    @Data
    @ToString
    class Arg implements Serializable {
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {
        private String msg;
        private String code;
    }
}