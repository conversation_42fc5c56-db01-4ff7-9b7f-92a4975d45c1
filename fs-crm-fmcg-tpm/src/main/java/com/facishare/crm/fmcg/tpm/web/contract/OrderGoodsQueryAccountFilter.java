package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2024/12/11 20:01
 */
public interface OrderGoodsQueryAccountFilter {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private String userId;

        @JSONField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;

        @JSONField(name = "object_id")
        @JsonProperty(value = "object_id")
        @SerializedName("object_id")
        private String objectId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<IFilter> filters;

    }
}