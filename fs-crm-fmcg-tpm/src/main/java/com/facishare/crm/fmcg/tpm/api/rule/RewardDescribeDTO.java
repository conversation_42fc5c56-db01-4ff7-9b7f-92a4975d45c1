package com.facishare.crm.fmcg.tpm.api.rule;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2023/9/15 15:27
 */
@Data
@ToString
public class RewardDescribeDTO implements Serializable {

    @JsonProperty(value = "tenant_id")
    @SerializedName("tenant_id")
    @JSONField(name = "tenant_id")
    private String tenantId;

    @JsonProperty(value = "field_describe_map")
    @SerializedName("field_describe_map")
    @JSONField(name = "field_describe_map")
    private Map<String, RewardFieldDescribeDTO> fieldDescribeMap;


    @JsonProperty(value = "max_reward_level")
    @SerializedName("max_reward_level")
    @JSONField(name = "max_reward_level")
    private Integer maxRewardLevel;

    @JsonProperty(value = "modules")
    @SerializedName("modules")
    @JSONField(name = "modules")
    private List<String> modules;
}
