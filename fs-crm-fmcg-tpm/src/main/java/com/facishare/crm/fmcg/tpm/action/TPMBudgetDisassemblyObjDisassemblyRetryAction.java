package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyNewDetailsFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IAsyncBudgetDisassemblyService;
import com.facishare.crm.fmcg.tpm.business.enums.DisassemblyActionCode;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import static com.facishare.crm.fmcg.tpm.business.AsyncBudgetDisassemblyService.STATUS_TRANSLATE_MAP;


@Slf4j
public class TPMBudgetDisassemblyObjDisassemblyRetryAction extends PreDefineAction<TPMBudgetDisassemblyObjDisassemblyRetryAction.Arg, TPMBudgetDisassemblyObjDisassemblyRetryAction.Result> {

    private final IAsyncBudgetDisassemblyService asyncBudgetDisassemblyService = SpringUtil.getContext().getBean(IAsyncBudgetDisassemblyService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.DISASSEMBLY_RETRY.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected Result doAct(Arg arg) {
        IObjectData masterData = loadMasterData();
        String status = masterData.get(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, String.class);
        if (!Objects.equals(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, status)
                && !Objects.equals(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, status)) {
            throw new MetaDataBusinessException(String.format(I18N.text(I18NKeys.METADATA_BUDGET_DISASSEMBLY_OBJ_DISASSEMBLY_RETRY_ACTION_0), I18N.text(STATUS_TRANSLATE_MAP.get(status))));
        }

        List<IObjectData> newDetails = loadNewDetails();
        List<IObjectData> existDetails = loadExistDetails();
        if (CollectionUtils.isEmpty(newDetails) && CollectionUtils.isEmpty(existDetails)) {
            log.info("newDetails and existDetails is empty.");
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OBJ_DISASSEMBLY_RETRY_ACTION_0));
        }
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            if (Objects.equals(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED, status)) {
                asyncBudgetDisassemblyService.unFrozen(actionContext.getUser(), arg.getObjectDataId());
            } else if (Objects.equals(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED, status)) {
                asyncBudgetDisassemblyService.doDisassembly(actionContext.getUser(), arg.getObjectDataId(), DisassemblyActionCode.DISASSEMBLY_RETRY.value());
            }

        })).run();
        return new Result();
    }


    private IObjectData loadMasterData() {
        return serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
    }

    private List<IObjectData> loadNewDetails() {
        List<String> fields = Lists.newArrayList(CommonFields.ID);

        return loadDetailWithLimit(actionContext.getTenantId(), arg.getObjectDataId(), fields, ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ);
    }

    private List<IObjectData> loadExistDetails() {
        List<String> fields = Lists.newArrayList(CommonFields.ID);


        return loadDetailWithLimit(actionContext.getTenantId(), arg.getObjectDataId(), fields, ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ);
    }


    private List<IObjectData> loadDetailWithLimit(String tenantId, String dataId, List<String> fields, String apiName) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(1);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter masterFilter = new Filter();
        masterFilter.setFieldName(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(dataId));

        stq.setFilters(Lists.newArrayList(masterFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String objectDataId;
    }

    @Data
    @ToString
    public static class Result implements Serializable {
    }
}
