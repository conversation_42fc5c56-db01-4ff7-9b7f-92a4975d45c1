package com.facishare.crm.fmcg.tpm.web.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccrualFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccrualService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetAccrualRulePO;
import com.facishare.crm.fmcg.tpm.service.abstraction.PluginInstanceService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.crm.fmcg.tpm.web.condition.model.ConditionDto;
import com.facishare.crm.fmcg.tpm.web.contract.model.*;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetAccrualRuleManager;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.autoconf.ConfigFactory;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/10 上午10:37
 */
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class BudgetAccrualRuleManager implements IBudgetAccrualRuleManager {

    @Resource
    private BudgetAccrualRuleDAO budgetAccrualRuleDAO;

    @Resource
    private PluginInstanceService pluginService;

    @Resource
    private ConditionAdapter conditionAdapter;

    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Resource
    private IBudgetAccrualService budgetAccrualService;

    @Resource
    private ServiceFacade serviceFacade;

    private static final String ACCRUAL_PLUGIN = "tpm_budget_accrual";
    protected static final Map<String, Predicate<BudgetAccrualRulePO>> ACCRUAL_RULE_FIELDS = Maps.newHashMap();
    private static final String CONFIG_NAME = "fs-fmcg-tpm-config";
    private static Map<String, List<String>> OBJECT_API_NAME_FILTER = new HashMap<>();

    static {
        ACCRUAL_RULE_FIELDS.put(String.format("%s.%s", ApiNames.TPM_BUDGET_ACCRUAL_OBJ, TPMBudgetAccrualFields.RULE_ID),
                type -> true);

        ConfigFactory.getConfig(CONFIG_NAME, config -> {
            String json = config.get("BUDGET_ACCRUAL_RULE_FILTER_OBJ");
            if (!Strings.isNullOrEmpty(json)) {
                OBJECT_API_NAME_FILTER = JSON.parseObject(json, new TypeReference<Map<String, List<String>>>() {
                });
            }
        });
    }

    @Override
    public void accrualRuleInfoValidate(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO) {

        //必填参数校验，
        accrualRuleRequiredValidate(budgetAccrualRuleVO);
        //重名校验，
        accrualRuleDuplicateNameValidate(tenantId, budgetAccrualRuleVO.getName());
        //唯一性校验，
        accrualRuleUniqueValidate(tenantId, budgetAccrualRuleVO.getApiName(),
                budgetAccrualRuleVO.getRecordType());
        //费用比例校验，
        accrualRuleRatioValidate(budgetAccrualRuleVO.getAccrualRuleNodes());
        //预算表映射关系校验
        budgetTableRelationValidate(budgetAccrualRuleVO.getAccrualRuleNodes());
    }

    @Override
    public void accrualRuleEditValidate(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO, BudgetAccrualRulePO oldPO) {
        //必填参数校验，
        accrualRuleRequiredValidate(budgetAccrualRuleVO);
        if (!oldPO.getName().equals(budgetAccrualRuleVO.getName())) {
            //重名校验，
            accrualRuleDuplicateNameValidate(tenantId, budgetAccrualRuleVO.getName());
        }
        if (!oldPO.getApiName().equals(budgetAccrualRuleVO.getApiName())
                || !oldPO.getRecordType().equals(budgetAccrualRuleVO.getRecordType())) {
            accrualRuleUniqueValidate(tenantId, budgetAccrualRuleVO.getApiName(),
                    budgetAccrualRuleVO.getRecordType());
        }
        //费用比例校验，
        accrualRuleRatioValidate(budgetAccrualRuleVO.getAccrualRuleNodes());
        //预算表映射关系校验
        budgetTableRelationValidate(budgetAccrualRuleVO.getAccrualRuleNodes());
    }

    @Override
    public void bindObjectPluginInstance(String tenantId, Integer employeeId, String apiName, String pluginName) {
        //查询对象是否已绑定插件
        if (pluginService.findPluginUnit(tenantId, apiName, pluginName)) {
            return;
        }
        try {
            pluginService.addPluginUnit(Integer.valueOf(tenantId), employeeId, apiName, pluginName);
        } catch (Exception e) {
            log.error("add plugin instance fail, e:", e);
        }
    }

    @Override
    public void publishSyncAccrualRuleFieldTask(String tenantId) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            List<BudgetAccrualRulePO> types = budgetAccrualRuleDAO.all(tenantId, false);
            for (Map.Entry<String, Predicate<BudgetAccrualRulePO>> entry : ACCRUAL_RULE_FIELDS.entrySet()) {
                types = types.stream().filter(entry.getValue()).collect(Collectors.toList());
                this.updateAccrualRuleFieldDescribe(tenantId, types, entry.getKey());
            }
        })).run();
    }

    @Override
    public void syncAddTriggerRuleStatusField(String tenantId, String objectApiName) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            budgetAccrualService.addTriggerRuleStatusField(tenantId, objectApiName);
        })).run();
    }

    @Override
    public Boolean isExistAccrualDataRelated(String tenantId, String apiName, String ruleId) {
        return budgetAccrualService.existsAccrualRuleDataByApiName(tenantId, apiName, ruleId);
    }

    @Override
    public void deleteObjectPluginInstance(String tenantId, String apiName, String recordType, String accrualPlugin) {
        //查询是否有规则用到了该对象 业务对象 + 类型
        long num = budgetAccrualRuleDAO.findByApiName(tenantId, apiName);
        if (num == 0) {
            //没有依据的了，
            pluginService.deletePluginUnit(tenantId, apiName, accrualPlugin);
        }
    }

    @Override
    public Map<String, String> findObjectsByTenantId(String tenantId) {
        List<IObjectDescribe> describes = serviceFacade.findObjectsByTenantId(tenantId, false, true, true, true);
        Set<String> objectApiNameSet = new HashSet<>();
        if (OBJECT_API_NAME_FILTER.containsKey(tenantId)) {
            objectApiNameSet.addAll(OBJECT_API_NAME_FILTER.get(tenantId));
        }
        return describes.stream()
                .filter(describe -> describe.getApiName().endsWith("__c")
                        || objectApiNameSet.contains(describe.getApiName()))
                .collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));
    }

    @Override
    public void deleteAccrualRuleByTypeId(String tenantId, int employeeId, String budgetTypeId) {
        budgetAccrualRuleDAO.isUsedByAccrualBudgetType(tenantId, budgetTypeId).forEach(accrualRule -> {
            budgetAccrualRuleDAO.delete(tenantId, employeeId, accrualRule.getUniqueId());
            deleteObjectPluginInstance(tenantId, accrualRule.getApiName(), accrualRule.getRecordType(), ACCRUAL_PLUGIN);
        });
    }

    @Override
    public void editPlatI18Key(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO) {
        try {
            RequestContext context = RequestContextManager.getContext();
            TPMI18Utils.editPlatBudgetI18Text(tenantId, budgetAccrualRuleVO.getId(), budgetAccrualRuleVO.getName(), context.getLang().getValue());
        } catch (Exception e) {
            log.error("编辑平台预算计提规则i18n失败", e);
        }
    }

    @Override
    public Map<String, String> getAccrualObjects(String tenantId) {
        Map<String, String> accrualObjectMap = Maps.newHashMap();
        List<String> apiNames = budgetAccrualRuleDAO.getAccrualObjects(tenantId);
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, apiNames);
        for (String apiName : apiNames) {
            IObjectDescribe objectDescribe = describeMap.get(apiName);
            if (objectDescribe != null) {
                accrualObjectMap.put(apiName, objectDescribe.getDisplayName());
            }
        }
        return accrualObjectMap;
    }

    private void updateAccrualRuleFieldDescribe(String tenantId, List<BudgetAccrualRulePO> types, String key) {
        try {
            String[] arr = key.split("\\.");

            String apiName = arr[0];
            String fieldApiName = arr[1];

            IObjectDescribe describe = describeService.findObject(tenantId, apiName);
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);

            Map<String, String> poMap = types.stream().collect(Collectors.toMap(k -> k.getId().toString(), BudgetAccrualRulePO::getDefaultName));
            for (SelectOne field : describeExt.getSelectOneFields()) {
                if (field.getApiName().equals(fieldApiName)) {
                    List<ISelectOption> options = field.getSelectOptions();

                    String optionsIdentity = toSelectOptionsIdentity(options);
                    String recordTypesIdentity = toPoMapIdentity(poMap);

                    if (!optionsIdentity.equals(recordTypesIdentity)) {
                        options.clear();
                        for (Map.Entry<String, String> entry : poMap.entrySet()) {
                            ISelectOption newOption = new SelectOption();
                            newOption.setLabel(entry.getValue());
                            newOption.setValue(entry.getKey());
                            options.add(newOption);
                        }
                        field.setSelectOptions(options);

                        IObjectDescribe updateResult = objectDescribeService.updateFieldDescribe(describe, Lists.newArrayList(field), this.getActionContext());
                        clearDescribeCache(tenantId, apiName);
                        log.info("update field : {}, update result : {}", field.toJsonString(), updateResult.toJsonString());
                    }
                }
            }
        } catch (ObjectDefNotFoundError objectDefNotFoundError) {
            log.info("describe not found ", objectDefNotFoundError);
        } catch (Exception ex) {
            // 非常严重的错误，更新预算模版的 select one 发生了错误
            log.error("update accrual rule select one field error : ", ex);
        }
    }

    private void clearDescribeCache(String tenantId, String apiName) {
        NotifierClient.send("describe-extra-clear-room", String.format("%s_%s", tenantId, apiName));
    }

    private String toSelectOptionsIdentity(List<ISelectOption> selectOptions) {
        return selectOptions
                .stream()
                .sorted(Comparator.comparing(ISelectOption::getValue))
                .map(m -> String.format("%s.%s", m.getValue(), m.getLabel()))
                .collect(Collectors.joining(","));
    }

    private String toPoMapIdentity(Map<String, String> poMap) {
        return poMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> String.format("%s.%s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining(","));
    }

    @Override
    public void enclosureConditionFilter(String tenantId, Integer employeeId, BudgetAccrualRuleVO budgetAccrualRuleVO) {
        String apiName = budgetAccrualRuleVO.getApiName();
        BudgetAccrualSourceNodeVO accrualSourceNode = budgetAccrualRuleVO.getAccrualSourceNode();
        List<BudgetWhereConditionVO> sourceWhereConditions = accrualSourceNode.getWhereConditions();
        String conditionCode = buildConditionCode(tenantId, employeeId, apiName, accrualSourceNode.getConditionCode(), sourceWhereConditions);
        accrualSourceNode.setConditionCode(conditionCode);

        for (BudgetAccrualRuleNodeVO accrualRuleNode : budgetAccrualRuleVO.getAccrualRuleNodes()) {
            List<BudgetWhereConditionVO> whereConditions = accrualRuleNode.getWhereConditions();
            String code = buildConditionCode(tenantId, employeeId, apiName, accrualRuleNode.getConditionCode(), whereConditions);
            accrualRuleNode.setConditionCode(code);
        }
    }

    private String buildConditionCode(String tenantId, Integer employeeId, String apiName, String ruleCode, List<BudgetWhereConditionVO> whereConditions) {
        List<ConditionDto> conditionDtoList = new ArrayList<>();
        int i = 0;
        // (0 and 1) or (2 and 3)
        StringBuilder pattern = new StringBuilder();
        for (BudgetWhereConditionVO whereCondition : whereConditions) {
            if (StringUtils.isNotEmpty(pattern.toString())) {
                pattern.append(" or ");
            }
            List<String> rowList = new ArrayList<>();
            for (BudgetTriggerConditionVO triggerCondition : whereCondition.getTriggerConditions()) {
                ConditionDto conditionDto = new ConditionDto();
                conditionDto.setFieldName(triggerCondition.getFieldName());
                conditionDto.setValues(triggerCondition.getFieldValues());
                conditionDto.setOperator(triggerCondition.getOperator());
                conditionDto.setRowNo(i++);
                conditionDtoList.add(conditionDto);
                rowList.add(String.valueOf(conditionDto.getRowNo()));
            }
            if (CollectionUtils.isEmpty(rowList)) {
                continue;
            }
            String order;
            if (rowList.size() > 1) {
                order = String.join(" and ", rowList);
            } else {
                order = rowList.get(0);
            }
            pattern.append("(").append(order).append(")");
        }
        if (CollectionUtils.isEmpty(conditionDtoList) && StringUtils.isEmpty(pattern.toString())) {
            return "";
        }
        if (StringUtils.isEmpty(ruleCode)) {
            //设置 生成条件的code
            return conditionAdapter.publish(Integer.valueOf(tenantId), employeeId, apiName, pattern.toString(), conditionDtoList);
        } else {
            // RULE UPDATE
            conditionAdapter.update(Integer.valueOf(tenantId), employeeId, apiName, ruleCode, pattern.toString(), conditionDtoList);
            return ruleCode;
        }

    }

    private void accrualRuleRequiredValidate(BudgetAccrualRuleVO vo) {
        // 必填项 校验
        validFieldEmpty(vo.getName(), vo.getApiName(), vo.getRecordType());
        validRuleNodes(vo.getAccrualSourceNode(), vo.getAccrualRuleNodes());
        BudgetAccrualSourceNodeVO sourceNode = vo.getAccrualSourceNode();
        validFieldEmpty(sourceNode.getTriggerTime(), sourceNode.getAmountSource(), sourceNode.getFrequency());
        // todo 校验 预算类型 是否必填
        vo.getAccrualRuleNodes().forEach(v -> validFieldEmpty(v.getNodeId(), v.getRatio(), v.getBudgetType()));

    }

    private void validFieldEmpty(String... object) {
        for (String field : object) {
            if (StringUtils.isEmpty(field)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_REQUIRED_ERROR));
            }
        }
    }

    private void validRuleNodes(BudgetAccrualSourceNodeVO accrualSourceNodeVO,
                                List<BudgetAccrualRuleNodeVO> accrualRuleNodeVOS) {
        if (Objects.isNull(accrualSourceNodeVO)
                || CollectionUtils.isEmpty(accrualRuleNodeVOS)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_REQUIRED_ERROR));
        }
        for (BudgetAccrualRuleNodeVO accrualRuleNodeVO : accrualRuleNodeVOS) {
            if (CollectionUtils.isEmpty(accrualRuleNodeVO.getFieldRelation())) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_REQUIRED_ERROR));
            }
        }
    }

    private void budgetTableRelationValidate(List<BudgetAccrualRuleNodeVO> accrualRuleNodes) {
        Map<String, List<BudgetFieldRelationVO>> tableMap = new HashMap<>();
        for (BudgetAccrualRuleNodeVO budgetTableNode : accrualRuleNodes) {
            if (tableMap.containsKey(budgetTableNode.getNodeId())) {
                validEqualsCollection(tableMap.get(budgetTableNode.getNodeId()), budgetTableNode.getFieldRelation());
            } else {
                tableMap.put(budgetTableNode.getNodeId(), budgetTableNode.getFieldRelation());
            }
        }
    }

    private void validEqualsCollection(List<BudgetFieldRelationVO> relationList,
                                       List<BudgetFieldRelationVO> nowRelationList) {
        if (CollectionUtils.isEqualCollection(relationList, nowRelationList)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_BUDGET_RELATION_ERROR));
        }
    }

    private void accrualRuleDuplicateNameValidate(String tenantId, String name) {
        if (budgetAccrualRuleDAO.isExistsAccrualRuleByName(tenantId, name)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_DUPLICATE_NAME_ERROR));
        }
    }

    private void accrualRuleUniqueValidate(String tenantId, String apiName, String recordType) {
        if (budgetAccrualRuleDAO.isExistsByApiNameWithRecordType(tenantId, apiName, recordType)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_EXISTS_USED_RULE_ERROR));
        }
    }

    private void accrualRuleRatioValidate(List<BudgetAccrualRuleNodeVO> accrualRuleNodeEntityList) {
        try {
            // 不允许出现负 百分比
            List<BudgetAccrualRuleNodeVO> ruleNodeVOS = accrualRuleNodeEntityList.stream()
                    .filter(v -> new BigDecimal(v.getRatio()).doubleValue() <= 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ruleNodeVOS)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_MANAGER_0));
            }

            Optional<String> reduce = accrualRuleNodeEntityList.stream()
                    .map(BudgetAccrualRuleNodeVO::getRatio)
                    .reduce((v1, v2) -> String.valueOf(new BigDecimal(v1).add(new BigDecimal(v2))));
            if (reduce.isPresent()
                    && new BigDecimal(reduce.get()).compareTo(new BigDecimal(100)) > 0) {
                log.info("AccrualRule reduce count is {}", reduce.get());
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_COST_RATIO_ERROR));
            }
        } catch (NumberFormatException formatException) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCRUAL_RULE_MANAGER_1));
        }
    }

    private IActionContext getActionContext() {
        RequestContext requestContext = RequestContextManager.getContext();
        IActionContext actionContext = ActionContextExt.of(new User((String) null, (String) null)).getContext();
        boolean direct = false;
        boolean notNeedDeepCopy = false;
        Boolean upstreamCopyDescribe = null;
        if (Objects.nonNull(requestContext)) {
            if (Objects.nonNull(requestContext.getAttribute("direct"))) {
                direct = (Boolean) requestContext.getAttribute("direct");
            }

            if (Objects.nonNull(requestContext.getAttribute("not_need_deep_copy"))) {
                notNeedDeepCopy = (Boolean) requestContext.getAttribute("not_need_deep_copy");
            }

            if (Objects.nonNull(requestContext.getAttribute("upstream_copy_describe"))) {
                upstreamCopyDescribe = (Boolean) requestContext.getAttribute("upstream_copy_describe");
            }
        }

        actionContext.put("direct", direct);
        actionContext.put("not_need_deep_copy", notNeedDeepCopy);
        actionContext.put("upstream_copy_describe", upstreamCopyDescribe);
        actionContext.put("not_check_option_has_data", true);
        return actionContext;
    }

}
