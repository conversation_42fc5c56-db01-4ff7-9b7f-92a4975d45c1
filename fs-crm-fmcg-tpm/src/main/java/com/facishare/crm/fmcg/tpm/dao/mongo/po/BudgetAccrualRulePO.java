package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetAccrualRuleVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IBudgetAccrualRule;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 预算  计提
 *
 * <AUTHOR>
 * @date 2022/10/9
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_budget_accrual_rule", noClassnameStored = true)
public class BudgetAccrualRulePO extends MongoPO implements IBudgetAccrualRule {

    // 全局
    public static final String F_VERSION = "version";
    public static final String F_NAME = "name";
    public static final String F_STATUS = "status";
    public static final String F_DESCRIPTION = "description";
    public static final String F_API_NAME = "api_name";
    public static final String F_RECORD_TYPE = "record_type";
    public static final String F_BUDGET_TYPE = "budget_type";

    //node
    public static final String F_ACCRUAL_SOURCE_NODE = "accrual_source_node";
    public static final String F_ACCRUAL_RULE_NODE = "accrual_rule_node";

    @Property(F_VERSION)
    private long version;

    // 计提名称
    @Property(F_NAME)
    private String name;

    //计提状态
    @Property(F_STATUS)
    private String status;

    //计提描述
    @Property(F_DESCRIPTION)
    private String description;

    //业务对象
    @Property(F_API_NAME)
    private String apiName;

    //业务类型
    @Property(F_RECORD_TYPE)
    private String recordType;

    //预算类型
    @Property(F_BUDGET_TYPE)
    private String budgetType;

    @Embedded(F_ACCRUAL_SOURCE_NODE)
    private BudgetAccrualSourceNodeEntity accrualSourceNode;

    @Embedded(F_ACCRUAL_RULE_NODE)
    private List<BudgetAccrualRuleNodeEntity> accrualRuleNodes;

    public static BudgetAccrualRulePO fromVO(BudgetAccrualRuleVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }
        BudgetAccrualRulePO budgetAccrualRulePO = new BudgetAccrualRulePO();
        budgetAccrualRulePO.setUniqueId(vo.getId());
        budgetAccrualRulePO.setName(vo.getName());
        budgetAccrualRulePO.setStatus(vo.getStatus());
        budgetAccrualRulePO.setDescription(vo.getDescription());
        budgetAccrualRulePO.setApiName(vo.getApiName());
        budgetAccrualRulePO.setRecordType(vo.getRecordType());
        budgetAccrualRulePO.setAccrualSourceNode(BudgetAccrualSourceNodeEntity.fromVO(vo.getAccrualSourceNode()));
        if (CollectionUtils.isEmpty(vo.getAccrualRuleNodes())) {
            budgetAccrualRulePO.setAccrualRuleNodes(new ArrayList<>());
        } else {
            budgetAccrualRulePO.setAccrualRuleNodes(vo.getAccrualRuleNodes().stream().map(BudgetAccrualRuleNodeEntity::fromVO).collect(Collectors.toList()));
        }

        return budgetAccrualRulePO;
    }

    public static BudgetAccrualRuleVO toVO(BudgetAccrualRulePO po) {
        if (Objects.isNull(po)) {
            return null;
        }
        BudgetAccrualRuleVO budgetAccrualRuleVO = new BudgetAccrualRuleVO();
        budgetAccrualRuleVO.setId(po.getId().toString());
        budgetAccrualRuleVO.setName(po.getName());
        budgetAccrualRuleVO.setDescription(po.getDescription());
        budgetAccrualRuleVO.setStatus(po.getStatus());
        budgetAccrualRuleVO.setApiName(po.getApiName());
        budgetAccrualRuleVO.setRecordType(po.getRecordType());
        budgetAccrualRuleVO.setAccrualSourceNode(BudgetAccrualSourceNodeEntity.toVO(po.getAccrualSourceNode()));
        if (CollectionUtils.isEmpty(po.getAccrualRuleNodes())) {
            budgetAccrualRuleVO.setAccrualRuleNodes(new ArrayList<>());
        } else {
            budgetAccrualRuleVO.setAccrualRuleNodes(po.getAccrualRuleNodes().stream().map(BudgetAccrualRuleNodeEntity::toVO).collect(Collectors.toList()));
        }
        budgetAccrualRuleVO.setTenantId(po.getTenantId());
        budgetAccrualRuleVO.setCreator(po.getCreator());
        budgetAccrualRuleVO.setCreateTime(po.getCreateTime());
        budgetAccrualRuleVO.setLastUpdater(po.getLastUpdater());
        budgetAccrualRuleVO.setLastUpdateTime(po.getLastUpdateTime());
        budgetAccrualRuleVO.setDeleted(po.isDeleted());
        budgetAccrualRuleVO.setVersion(po.getVersion());
        return budgetAccrualRuleVO;
    }

    public static BudgetAccrualRulePO fromEditVO(BudgetAccrualRulePO po, BudgetAccrualRuleVO vo) {

        if (Objects.isNull(vo)) {
            return null;
        }
        po.setId(po.getOriginalId());
        po.setUniqueId(vo.getId());
        po.setName(vo.getName());
        po.setDescription(vo.getDescription());
        po.setStatus(vo.getStatus());
        po.setApiName(vo.getApiName());
        po.setRecordType(vo.getRecordType());
        po.setAccrualSourceNode(BudgetAccrualSourceNodeEntity.fromVO(vo.getAccrualSourceNode()));
        if (CollectionUtils.isEmpty(vo.getAccrualRuleNodes())) {
            po.setAccrualRuleNodes(new ArrayList<>());
        } else {
            po.setAccrualRuleNodes(vo.getAccrualRuleNodes().stream().map(BudgetAccrualRuleNodeEntity::fromVO).collect(Collectors.toList()));
        }
        return po;
    }

    @Override
    public String getName() {
        if (Objects.isNull(getId()) || Strings.isNullOrEmpty(super.getTenantId()) || Strings.isNullOrEmpty(name)) {
            return name;
        }
        String text = TPMI18Utils.getBudgeText(super.getTenantId(), getId().toString());
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }

    @Override
    public String getDefaultName() {
        return name;
    }
}