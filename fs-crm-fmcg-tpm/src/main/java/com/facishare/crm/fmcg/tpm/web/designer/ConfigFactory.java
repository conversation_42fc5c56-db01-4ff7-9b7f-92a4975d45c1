package com.facishare.crm.fmcg.tpm.web.designer;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigEnum;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * author: wuyx
 * description:
 * createTime: 2023/3/23 17:26
 */

@Component
public class ConfigFactory {

    private static final Map<String, String> RESOLVE_MAP = Maps.newHashMap();

    static {
        RESOLVE_MAP.put(ConfigEnum.DEALER_RECORD_TYPE.key(), "dealerRecordTypeConfigHandler");
        RESOLVE_MAP.put(ConfigEnum.STORE_DEALER.key(), "storeDealerConfigHandler");
        RESOLVE_MAP.put(ConfigEnum.ACTIVATED_SCAN_CODE_ACTION.key(), "activatedScanCodeActionConfigHandler");
        RESOLVE_MAP.put(ConfigEnum.REWARD_TAG.key(), "rewardTagConfigHandler");
        RESOLVE_MAP.put(ConfigEnum.APL_DISPLAY_CUSTOMIZED_INFORMATION.key(), "aplDisplayCustomizedInfoConfigHandler");
    }

    public IConfigHandler resolve(String key) {
        String handler = RESOLVE_MAP.get(key);
        if (Strings.isEmpty(handler)) {
            throw new ValidateException(I18N.text(I18NKeys.CONFIG_FACTORY_0));
        }
        return SpringUtil.getContext().getBean(handler, IConfigHandler.class);
    }
}
