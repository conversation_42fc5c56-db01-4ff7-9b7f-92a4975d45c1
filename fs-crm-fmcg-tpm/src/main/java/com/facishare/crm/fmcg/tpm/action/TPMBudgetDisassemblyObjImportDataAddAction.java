package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetDisassemblyService;
import com.facishare.paas.appframework.core.predef.action.StandardImportDataAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
public class TPMBudgetDisassemblyObjImportDataAddAction extends StandardImportDataAddAction {

    private final IBudgetDisassemblyService budgetDisassemblyService = SpringUtil.getContext().getBean(IBudgetDisassemblyService.class);
    private final Map<String, BigDecimal> availableAmountGroupByAccountId = Maps.newHashMap();

    @Override
    protected void before(Arg arg) {
        log.info("import budget.arg:{}", arg);
        super.before(arg);
        arg.setIsApprovalFlowEnabled(false);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return super.getFuncPrivilegeCodes();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }

    @Override
    protected void validateField(IObjectDescribe objectDescribe, List<ImportData> dataList) {

        super.validateField(objectDescribe, dataList);

        for (Map.Entry<String, List<ImportData>> entry : importDataListMap.entrySet()) {
            String describeApiName = entry.getKey();
            List<ImportData> importDataList = entry.getValue();

            if (!Objects.equals(describeApiName, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ)) {
                continue;
            }
            List<ImportError> errorList = new ArrayList<>();

            for (ImportData data : importDataList) {
                try {
                    validateMasterData(data.getData());

                    setDefaultValue(data.getData());

                } catch (Exception ex) {
                    log.error("validateMasterData error:{},errorRowNo:{},errorMsg:{}", ex, data.getRowNo(), ex.getMessage());
                    ImportError error = new ImportError();
                    error.setRowNo(data.getRowNo());
                    error.setErrorMessage(ex.getMessage());
                    errorList.add(error);
                }


            }
            mergeErrorList(errorList);
        }

    }

    private void setDefaultValue(IObjectData masterData) {
        masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_AMOUNT, 0);
        masterData.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, 0);
        masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SCHEDULE);
        masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_FAILED_MESSAGE, null);
        masterData.set(TPMBudgetDisassemblyFields.DISASSEMBLY_COMPLETED_TIME, null);

        String sourceAccountId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
        BigDecimal availableAmount = availableAmountGroupByAccountId.get(sourceAccountId);
        if (availableAmount == null) {
            IObjectData sourceAccount = budgetDisassemblyService.findDataWithFields(actionContext.getTenantId(), Lists.newArrayList(TPMBudgetAccountFields.AVAILABLE_AMOUNT), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
            if (sourceAccount == null) {
                return;
            }
            String availableAmountStr = sourceAccount.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class);
            if (StringUtils.isEmpty(availableAmountStr)) {
                return;
            }
            availableAmount = new BigDecimal(availableAmountStr);
            availableAmountGroupByAccountId.put(sourceAccountId, availableAmount);
        }

        masterData.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, availableAmount);
    }

    private void validateMasterData(IObjectData data) {
        log.info("masterData import data:{}", JSON.toJSONString(data));
        budgetDisassemblyService.validateMasterData(actionContext.getTenantId(), data);
    }

}
