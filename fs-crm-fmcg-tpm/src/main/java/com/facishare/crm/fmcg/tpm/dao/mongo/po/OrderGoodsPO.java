package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2024/11/26 11:41
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_order_goods", noClassnameStored = true)
public class OrderGoodsPO extends MongoPO implements Serializable {

    public static final String F_VERSION = "version";
    public static final String F_STATUS = "status";
    public static final String F_REF_OBJECT_API_NAME = "ref_object_api_name";
    public static final String F_REF_OBJECT_ID = "ref_object_id";
    public static final String F_RECHARGE_PROMOTION_JSON = "recharge_promotion_json";
    public static final String F_PRODUCT_PROMOTION_JSON_COPY = "product_promotion_json_copy";
    public static final String F_RECHARGE_PROMOTION_JSON_COPY = "recharge_promotion_json_copy";

    @Property(F_VERSION)
    private long version;

    @Property(F_STATUS)
    private String status;

    @Property(F_REF_OBJECT_API_NAME)
    private String objectApiName;

    @Property(F_REF_OBJECT_ID)
    private String objectId;

    @Property(F_RECHARGE_PROMOTION_JSON)
    private String rechargePromotionJson;

    @Property(F_PRODUCT_PROMOTION_JSON_COPY)
    private String productPromotionJsonCopy;

    @Property(F_RECHARGE_PROMOTION_JSON_COPY)
    private String rechargePromotionJsonCopy;

}