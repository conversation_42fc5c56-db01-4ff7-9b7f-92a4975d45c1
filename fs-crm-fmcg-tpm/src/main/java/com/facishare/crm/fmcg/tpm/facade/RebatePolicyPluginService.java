package com.facishare.crm.fmcg.tpm.facade;

import com.facishare.crm.fmcg.common.apiname.PricePolicyFields;
import com.facishare.crm.fmcg.tpm.business.OrderGoodsPromotionPolicyService;
import com.facishare.crm.fmcg.tpm.business.TPMTriggerActionService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2024/12/16 16:34
 */
@Slf4j
@Service
@ServiceModule("tpm_rebate_price_policy_action")
public class RebatePolicyPluginService {

    @Resource
    private OrderGoodsPromotionPolicyService orderGoodsPromotionPolicyService;

    @ServiceMethod("add_before")
    public AddActionDomainPlugin.Result before(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        validationTPMActivityId(objectData);
        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result before(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        validationTPMActivityId(objectData);
        return new EditActionDomainPlugin.Result();
    }

    @ServiceMethod("bulkInvalid_before")
    public BulkInvalidActionDomainPlugin.Result before(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        if (orderGoodsPromotionPolicyService.isFromTPM(arg.getExtraData())) {
            return new BulkInvalidActionDomainPlugin.Result();
        }
        List<ObjectDataDocument> objectDataList = arg.getObjectDataList();
        List<String> excludeNames = validationTPMActivityIds(objectDataList);

        if (CollectionUtils.isNotEmpty(excludeNames)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.REBATE_POLICY_PLUGIN_SERVICE_1) + ":%s", excludeNames.toString()));
        }

        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("invalid_before")
    public InvalidActionDomainPlugin.Result before(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        if (orderGoodsPromotionPolicyService.isFromTPM(arg.getExtraData())) {
            return new InvalidActionDomainPlugin.Result();
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        validationTPMActivityId(objectData);
        return new InvalidActionDomainPlugin.Result();
    }

    private List<String> validationTPMActivityIds(List<ObjectDataDocument> objectDataList) {
        List<String> excludeNames = Lists.newArrayList();
        for (ObjectDataDocument objectData : objectDataList) {
            String activityId = objectData.toObjectData().get(PricePolicyFields.ACTIVITY_ID, String.class, "");
            if (!isFromTPM(objectData.toObjectData())) {
                if (StringUtils.isNotBlank(activityId)) {
                    excludeNames.add(objectData.toObjectData().getName());
                }
            }
        }
        return excludeNames;
    }

    private void validationTPMActivityId(IObjectData objectData) {
        String activityId = objectData.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
        if (isFromTPM(objectData)) {
            if (StringUtils.isBlank(activityId)) {
                throw new ValidateException(I18N.text(I18NKeys.REBATE_POLICY_PLUGIN_SERVICE_0));
            }
        } else {
            if (StringUtils.isNotBlank(activityId)) {
                throw new ValidateException(I18N.text(I18NKeys.REBATE_POLICY_PLUGIN_SERVICE_1));
            }
        }
    }

    private boolean isFromTPM(IObjectData objectData) {
        String requestFrom = objectData.get(TPMTriggerActionService.REQUEST_FROM, String.class);
        return TPMTriggerActionService.REQUEST_APP_NAME.equals(requestFrom);
    }

}
