package com.facishare.crm.fmcg.tpm.business.abstraction;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/8 下午3:33
 */
public interface IDescribeCacheService {

    boolean isExistField(String tenantId, String objApiName, String fieldApiName);

    void initFields(String tenantId, String objectApiName, List<String> fields);

    void updateFields(String tenantId, String objectApiName, List<String> fields);

    void saveOrUpdateFields(String tenantId, String objectApiName, List<String> fields);

    void initLayout(String tenantId, String objectApiName, String layoutApiName);

    void createDefaultDescribe(String tenantId, List<String> objectApiNames);

    void assignLayout(String tenantId, String layoutType, String objectApiName, String objectRecordType, String objectLayoutApiName);
}
