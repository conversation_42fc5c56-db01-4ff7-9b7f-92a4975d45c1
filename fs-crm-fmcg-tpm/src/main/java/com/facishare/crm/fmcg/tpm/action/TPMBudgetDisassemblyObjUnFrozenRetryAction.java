package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.IAsyncBudgetDisassemblyService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;


@Slf4j
@SuppressWarnings("Duplicates,unused")
public class TPMBudgetDisassemblyObjUnFrozenRetryAction extends PreDefineAction<TPMBudgetDisassemblyObjUnFrozenRetryAction.Arg, TPMBudgetDisassemblyObjUnFrozenRetryAction.Result> {

    private final IAsyncBudgetDisassemblyService asyncBudgetDisassemblyService = SpringUtil.getContext().getBean(IAsyncBudgetDisassemblyService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.DisassemblyUnFrozenRetry.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected Result doAct(Arg arg) {
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            asyncBudgetDisassemblyService.unFrozen(actionContext.getUser(), arg.getObjectDataId());
        })).run();
        return new Result();
    }

    @Data
    @ToString
    public static class Arg implements Serializable {

        @SerializedName("object_data_id")
        @JSONField(name = "object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;
    }

    @Data
    @ToString
    public static class Result implements Serializable {
    }
}