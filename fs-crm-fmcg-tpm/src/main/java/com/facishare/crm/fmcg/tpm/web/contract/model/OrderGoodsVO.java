package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class OrderGoodsVO extends CommonVO implements Serializable {

    private long version;

    private String status;

    @JSONField(name = "product_promotion_json")
    @JsonProperty(value = "product_promotion_json")
    @SerializedName("product_promotion_json")
    private List<OrderGoodsPromotionPolicyRuleVO> productPromotionJson;

    @JSONField(name = "recharge_promotion_json")
    @JsonProperty(value = "recharge_promotion_json")
    @SerializedName("recharge_promotion_json")
    private String rechargePromotionJson;
}
