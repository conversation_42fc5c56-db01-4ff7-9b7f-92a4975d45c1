package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityNodeTemplate;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 18:43
 */
@Entity(value = "fmcg_tpm_activity_node_template", noClassnameStored = true)
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class ActivityNodeTemplatePO extends MongoPO implements IActivityNodeTemplate {

    public static final String F_NAME = "name";
    public static final String F_DESCRIPTION = "description";
    public static final String F_OBJECT_API_NAME = "object_api_name";
    public static final String F_REFERENCE_FIELD_API_NAME = "reference_field_api_name";
    public static final String F_PACKAGE = "package";
    public static final String F_TYPE = "type";
    public static final String F_STATUS = "status";
    public static final String F_EXCEPTION_STATUS = "exception_status";
    public static final String F_EXCEPTION_COUNT = "exception_count";
    public static final String F_VERSION = "version";
    public static final String F_NODE_EXCEPTION_INFO = "node_exception_info";

    @Property(F_NAME)
    private String name;

    @Property(F_DESCRIPTION)
    private String description;

    @Property(F_OBJECT_API_NAME)
    private String objectApiName;

    @Property(F_REFERENCE_FIELD_API_NAME)
    private String referenceFieldApiName;

    @Property(F_PACKAGE)
    private String packageType;
    /**
     * activity audit proof ...
     */
    @Property(F_TYPE)
    private String type;

    @Property(F_STATUS)
    private String status;

    @Property(F_EXCEPTION_STATUS)
    private String exceptionStatus;

    @Property(F_EXCEPTION_COUNT)
    private Integer exceptionCount = 0;

    @Property(F_VERSION)
    private int version;

    @Embedded(F_NODE_EXCEPTION_INFO)
    private NodeExceptionInfoEntity nodeExceptionInfo;

    public static ActivityNodeTemplatePO fromVO(ActivityNodeTemplateVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityNodeTemplatePO po = new ActivityNodeTemplatePO();
        po.setName(vo.getName());
        po.setUniqueId(vo.getId());
        po.setDescription(vo.getDescription());
        po.setObjectApiName(vo.getObjectApiName());
        po.setReferenceFieldApiName(vo.getReferenceFieldApiName());
        po.setPackageType(PackageType.CUSTOM.value());
        po.setType(NodeType.CUSTOM.value());
        po.setStatus(vo.getStatus());
        po.setExceptionStatus(ExceptionStatusType.NORMAL.value());
        po.setExceptionCount(0);
        po.setVersion(vo.getVersion());
        po.setNodeExceptionInfo(new NodeExceptionInfoEntity(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.NORMAL.value()));
        return po;
    }

    @Override
    public String getName() {
        if (Objects.isNull(getId()) || Strings.isNullOrEmpty(this.objectApiName) || Strings.isNullOrEmpty(this.name)) {
            return name;
        }
        String text = TPMI18Utils.getActivitySystemNodeText(super.getTenantId(), getId().toString(), this.objectApiName, this.name, this.type);
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }
}
