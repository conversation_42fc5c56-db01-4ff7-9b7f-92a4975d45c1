package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetConsumeRuleVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IBudgetConsumeRule;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/26 下午5:54
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_budget_consume_rule", noClassnameStored = true)
public class BudgetConsumeRulePO extends MongoPO implements IBudgetConsumeRule {

    public static final String F_VERSION = "version";
    public static final String F_NAME = "name";
    public static final String F_RULE_TYPE = "rule_type";
    public static final String F_RULE_DESCRIPTION = "rule_description";
    public static final String F_RULE_STATUS = "rule_status";
    public static final String F_API_NAME = "api_name";
    public static final String F_RECORD_TYPE = "record_type";
    public static final String F_RULE_TYPE_NODES = "rule_type_nodes";
    public static final String F_BUDGET_TABLE_NODES = "budget_table_nodes";
    public static final String F_BUDGET_TYPE = "budget_type";

    @Property(F_VERSION)
    private long version;

    @Property(F_NAME)
    private String name;

    @Property(F_RULE_TYPE)
    private String ruleType;

    @Property(F_RULE_DESCRIPTION)
    private String ruleDescription;

    @Property(F_RULE_STATUS)
    private String ruleStatus;

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_BUDGET_TYPE)
    private String budgetType;

    @Property(F_RECORD_TYPE)
    private String recordType;

    @Embedded(F_RULE_TYPE_NODES)
    private List<BudgetRuleTypeNodeEntity> ruleTypeNodes;

    @Embedded(F_BUDGET_TABLE_NODES)
    private List<BudgetTableNodeEntity> budgetTableNodes;

    public static BudgetConsumeRulePO fromVO(BudgetConsumeRuleVO vo) {
        if (vo == null) {
            return null;
        }
        BudgetConsumeRulePO budgetConsumeRulePO = new BudgetConsumeRulePO();
        budgetConsumeRulePO.setUniqueId(vo.getId());
        budgetConsumeRulePO.setName(vo.getName());
        budgetConsumeRulePO.setRuleType(vo.getRuleType());
        budgetConsumeRulePO.setRuleDescription(vo.getRuleDescription());
        budgetConsumeRulePO.setRuleStatus(vo.getRuleStatus());
        budgetConsumeRulePO.setApiName(vo.getApiName());
        budgetConsumeRulePO.setBudgetType(vo.getBudgetType());
        budgetConsumeRulePO.setRecordType(vo.getRecordType());
        if (CollectionUtils.isEmpty(vo.getRuleTypeNodes())) {
            budgetConsumeRulePO.setRuleTypeNodes(new ArrayList<>());
        } else {
            budgetConsumeRulePO.setRuleTypeNodes(vo.getRuleTypeNodes().stream().map(BudgetRuleTypeNodeEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getBudgetTableNodes())) {
            budgetConsumeRulePO.setBudgetTableNodes(new ArrayList<>());
        } else {
            budgetConsumeRulePO.setBudgetTableNodes(vo.getBudgetTableNodes().stream().map(BudgetTableNodeEntity::fromVO).collect(Collectors.toList()));
        }
        return budgetConsumeRulePO;

    }

    public static BudgetConsumeRuleVO toVO(BudgetConsumeRulePO po) {
        if (po == null) {
            return null;
        }
        BudgetConsumeRuleVO budgetConsumeRuleVO = new BudgetConsumeRuleVO();
        budgetConsumeRuleVO.setId(po.getId().toString());
        budgetConsumeRuleVO.setName(po.getName());
        budgetConsumeRuleVO.setRuleType(po.getRuleType());
        budgetConsumeRuleVO.setRuleDescription(po.getRuleDescription());
        budgetConsumeRuleVO.setRuleStatus(po.getRuleStatus());
        budgetConsumeRuleVO.setApiName(po.getApiName());
        budgetConsumeRuleVO.setBudgetType(po.getBudgetType());
        budgetConsumeRuleVO.setRecordType(po.getRecordType());
        if (CollectionUtils.isEmpty(po.getRuleTypeNodes())) {
            budgetConsumeRuleVO.setRuleTypeNodes(new ArrayList<>());
        } else {
            budgetConsumeRuleVO.setRuleTypeNodes(po.getRuleTypeNodes().stream().map(BudgetRuleTypeNodeEntity::toVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(po.getBudgetTableNodes())) {
            budgetConsumeRuleVO.setBudgetTableNodes(new ArrayList<>());
        } else {
            budgetConsumeRuleVO.setBudgetTableNodes(po.getBudgetTableNodes().stream().map(BudgetTableNodeEntity::toVO).collect(Collectors.toList()));
        }
        budgetConsumeRuleVO.setTenantId(po.getTenantId());
        budgetConsumeRuleVO.setCreator(po.getCreator());
        budgetConsumeRuleVO.setCreateTime(po.getCreateTime());
        budgetConsumeRuleVO.setLastUpdater(po.getLastUpdater());
        budgetConsumeRuleVO.setLastUpdateTime(po.getLastUpdateTime());
        budgetConsumeRuleVO.setDeleted(po.isDeleted());
        budgetConsumeRuleVO.setVersion(po.getVersion());
        return budgetConsumeRuleVO;
    }

    @Override
    public String getName() {
        if (Objects.isNull(getId()) || Strings.isNullOrEmpty(super.getTenantId()) || Strings.isNullOrEmpty(name)) {
            return name;
        }
        String text = TPMI18Utils.getBudgeText(super.getTenantId(), getId().toString());
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }
}
