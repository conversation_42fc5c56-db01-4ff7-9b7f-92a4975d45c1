package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class FunctionInfo {
    /**
     * 函数aiName
     */
    @JSONField(name = "api_name")
    @SerializedName("api_name")
    @JsonProperty("api_name")
    private String apiName;

    @JSONField(name = "function_name")
    @SerializedName("function_name")
    @JsonProperty("function_name")
    private String functionName;

    @JSONField(name = "binding_object_api_name")
    @SerializedName("binding_object_api_name")
    @JsonProperty("binding_object_api_name")
    private String bindingObjectApiName;

    @JSONField(name = "name_space")
    @SerializedName("name_space")
    @JsonProperty("name_space")
    private String nameSpace;

    @JSONField(name = "return_type")
    @SerializedName("return_type")
    @JsonProperty("return_type")
    private String returnType;
}
