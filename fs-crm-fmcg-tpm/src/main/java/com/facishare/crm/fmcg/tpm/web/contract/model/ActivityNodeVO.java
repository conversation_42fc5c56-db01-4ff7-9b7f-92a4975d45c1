package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.StatusType;
import com.facishare.crm.fmcg.tpm.web.designer.IActivityNode;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 16:23
 */
@Data
@ToString
@SuppressWarnings("Duplicates")
public class ActivityNodeVO implements
        Serializable,
        IActivityNode {

    private String name;

    @JSONField(name = "activity_plan_report_data")
    @JsonProperty(value = "activity_plan_report_data")
    @SerializedName("activity_plan_report_data")
    private ActivityPlanReportDatumVO activityPlanReportData;

    @JSONField(name = "template_id")
    @JsonProperty(value = "template_id")
    @SerializedName("template_id")
    private String templateId;

    @JSONField(name = "package")
    @JsonProperty(value = "package")
    @SerializedName("package")
    private String packageType;

    @JSONField(name = "type")
    @JsonProperty(value = "type")
    @SerializedName("type")
    private String type;

    @JSONField(name = "object_api_name")
    @JsonProperty(value = "object_api_name")
    @SerializedName("object_api_name")
    private String objectApiName;

    @JSONField(name = "object_display_name")
    @JsonProperty(value = "object_display_name")
    @SerializedName("object_display_name")
    private String objectDisplayName;

    @JSONField(name = "object_record_type")
    @JsonProperty(value = "object_record_type")
    @SerializedName("object_record_type")
    private String objectRecordType;

    private String description;

    @JSONField(name = "exception_status")
    @JsonProperty(value = "exception_status")
    @SerializedName("exception_status")
    private String exceptionStatus;

    @JSONField(name = "object_record_type_name")
    @JsonProperty(value = "object_record_type_name")
    @SerializedName("object_record_type_name")
    private String objectRecordTypeName;

    @JSONField(name = "reference_activity_field_api_name")
    @JsonProperty(value = "reference_activity_field_api_name")
    @SerializedName("reference_activity_field_api_name")
    private String referenceActivityFieldApiName;

    @JSONField(name = "activity_plan_config")
    @JsonProperty(value = "activity_plan_config")
    @SerializedName("activity_plan_config")
    private ActivityPlanConfigVO activityPlanConfig;

    @JSONField(name = "activity_agreement_config")
    @JsonProperty(value = "activity_agreement_config")
    @SerializedName("activity_agreement_config")
    private ActivityAgreementConfigVO activityAgreementConfig;

    @JSONField(name = "activity_proof_config")
    @JsonProperty(value = "activity_proof_config")
    @SerializedName("activity_proof_config")
    private ActivityProofConfigVO activityProofConfig;

    @JSONField(name = "activity_proof_audit_config")
    @JsonProperty(value = "activity_proof_audit_config")
    @SerializedName("activity_proof_audit_config")
    private ActivityProofAuditConfigVO activityProofAuditConfig;

    @JSONField(name = "activity_write_off_config")
    @JsonProperty(value = "activity_write_off_config")
    @SerializedName("activity_write_off_config")
    private ActivityWriteOffConfigVO activityWriteOffConfig;

    @JSONField(name = "activity_store_write_off_config")
    @JsonProperty(value = "activity_store_write_off_config")
    @SerializedName("activity_store_write_off_config")
    private ActivityStoreWriteOffConfigVO activityStoreWriteOffConfig;

    @JSONField(name = "activity_cost_assign_config")
    @JsonProperty(value = "activity_cost_assign_config")
    @SerializedName("activity_cost_assign_config")
    private ActivityCostAssignConfigVO activityCostAssignConfig;

    @JSONField(name = "node_exception_info")
    @JsonProperty(value = "node_exception_info")
    @SerializedName("node_exception_info")
    private NodeExceptionInfoVO nodeExceptionInfo;

    public static ActivityNodeVO fromPO(
            ActivityNodeEntity po,
            Map<String, String> objectNameMap,
            Map<String, Map<String, String>> recordTypeNameMap) {
        if (po == null) {
            return null;
        }

        ActivityNodeVO vo = new ActivityNodeVO();

        vo.setName(po.getName());
        vo.setType(po.getType());
        vo.setTemplateId(po.getTemplateId());
        vo.setPackageType(po.getPackageType());
        vo.setObjectApiName(po.getObjectApiName());
        vo.setObjectDisplayName(objectNameMap.getOrDefault(po.getObjectApiName(), "--"));
        vo.setObjectRecordType(po.getObjectRecordType());
        vo.setObjectRecordTypeName(recordTypeNameMap.getOrDefault(po.getObjectApiName(), Maps.newHashMap()).getOrDefault(po.getObjectRecordType(), "--"));
        vo.setReferenceActivityFieldApiName(po.getReferenceActivityFieldApiName());
        vo.setNodeExceptionInfo(NodeExceptionInfoVO.fromPO(po.getNodeExceptionInfo()));

        switch (po.getType()) {
            case "plan":
                vo.setActivityPlanConfig(ActivityPlanConfigVO.fromPO(po.getActivityPlanConfig()));
                break;
            case "proof":
                vo.setActivityProofConfig(ActivityProofConfigVO.fromPO(po.getActivityProofConfig()));
                break;
            case "write_off":
                vo.setActivityWriteOffConfig(ActivityWriteOffConfigVO.fromPO(po.getActivityWriteOffConfig()));
                break;
            case "audit":
                vo.setActivityProofAuditConfig(ActivityProofAuditConfigVO.fromPO(po.getActivityProofAuditConfig()));
                break;
            case "agreement":
                vo.setActivityAgreementConfig(ActivityAgreementConfigVO.fromPO(po.getActivityAgreementConfig()));
                break;
            case "cost_assign":
                vo.setActivityCostAssignConfig(ActivityCostAssignConfigVO.fromPO(po.getActivityCostAssignConfigEntity()));
                break;
            case "store_write_off":
                vo.setActivityStoreWriteOffConfig(ActivityStoreWriteOffConfigVO.fromPO(po.getActivityStoreWriteOffConfig()));
                break;
            default:
                break;
        }
        return vo;
    }

    public static ActivityNodeVO fromPO(ActivityNodeEntity po) {
        if (po == null) {
            return null;
        }
        ActivityNodeVO vo = new ActivityNodeVO();

        vo.setName(po.getName());
        vo.setType(po.getType());
        vo.setTemplateId(po.getTemplateId());
        vo.setPackageType(po.getPackageType());
        vo.setObjectApiName(po.getObjectApiName());
        vo.setObjectRecordType(po.getObjectRecordType());
        vo.setExceptionStatus(po.getExceptionStatus() == null ? StatusType.NORMAL.value() : po.getExceptionStatus());
        vo.setNodeExceptionInfo(NodeExceptionInfoVO.fromPO(po.getNodeExceptionInfo()));
        vo.setReferenceActivityFieldApiName(po.getReferenceActivityFieldApiName());

        switch (po.getType()) {
            case "plan":
                vo.setActivityPlanConfig(ActivityPlanConfigVO.fromPO(po.getActivityPlanConfig()));
                break;
            case "proof":
                vo.setActivityProofConfig(ActivityProofConfigVO.fromPO(po.getActivityProofConfig()));
                break;
            case "write_off":
                vo.setActivityWriteOffConfig(ActivityWriteOffConfigVO.fromPO(po.getActivityWriteOffConfig()));
                break;
            case "audit":
                vo.setActivityProofAuditConfig(ActivityProofAuditConfigVO.fromPO(po.getActivityProofAuditConfig()));
                break;
            case "agreement":
                vo.setActivityAgreementConfig(ActivityAgreementConfigVO.fromPO(po.getActivityAgreementConfig()));
                break;
            case "cost_assign":
                vo.setActivityCostAssignConfig(ActivityCostAssignConfigVO.fromPO(po.getActivityCostAssignConfigEntity()));
                break;
            case "store_write_off":
                vo.setActivityStoreWriteOffConfig(ActivityStoreWriteOffConfigVO.fromPO(po.getActivityStoreWriteOffConfig()));
                break;
            default:
                break;
        }
        return vo;
    }

    public static ActivityNodeVO fromPOToValidActivityTypeVO(ActivityNodeEntity po) {
        if (po == null) {
            return null;
        }
        ActivityNodeVO vo = new ActivityNodeVO();

        vo.setTemplateId(po.getTemplateId());
        vo.setObjectApiName(po.getObjectApiName());
        vo.setObjectRecordType(po.getObjectRecordType());
        vo.setName(po.getName());

        return vo;
    }

    @Override
    public String getName() {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = "-10000";
        if (Objects.nonNull(context)) {
            tenantId = context.getTenantId();
        }
        if (Strings.isNullOrEmpty(this.templateId) || Strings.isNullOrEmpty(this.objectApiName) || Strings.isNullOrEmpty(this.name)) {
            return name;
        }
        String text = TPMI18Utils.getActivitySystemNodeText(tenantId, this.templateId, this.objectApiName, this.name, this.type);
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }

}