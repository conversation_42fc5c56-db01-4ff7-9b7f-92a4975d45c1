package com.facishare.crm.fmcg.tpm.api.rule;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/1/2 15:20
 */
public interface GetRuleDescribe {

    @Data
    @ToString
    class Arg implements Serializable {

        @JsonProperty(value = "tenant_id")
        @SerializedName("tenant_id")
        @JSONField(name = "tenant_id")
        private String tenantId;

        @JsonProperty(value = "template_id")
        @SerializedName("template_id")
        @JSONField(name = "template_id")
        private String templateId;
    }


    @Data
    @ToString
    class Result implements Serializable {

    }
}
