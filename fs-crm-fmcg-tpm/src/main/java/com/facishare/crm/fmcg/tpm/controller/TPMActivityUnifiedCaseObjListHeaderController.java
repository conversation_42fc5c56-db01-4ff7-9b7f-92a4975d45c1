package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/23/20 8:42 PM
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class TPMActivityUnifiedCaseObjListHeaderController extends StandardListHeaderController {

    private final static String IMPORT_BUTTON = "Import";

    @Override
    protected Result after(Arg arg, Result result) {

        hideButtonFilter(arg, result);
        return super.after(arg, result);
    }

    private void hideButtonFilter(Arg arg, Result result) {
        log.info("activityObj arg layoutAgentType is {}", arg.getLayoutAgentType());
        if (!"mobile".equals(arg.getLayoutAgentType())) {
            List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
            result.getLayout().put("buttons", buttons.stream().filter(v -> !IMPORT_BUTTON.equals(v.getString("action"))).collect(Collectors.toList()));
        } else {
            ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
            buttons.removeIf(button -> {
                Map btn = (Map) (button);
                return IMPORT_BUTTON.equals(btn.get("action"));
            });
        }
    }


    //hide field
    @Override
    protected List<String> getAuthorizedFields() {
        log.info("start getAuthorizedFields...");
        List<String> list = super.getAuthorizedFields();
        if (!TPMGrayUtils.isShowFieldWithActivityRangeField(controllerContext.getTenantId())){
            list.remove("store_range");
        }
        list.remove("cashing_product_range");
        list.remove("product_range");
        return list;
    }
}