package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.business.OrderGoodsPromotionPolicyService;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.api.enumeration.LogType;
import com.facishare.crm.fmcg.tpm.api.log.LogData;
import com.facishare.crm.fmcg.tpm.api.method.IdempotentArgBase;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.OperateInfoService;
import com.facishare.crm.fmcg.tpm.business.UnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/11 下午5:03
 */
//IgnoreI18nFile
@SuppressWarnings("Duplicates")
public class TPMActivityObjInvalidAction extends StandardInvalidAction implements TransactionService<StandardInvalidAction.Arg, StandardInvalidAction.Result> {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);

    private final OperateInfoService operateInfoService = SpringUtil.getContext().getBean(OperateInfoService.class);

    private final IRewardRuleManager rewardRuleManager = SpringUtil.getContext().getBean(IRewardRuleManager.class);

    private SpecialTableMapper specialTableMapper = SpringUtil.getContext().getBean(SpecialTableMapper.class);

    private final ActivityRewardRuleDAO activityRewardRuleDAO = SpringUtil.getContext().getBean(ActivityRewardRuleDAO.class);

    private boolean isTpm2Tenant = false;
    private boolean isOderGoods = false;
    public static final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);

    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);
    private final OrderGoodsPromotionPolicyService orderGoodsPromotionPolicyService = SpringUtil.getContext().getBean(OrderGoodsPromotionPolicyService.class);
    private IObjectData activity = null;


    @Override
    protected void before(Arg arg) {
        this.isTpm2Tenant = tpm2Service.isTPM2Tenant(Integer.valueOf(actionContext.getTenantId()));

        activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_OBJ);
        if (promotionPolicyService.judgedIsPromotionPolicyData(activity) &&
                promotionPolicyService.doEnableInvalidPromotionPolicy(actionContext.getTenantId(), activity)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_INVALID_ACTION_0));
        }

        ActivityRewardRulePO activityRewardRulePO = activityRewardRuleDAO.getByRelatedObject(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectDataId());
        if (activityRewardRulePO != null) {
            rewardRuleManager.validateContainsFailRewardDetail(activityRewardRulePO.getTenantId(), activityRewardRulePO.getUniqueId());
        }

        validationOrderGoodsPromotion();

        SearchTemplateQuery query = new SearchTemplateQuery();
        validationHasProof(arg, query);
        validationHasAgreement(arg, query);
        validationHasCost(arg, query);

        super.before(arg);
    }

    private void validationOrderGoodsPromotion() {
        Boolean oderGoodsMeetingFlag = activity.get(TPMActivityFields.ORDER_GOODS_MEETING_FLAG, Boolean.class,false);
        if (oderGoodsMeetingFlag) {
            List<String> userOrderGoodsNames = orderGoodsPromotionPolicyService.queryUsedOrderGoodsPromotionActivityNames(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), Lists.newArrayList(activity));
            if (!CollectionUtils.isEmpty(userOrderGoodsNames)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_OBJ_BULK_INVALID_ACTION_1), userOrderGoodsNames));
            }
            isOderGoods = true;
        }
    }

    private void validationHasCost(Arg arg, SearchTemplateQuery query) {
        Filter deletedFilter;
        Filter idFilter;
        List<IObjectData> data;

        query.setLimit(1);
        query.setOffset(0);

        idFilter = new Filter();
        idFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_DEALER_ACTIVITY_COST, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_DEALER_ACTIVITY_CAN_NOT_BE_INVALID));
        }
    }

    private void validationHasAgreement(Arg arg, SearchTemplateQuery query) {
        Filter deletedFilter;
        Filter idFilter;
        List<IObjectData> data;

        query.setLimit(1);
        query.setOffset(0);

        idFilter = new Filter();
        idFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.INVALID_ACTIVITY_ERROR_ITEM_USED_BY_AGREEMENT));
        }
    }

    private void validationHasProof(Arg arg, SearchTemplateQuery query) {
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMActivityProofFields.ACTIVITY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(arg.getObjectDataId()));

        Filter deletedFilter = new Filter();
        deletedFilter.setFieldName(CommonFields.IS_DELETED);
        deletedFilter.setOperator(Operator.EQ);
        deletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(idFilter, deletedFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();

        if (!data.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.INVALID_ACTIVITY_ERROR_ITEM_USED_BY_PROOF));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        // 促销类，删除，同步价格政策规则
        disablePromotionPolicy(activity);
        disableOrderGoodsPromotion();
        Result rst = super.after(arg, result);
        recalculateAmount(rst.getObjectData().toObjectData());
        String type = (String) result.getObjectData().get("record_type");
        if ("default__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE_CUSTOM, false);
        }
        if ("dealer_activity__c".equals(type)) {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE_PERSONAL, false);
        }
        BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.parseInt(actionContext.getUser().getUpstreamOwnerIdOrUserId()), BuryModule.TPM.TPM_ACTIVITY, BuryOperation.DELETE, isTpm2Tenant);
        //作废激励规则
        invalidRewardRule(activity.getId());

        return rst;
    }

    private void disableOrderGoodsPromotion() {
        if (isOderGoods) {
            orderGoodsPromotionPolicyService.disableOrderGoodsSFAData(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), activity);
        }
    }

    private void invalidRewardRule(String id) {
        activityRewardRuleDAO.deleteByRelatedId(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, id, Integer.parseInt(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()));
    }

    private void recalculateAmount(IObjectData data) {
        if (!this.isApprovalFlowStartSuccess(data.getId())) {
            unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), data.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {

        Result result = super.doAct(arg);

        if (!"ineffective".equals(activity.get(CommonFields.LIFE_STATUS)) && budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            String budgetId = (String) result.getObjectData().getOrDefault(TPMActivityFields.BUDGET_TABLE, "");
            if (TPMGrayUtils.skipDeletedBudgetWhenCalculating(actionContext.getTenantId())) {
                List<Map> data = (specialTableMapper.setTenantId(actionContext.getTenantId())).findBySql(String.format("select id,is_deleted from fmcg_tpm_activity_budget where id = '%s' and tenant_id = '%s'", SqlEscaper.pg_escape(budgetId), SqlEscaper.pg_escape(actionContext.getTenantId())));
                if (!CollectionUtils.isEmpty(data)) {
                    Map budget = data.get(0);
                    if (!"0".equals(budget.get("is_deleted").toString())) {
                        return result;
                    }
                }
            }
            String closeStatus = (String) activity.get(TPMActivityFields.CLOSE_STATUS);

            if (!Strings.isNullOrEmpty(budgetId) && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
                budgetService.tryLockBudget(actionContext, budgetId);
                double activityAmount = Double.parseDouble((String) CommonUtils.getOrDefault(result.getObjectData().get(TPMActivityFields.ACTIVITY_AMOUNT), "0.0"));
                double actualAmount = Double.parseDouble((String) CommonUtils.getOrDefault(result.getObjectData().get(TPMActivityFields.ACTIVITY_ACTUAL_AMOUNT), "0.0"));
                IObjectData budget = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), budgetId, ApiNames.TPM_ACTIVITY_BUDGET);
                double availableAmount = Double.parseDouble((String) budget.get(TPMActivityBudgetFields.AVAILABLE_AMOUNT));

                LogData logData = LogData.builder().data(JSON.toJSONString(result.getObjectData())).build();
                logData.setAttribute("budget", budget);
                String logId = operateInfoService.log(actionContext.getTenantId(), LogType.INVALID.value(), JSON.toJSONString(logData), actionContext.getUser().getUpstreamOwnerIdOrUserId(), ApiNames.TPM_ACTIVITY_OBJ, arg.getObjectDataId(), this.needTriggerApprovalFlow());

                budgetService.addBudgetDetail(actionContext.getTenantId(), actionContext.getUser().getUserId(),
                        "2",
                        budgetId,
                        String.format("个案活动作废：「%s」作废", result.getObjectData().get("name")),
                        activityAmount - actualAmount,
                        availableAmount,
                        availableAmount + activityAmount,
                        System.currentTimeMillis(),
                        String.format("LogId:%s-traceId:%s", logId, TraceContext.get().getTraceId()),
                        arg.getObjectDataId(),
                        TraceContext.get().getTraceId(),
                        IdempotentArgBase.builder().idempotentKey(actionContext.getPostId() + ":" + budgetId).build());
                budgetService.calculateBudget(actionContext.getTenantId(), (String) result.getObjectData().getOrDefault(TPMActivityFields.BUDGET_TABLE, ""));
            }
        }

        return result;
    }

    private void disablePromotionPolicy(IObjectData objectData) {
        if (promotionPolicyService.judgedIsPromotionPolicyData(objectData)) {
            promotionPolicyService.disablePromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);
        }

    }
}
