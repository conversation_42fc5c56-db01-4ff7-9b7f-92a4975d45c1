package com.facishare.crm.fmcg.tpm.business.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.enums.RIOSpecialDisplayFormEnum;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FmcgCrmProxy;
import com.fmcg.framework.http.contract.fmcg.DataReportAch;
import com.fmcg.framework.http.contract.fmcg.ai.GetDisplayScenesByModelId;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService.TENANT_DISPLAY_DETECT_FAULT_TOLERANCE;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class QueryDataManager {

    @Resource
    private FmcgCrmProxy fmcgCrmProxy;
    @Resource
    private ServiceFacade serviceFacade;


    public Map<String, IObjectData> queryRIODisplayProjectJudgmentStandard(String tenantId, List<IObjectData> details, boolean existAgreementNode) {
        if (!TPMGrayUtils.isRioTenant(tenantId) || CollectionUtils.isEmpty(details)) {
            return Collections.emptyMap();
        }

        Map<String, IObjectData> rioDisplayProjectJudgmentStandard = new HashMap<>();
        List<String> activityDetails;
        if (existAgreementNode) {
            activityDetails = details.stream().map(d -> d.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(activityDetails)) {
                return Collections.emptyMap();
            }
        } else {
            activityDetails = details.stream().map(DBRecord::getId).collect(Collectors.toList());
        }

        List<IObjectData> activityDetailObjs = serviceFacade.findObjectDataByIds(tenantId, activityDetails, ApiNames.TPM_ACTIVITY_DETAIL_OBJ);
        if (CollectionUtils.isEmpty(activityDetailObjs)) {
            return Collections.emptyMap();
        }

        Map<String, IObjectData> activityDetailObjMap = activityDetailObjs.stream().collect(Collectors.toMap(DBRecord::getId, activityDetail -> activityDetail));

        List<String> displayProjectJudgmentStandardIds = activityDetailObjs.stream()
                .filter(detail -> StringUtils.isNotBlank(detail.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class)))
                .map(detail -> detail.get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class))
                .collect(Collectors.toList());

        List<IObjectData> displayProjectJudgmentStandardObjs = serviceFacade.findObjectDataByIds(tenantId, displayProjectJudgmentStandardIds, ApiNames.DISPLAY_PROJECT_JUDGMENT_STANDARD__C);
        if (CollectionUtils.isEmpty(displayProjectJudgmentStandardObjs)) {
            return Collections.emptyMap();
        }

        Map<String, IObjectData> displayProjectJudgmentStandardMap = displayProjectJudgmentStandardObjs.stream().collect(Collectors.toMap(DBRecord::getId, obj -> obj));

        for (IObjectData data : details) {
            String activityDetailId = existAgreementNode ? data.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class) : data.getId();
            String displayProjectJudgmentStandardId = activityDetailObjMap.get(activityDetailId).get(TPMActivityDetailFields.DISPLAY_PROJECT_JUDGMENT_STANDARD, String.class);
            if (StringUtils.isBlank(displayProjectJudgmentStandardId)) {
                continue;
            }
            if (!displayProjectJudgmentStandardMap.containsKey(displayProjectJudgmentStandardId)) {
                continue;
            }
            rioDisplayProjectJudgmentStandard.put(activityDetailId, displayProjectJudgmentStandardMap.get(displayProjectJudgmentStandardId));
        }

        return rioDisplayProjectJudgmentStandard;
    }



    public Map<String, TPMDisplayReportService.ProductOrMaterialAchieveStatusDTO> queryProductOrMaterialAchieveStatus(String tenantId, String activityId, String agreementId, String proofId, IObjectData proofObj, boolean existAgreementNode) {
        DataReportAch.Arg arg = new DataReportAch.Arg();
        arg.setTenantId(tenantId);
        arg.setDataMainId(proofId);
        arg.setDataMainApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        arg.setCheckinsId(proofObj.get(TPMActivityProofFields.VISIT_ID, String.class, ""));
        arg.setBusinessDataApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        arg.setBusinessDataId(proofId);
        arg.setAccountId(getAccountIdOfProof(proofObj));

        Map<String, TPMDisplayReportService.ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap = new HashMap<>();
        if (existAgreementNode) {
            //协议
            arg.setStandardMainId(agreementId);
            arg.setStandardMainApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            if (StringUtils.isBlank(agreementId)) {
                return Collections.emptyMap();
            }
        } else {
            //活动
            arg.setStandardMainId(activityId);
            arg.setStandardMainApiName(ApiNames.TPM_ACTIVITY_OBJ);
            if (StringUtils.isBlank(activityId)) {
                return Collections.emptyMap();
            }
        }

        log.info("prodMatRes start:{}", JSON.toJSONString(arg));
        DataReportAch.Result result = fmcgCrmProxy.prodMatRes(Integer.parseInt(tenantId), -10000, arg);
        if (result.getCode() != 0 || result.getData().isEmpty()) {
            log.info("prodMatRes fail:{}", result.getMessage());
            return Collections.emptyMap();
        }
        log.info("prodMatRes success:{}", result.getData());

        result.getData().forEach((k, v) -> {
            String productAchieveStatus = revertAchieveStatus(tenantId, v.getProductDetail());
            String materialAchieveStatus = revertAchieveStatus(tenantId, v.getMaterialDetail());

            TPMDisplayReportService.ProductOrMaterialAchieveStatusDTO materialAchieveStatusDTO = TPMDisplayReportService.ProductOrMaterialAchieveStatusDTO.builder()
                    .proofId(proofId)
                    .dataId(k)
                    .productAchieveStatus(productAchieveStatus)
                    .materialAchieveStatus(materialAchieveStatus)
                    .build();
            productOrMaterialAchieveStatusMap.put(k, materialAchieveStatusDTO);
        });

        return productOrMaterialAchieveStatusMap;
    }

    public List<TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm> queryTenantDetectFaultTolerantForDisPlayForm(String tenantId, GetDisplayScenesByModelId.Result displayScenesByModelIdRest) {

        Map<String, String> faultKeyRelationMap = TENANT_DISPLAY_DETECT_FAULT_TOLERANCE.get(tenantId);
        if (TPMGrayUtils.isRioTenant(tenantId)) {
            faultKeyRelationMap = Maps.of(RIOSpecialDisplayFormEnum.SHELF.getFieldKey(), RIOSpecialDisplayFormEnum.GE.getFieldKey());
        }

        if (Objects.isNull(faultKeyRelationMap) || faultKeyRelationMap.isEmpty()) {
            log.info("faultKeyRelationMap is empty, tenantId: {}", tenantId);
            return Collections.emptyList();
        }

        log.info("faultKeyRelationMap: {}", faultKeyRelationMap);

        List<TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList = new ArrayList<>();

        faultKeyRelationMap.forEach((faultKey, reliabilityKey) -> {
            String faultValue = displayScenesByModelIdRest.getDisplayScenes().stream()
                    .filter(scene -> scene.getKey().equals(faultKey))
                    .findFirst()
                    .map(GetDisplayScenesByModelId.DisplaySceneDTO::getValue)
                    .orElse("");

            String reliabilityValue = displayScenesByModelIdRest.getDisplayScenes().stream()
                    .filter(scene -> scene.getKey().equals(reliabilityKey))
                    .findFirst()
                    .map(GetDisplayScenesByModelId.DisplaySceneDTO::getValue)
                    .orElse("");

            if (StringUtils.isBlank(faultValue) || StringUtils.isBlank(reliabilityValue)) {
                log.info("faultKey: {}, reliabilityKey: {}, faultValue: {}, reliabilityValue: {}", faultKey, reliabilityKey, faultValue, reliabilityValue);
                return;
            }
            tenantDetectFaultTolerantForDisPlayFormList.add(
                    TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm.builder()
                            .faultKeys(Lists.newArrayList(faultKey))
                            .reliabilityKey(reliabilityKey)
                            .faultValues(Lists.newArrayList(faultValue))
                            .reliabilityValue(reliabilityValue)
                            .build()
            );
        });

        return tenantDetectFaultTolerantForDisPlayFormList;
    }


    public List<IObjectData> queryTPMActivityProofDetailsByProofId(String tenantId, String proofId) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.EQ);
        activityProofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(activityProofIdFilter));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);
    }

    public List<IObjectData> queryTPMActivityDetailByObjectId(String tenantId, String id) {

        if (StringUtils.isEmpty(id)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(TPMActivityDetailFields.ACTIVITY_ID);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        return details;
    }


    public List<IObjectData> queryTPMActivityProofDisplayImgsByProofIds(String tenantId, List<String> proofIds) {
        if (CollectionUtils.isEmpty(proofIds)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(proofIds);

        query.setFilters(Lists.newArrayList(activityIdFilter));

        // ACTIVITY_PROOF_ID + DISPLAY_FORM_ID + ACTIVITY_ITEM_ID 共同组成一组举证项目
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, query);
    }

    /**
     * 通过举证ID查询举证产品明细对象
     *
     * @param tenantId 租户ID
     * @param proofId  举证ID
     * @return 举证产品明细对象列表
     */
    public List<IObjectData> queryTPMActivityProofProductDetailsByProofIds(String tenantId, String proofId, boolean isRetake, List<String> displayFormIds) {
        if (Objects.isNull(proofId)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofProductDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.EQ);
        activityProofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(activityProofIdFilter));


        if (isRetake && CollectionUtils.isNotEmpty(displayFormIds)) {
            Filter displayFormFilter = new Filter();
            displayFormFilter.setFieldName(TPMActivityProofProductDetailFields.DISPLAY_FORM_ID);
            displayFormFilter.setOperator(Operator.IN);
            displayFormFilter.setFieldValues(displayFormIds);
            query.getFilters().add(displayFormFilter);
        }

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_PRODUCT_DETAIL_OBJ, query);
    }

    /**
     * 通过举证ID查询举证物料明细对象
     *
     * @param tenantId 租户ID
     * @param proofId  举证ID
     * @return 举证物料明细对象列表
     */
    public List<IObjectData> queryTPMActivityProofMaterialDetailsByProofIds(String tenantId, String proofId, boolean isRetake, List<String> displayFormIds) {
        if (Objects.isNull(proofId)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofMaterialDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.EQ);
        activityProofIdFilter.setFieldValues(Lists.newArrayList(proofId));

        query.setFilters(Lists.newArrayList(activityProofIdFilter));

        if (isRetake) {
            Filter displayFormFilter = new Filter();
            displayFormFilter.setFieldName(TPMActivityProofMaterialDetailFields.DISPLAY_FORM_ID);
            displayFormFilter.setOperator(Operator.IN);
            displayFormFilter.setFieldValues(displayFormIds);
            query.getFilters().add(displayFormFilter);
        }

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_MATERIAL_DETAIL_OBJ, query);
    }


    public List<IObjectData> queryTPMAgreementDetailByObjectId(String tenantId, String id) {

        if (StringUtils.isEmpty(id)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> details = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        return details;
    }

    private static String getAccountIdOfProof(IObjectData proofObj) {
        String accountId = proofObj.get(TPMActivityProofFields.STORE_ID, String.class);
        if (StringUtils.isEmpty(accountId)) {
            accountId = proofObj.get(TPMActivityProofFields.DEALER_ID, String.class);
        }
        return accountId;
    }

    private static String revertAchieveStatus(String tenantId, DataReportAch.ProjectAchievementDetail productDetail) {
        String productAchieveStatus = "";
        if (Objects.isNull(productDetail)) {
            return productAchieveStatus;
        }
        if (productDetail.isAchieved()) {
            productAchieveStatus = TPMActivityProofDetailFields.PASS_STATUS;
        } else if (productDetail.getAchievedCount() == 0) {
            productAchieveStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (productDetail.getAchievedCount() < productDetail.getTotalCount()) {
            productAchieveStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
        }
        return productAchieveStatus;
    }
}
