package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.action.TPMActivityObjCloseTPMActivityAction;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IUnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.common.constant.OrderGoodsContents;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.ITenantDevService;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * author: wuyx
 * description:
 * createTime: 2022/3/7 18:28
 */

@Slf4j
@Component
public class ActivityService implements IActivityService {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    private DescribeLogicService describeLogicService;

    @Resource
    private IRangeFieldBusiness rangeFieldBusiness;

    @Resource
    private StoreBusiness storeBusiness;

    @Resource
    private ITenantDevService tenantDevService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    @Resource
    private IUnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness;

    private static final OrganizationService organizationService = SpringUtil.getContext().getBean(OrganizationService.class);

    private static final IDescribeCacheService I_DESCRIBE_CACHE_SERVICE = SpringUtil.getContext().getBean(IDescribeCacheService.class);

    private final DataPrivilegeService dataPrivilegeService = SpringUtil.getContext().getBean(DataPrivilegeService.class);

    @Override
    public void preValidateAccount(User user, IObjectData activity) {
        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(user.getTenantId()).user(user).build(), ApiNames.TPM_ACTIVITY_OBJ, "Add");
        // new TPMActivityObjAddAction().preValidate(activity, actionContext);
    }

    @Override
    public boolean validateActivityEnableEdit(String tenantId, String activityId) {
        log.info("init validateActivityEnableEdit tenantId={},activityId={}", tenantId, activityId);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            log.info("活动申请不存在！");
            return false;
        }

        String activityTypeId = ObjectDataExt.of(activity).getStringValue(TPMActivityFields.ACTIVITY_TYPE);

        ActivityTypePO po = activityTypeDAO.get(tenantId, activityTypeId);
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_EXISTS_ERROR));
        }
        ActivityTypeVO vo = ActivityTypeVO.fromPO(po);

        for (ActivityNodeVO node : vo.getActivityNodeList()) {
            if (NodeType.PLAN.value().equals(node.getType())
                    || NodeType.PLAN_TEMPLATE.value().equals(node.getType())) {
                continue;
            }
            String apiName = node.getObjectApiName();
            IObjectDescribe describe = describeLogicService.findObject(tenantId, apiName);
            if (describe == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_OBJ_DESCRIBE_NOT_FOUND_ERROR));
            }

            for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
                if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                    String targetApiName = fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
                    if (ApiNames.TPM_ACTIVITY_OBJ.equals(targetApiName)) {
                        List<IObjectData> obj = findObjByActivity(tenantId, activityId, apiName, fieldDescribe.getApiName());
                        if (!CollectionUtils.isEmpty(obj)) {

                          /*  if (TPMGrayUtils.isAllowEditActivityObjCustomField(tenantId) || tenantDevService.isMengNiuCloudTenant(Integer.parseInt(tenantId))) {
                                if (isNotExistsPreField) {
                                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_0));
                                }
                            } else {
                            }*/
                            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_DATA_ALREADY_EXISTS_ERROR));
                        }
                    }
                }
            }
        }

        return true;
    }

    @Override
    public boolean validateActivityProofEnableEdit(String tenantId, String proofId, String activityId) {
        log.info("init validateActivityEnableEdit tenantId={},activityId={}", tenantId, activityId);
        IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            return false;
        }

        String activityTypeId = ObjectDataExt.of(activity).getStringValue(TPMActivityFields.ACTIVITY_TYPE);

        ActivityTypePO po = activityTypeDAO.get(tenantId, activityTypeId);
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_EXISTS_ERROR));
        }
        ActivityTypeVO vo = ActivityTypeVO.fromPO(po);

        for (ActivityNodeVO node : vo.getActivityNodeList()) {
            if (NodeType.PLAN.value().equals(node.getType())
                    || NodeType.PLAN_TEMPLATE.value().equals(node.getType())
                    || NodeType.AGREEMENT.value().equals(node.getType())
                    || NodeType.PROOF.value().equals(node.getType())) {
                continue;
            }
            String apiName = node.getObjectApiName();
            IObjectDescribe describe = describeLogicService.findObject(tenantId, apiName);
            if (describe == null) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_OBJ_DESCRIBE_NOT_FOUND_ERROR));
            }

            for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
                if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                    String targetApiName = fieldDescribe.get(ObjectReferenceFieldDescribe.TARGET_API_NAME, String.class);
                    if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(targetApiName)) {
                        List<IObjectData> obj = findObjByActivity(tenantId, proofId, apiName, fieldDescribe.getApiName());
                        if (!CollectionUtils.isEmpty(obj)) {
                            if (ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ.equals(apiName)){
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_DATA_EXISTS_BY_AUDIT_ERROR));
                            }else if (ApiNames.TPM_DEALER_ACTIVITY_COST.equals(apiName)){
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_DATA_EXISTS_BY_COST_ERROR));
                            }else{
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_DATA_ALREADY_EXISTS_ERROR));
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    @Override
    public void ifAllowCreateDataDueToOnceWriteOff(String tenantId, IObjectData activity) {
        if (Objects.isNull(activity)) {
            log.info("ifAllowCreateDataDueToOnceWriteOff activity is null ");
            return;
        }

        String maxWriteOffCount = activity.get(TPMActivityFields.MAX_WRITE_OFF_COUNT, String.class);

        if (ActivityMaxWriteOffCountEnum.ONCE.value().equals(maxWriteOffCount)) {

            Filter activityFilter = new Filter();
            activityFilter.setFieldName(TPMDealerActivityCostFields.ACTIVITY_ID);
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(activity.getId()));

            SearchTemplateQuery query = QueryDataUtil.minimumQuery(activityFilter);

            query.setLimit(1);

            List<IObjectData> costs = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_DEALER_ACTIVITY_COST, query, Lists.newArrayList("_id", CommonFields.RECORD_TYPE));

            if (!CollectionUtils.isEmpty(costs)) {
                IObjectData cost = costs.get(0);
                switch (cost.getRecordType()) {
                    case CommonFields.LIFE_STATUS__NORMAL:
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_1));
                    case CommonFields.LIFE_STATUS__INEFFECTIVE:
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_2));
                    default:
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_SERVICE_3));
                }
            }
        }
    }

    @Override
    public void triggerCloseTPMActivity(String tenantId, String userId, String activityId, boolean needForce, boolean needTriggerApproval) {

        ActionContext context = new ActionContext(RequestContext.builder().tenantId(tenantId).user(User.builder().tenantId(tenantId).userId(userId).build()).build(),
                ApiNames.TPM_ACTIVITY_OBJ, "CloseTPMActivity");
        context.setAttribute("triggerFlow", needTriggerApproval);
        TPMActivityObjCloseTPMActivityAction.Arg arg = new TPMActivityObjCloseTPMActivityAction.Arg();
        arg.setDataId(activityId);
        arg.setIsConfirmWriteOff(needForce);
        serviceFacade.triggerAction(context, arg, TPMActivityObjCloseTPMActivityAction.Arg.class);
    }

    @Override
    public List<IObjectData> findActivityByStore(String tenantId, List<String> departmentIds, IObjectData store, List<String> includeActivityIds, List<String> activityTypeIds) {
        if (CollectionUtils.isEmpty(activityTypeIds)) {
            return new ArrayList<>();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setFilters(Lists.newArrayList());

        int index = 1;
        StringBuilder pattern = new StringBuilder();

        Filter activityStatusFilter = new Filter();
        activityStatusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        activityStatusFilter.setOperator(Operator.EQ);
        activityStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));
        query.getFilters().add(activityStatusFilter);
        pattern.append("  ").append(index++);

        if (!CollectionUtils.isEmpty(includeActivityIds)) {
            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(includeActivityIds);
            query.getFilters().add(idFilter);
            pattern.append(" and ").append(index++).append(" ");
        }

        if (!CollectionUtils.isEmpty(departmentIds)) {
            Filter departmentFilter = new Filter();
            departmentFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
            departmentFilter.setOperator(Operator.HASANYOF);
            departmentFilter.setFieldValues(departmentIds);
            query.getFilters().add(departmentFilter);

            pattern.append(" and ").append(index++).append(" ");
        }

        Filter storeFilter = new Filter();
        storeFilter.setFieldName(TPMActivityFields.DEALER_ID);
        storeFilter.setOperator(Operator.IS);
        storeFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().add(storeFilter);

        String dealerId = storeBusiness.findDealerId(tenantId, store);

        if (!Strings.isNullOrEmpty(dealerId)) {
            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            query.getFilters().add(dealerIdFilter);
            pattern.append(" and  ( ").append(index++).append(" or ").append(index++).append(" ) ");
        } else {
            pattern.append(" and ").append(index++);
        }


        if (!CollectionUtils.isEmpty(activityTypeIds) && !activityTypeIds.contains("all")) {
            Filter activityTypeFilter = new Filter();
            activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
            activityTypeFilter.setOperator(Operator.IN);
            activityTypeFilter.setFieldValues(activityTypeIds);
            query.getFilters().add(activityTypeFilter);
            pattern.append(" and  ").append(index);
        }

        query.setPattern(pattern.toString());
        List<IObjectData> activities = CommonUtils.queryAllDataInFields(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.CREATE_TIME, TPMActivityFields.DEALER_ID, TPMActivityFields.ACTIVITY_TYPE, TPMActivityFields.STORE_RANGE, TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, "reward_activity_tenant__c", TPMActivityFields.PRODUCT_RANGE, TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, TPMActivityFields.ACTIVATION_START_TIME, TPMActivityFields.ACTIVATION_END_TIME, TPMActivityFields.ACTIVATION_END_TIME, TPMActivityFields.IS_ALLOW_OUTER_CODE_SCAN));

        Map<String, Boolean> existsMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(tenantId, store.getId(), dealerId, activities, false, true);

        return activities.stream().filter(v -> existsMap.getOrDefault(v.getId(), false)).collect(Collectors.toList());
    }

    @Override
    public List<String> getDepartmentByStore(String tenantId, IObjectData store) {
        if (TPMGrayUtils.jumpDepartmentInRewardActivity(tenantId)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(store.getDataOwnDepartment())) {
            throw new RewardFmcgException("10011", I18N.text(I18NKeys.REWARD2_ACTIVITY_SERVICE_0));
        }
        return queryDepartmentAndAncestorIds(tenantId, store.getDataOwnDepartment().get(0));
    }

    private List<String> queryDepartmentAndAncestorIds(String tenantId, String departmentId) {
        GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        arg.setDepartmentId(Integer.parseInt(departmentId));
        GetDepartmentDtoResult result = departmentProviderService.getDepartmentDto(arg);
        List<String> ids = new ArrayList<>();
        ids.add(departmentId);
        log.info("result:{},departmentId:{}", result, departmentId);
        result.getDepartment().getAncestors().forEach(v -> ids.add(String.valueOf(v)));
        return ids;
    }


    public List<IObjectData> findObjByActivity(String tenantId, String id, String objApiName, String referenceFieldApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(referenceFieldApiName);
        activityTypeFilter.setFieldValues(Lists.newArrayList(id));
        activityTypeFilter.setOperator(Operator.EQ);

        query.setFilters(Lists.newArrayList(activityTypeFilter));
        query.setOrders(Lists.newArrayList());

        List<IObjectData> activityObjs = serviceFacade.findBySearchQuery(User.systemUser(tenantId), objApiName, query).getData();
        if (activityObjs == null) {
            return Lists.newArrayList();
        }
        return activityObjs;
    }

    @Override
    public List<IFilter> getObjAccountFiltersByActivity(String tenantId, int userId, String activityId) {
        List<IFilter> filters = Lists.newLinkedList();
        if (!Strings.isNullOrEmpty(activityId)) {
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(tenantId), activityId, ApiNames.TPM_ACTIVITY_OBJ);
            if (!Objects.isNull(activity)) {
                String dealerFieldApiName = storeBusiness.findDealerFieldApiName(tenantId);
                String dealerId = activity.get(TPMActivityFields.DEALER_ID, String.class);
                String customerType = activity.get(TPMActivityFields.CUSTOMER_TYPE, String.class, ActivityCustomerTypeEnum.DEALER_STORE.value());
                JSONObject storeRange = JSON.parseObject(activity.get(TPMActivityFields.STORE_RANGE, String.class));

                switch (storeRange.getString("type").toUpperCase()) {
                    case "CONDITION":
                        List<Wheres> tmpWhereList = JSON.parseArray(storeRange.getString("value"), Wheres.class);
                        List<IFilter> filtersForDealer = setAdditionFilterForDealer(tenantId, User.systemUser(tenantId), activity, dealerId, customerType, storeBusiness.findDealerRecordType(tenantId), dealerFieldApiName);
                        if (!CollectionUtils.isEmpty(tmpWhereList)) {
                            AtomicInteger count = new AtomicInteger(1);
                            if (!CollectionUtils.isEmpty(filtersForDealer)) {
                                Map<String, List<IFilter>> groupFilterMap = filtersForDealer.stream().collect(Collectors.groupingBy(IFilter::getFilterGroup));
                                for (List<IFilter> filterList : groupFilterMap.values()) {
                                    for (Wheres where : tmpWhereList) {
                                        List<IFilter> innerFilters = new LinkedList<>();
                                        innerFilters.addAll(filterDeepCopy(filterList));
                                        innerFilters.addAll(filterDeepCopy(where.getFilters()));
                                        innerFilters.forEach(filter -> filter.setFilterGroup(String.valueOf(count.get())));
                                        filters.addAll(innerFilters);
                                        count.getAndIncrement();
                                    }
                                }
                            } else {
                                for (Wheres where : tmpWhereList) {
                                    List<IFilter> innerFilters = Lists.newArrayList();
                                    innerFilters.addAll(where.getFilters());
                                    innerFilters.forEach(filter -> filter.setFilterGroup(String.valueOf(count.get())));
                                    filters.addAll(innerFilters);
                                    count.getAndIncrement();
                                }
                            }
                        } else {
                            filters = filtersForDealer;
                        }
                        break;
                    case "FIXED":
                        List<String> storeIds = queryStoreIds(tenantId, activityId);
                        log.info("fixed store ids : {}", storeIds);

                        if (!CollectionUtils.isEmpty(storeIds)) {
                            Filter storeIdFilter = new Filter();
                            storeIdFilter.setFieldName(CommonFields.ID);
                            storeIdFilter.setOperator(Operator.IN);
                            storeIdFilter.setFieldValues(storeIds);
                            filters.add(storeIdFilter);
                        }
                        break;
                    case "ALL":
                        filters = setAdditionFilterForDealer(tenantId, User.systemUser(tenantId), activity, dealerId, customerType, storeBusiness.findDealerRecordType(tenantId), dealerFieldApiName);
                        break;
                    default:
                        break;
                }
            }
        }

        return filters;
    }

    @Override
    public List<IFilter> getOrderGoodsMeetingByAccountId(int tenantId, int userId, String accountId) {
        List<Integer> departmentIds = organizationService.getDepartmentIds(tenantId, userId);
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        log.info("departments : {}", departmentIds);
        User user = new User(String.valueOf(tenantId), String.valueOf(userId));

        List<ActivityTypePO> activityTypePOS = activityTypeDAO.queryByTypeTemplateId(String.valueOf(tenantId), OrderGoodsContents.ORDER_GOODS_TEMPLATE_ID);
        List<String> activityTypeList = activityTypePOS.stream().map(o -> o.getId().toString()).collect(Collectors.toList());
        List<IObjectData> tpmActivityObjList = queryTPMActivityOfOrderGoodsMeeting(user, accountId, departmentIds, activityTypeList);
        log.info("activities ids: {}", tpmActivityObjList.stream().map(IObjectData::getId).collect(Collectors.toList()));

        Map<String, Boolean> activityIdToJudgeResult = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(user.getTenantId(), accountId,
                null, tpmActivityObjList, false, false);
        List<String> activityIdList = activityIdToJudgeResult.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(CommonFields.ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(activityIdList.isEmpty() ? Lists.newArrayList("0") : activityIdList);
        return Lists.newArrayList(activityIdFilter);
    }

    private List<IObjectData> queryTPMActivityOfOrderGoodsMeeting(User user, String storeId, List<Integer> departmentIds, List<String> activityTypeList) {
        User systemUser = User.systemUser(user.getTenantId());
        IObjectDescribe activityDescribe = serviceFacade.findDescribeAndLayout(systemUser, ApiNames.TPM_ACTIVITY_OBJ,
                false, null).getObjectDescribe();
        ISearchTemplate activitySearchTemplate = serviceFacade.findSearchTemplateByIdAndType(systemUser, "", ApiNames.TPM_ACTIVITY_OBJ, "All");
        boolean isAdminRequest = serviceFacade.isAdmin(user);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setFilters(Lists.newArrayList());
        query.setSearchSource("db");

        long nowTime = System.currentTimeMillis();
        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowTime)));
        query.getFilters().add(beginDateFilter);

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(nowTime)));
        query.getFilters().add(endDateFilter);

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        query.getFilters().add(lifeStatusFilter);

        Filter closeStatusFilter = new Filter();
        closeStatusFilter.setFieldName(TPMActivityFields.CLOSE_STATUS);
        closeStatusFilter.setOperator(Operator.EQ);
        closeStatusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.CLOSE_STATUS__UNCLOSED));
        query.getFilters().add(closeStatusFilter);

        Filter customerTypeFilter = new Filter();
        customerTypeFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeFilter.setOperator(Operator.NEQ);
        customerTypeFilter.setFieldValues(Lists.newArrayList(ActivityCustomerTypeEnum.BRAND.value()));
        query.getFilters().add(customerTypeFilter);

        Filter customerTypeNullFilter = new Filter();
        customerTypeNullFilter.setFieldName(TPMActivityFields.CUSTOMER_TYPE);
        customerTypeNullFilter.setOperator(Operator.IS);
        customerTypeNullFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().add(customerTypeNullFilter);

        int number = 6;
        StringBuilder pattern = new StringBuilder(" 1 and 2 and 3 and 4 and (5 or 6) ");
        if (!CollectionUtils.isEmpty(activityTypeList) && !activityTypeList.contains("all")) {
            if (activityTypeList.size() == 1) {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.EQ);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            } else {
                Filter activityType = new Filter();
                activityType.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
                activityType.setOperator(Operator.IN);
                activityType.setFieldValues(activityTypeList);
                query.getFilters().add(activityType);
            }
            pattern.append(" and ").append(++number).append(" ");
        }

        pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
        Filter dealerIdEqualStoreIdFilter = new Filter();
        dealerIdEqualStoreIdFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEqualStoreIdFilter.setOperator(Operator.EQ);
        dealerIdEqualStoreIdFilter.setFieldValues(Lists.newArrayList(storeId));

        Filter dealerIdEmptyFilter = new Filter();
        dealerIdEmptyFilter.setFieldName(TPMActivityFields.DEALER_ID);
        dealerIdEmptyFilter.setOperator(Operator.IS);
        dealerIdEmptyFilter.setFieldValues(Lists.newArrayList());
        query.getFilters().addAll(Lists.newArrayList(dealerIdEmptyFilter, dealerIdEqualStoreIdFilter));

        Filter departmentRangeFilter = new Filter();
        departmentRangeFilter.setFieldName(TPMActivityFields.DEPARTMENT_RANGE);
        departmentRangeFilter.setOperator(Operator.IN);
        departmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

        Filter multiDepartmentRangeFilter = new Filter();
        multiDepartmentRangeFilter.setFieldName(TPMActivityFields.MULTI_DEPARTMENT_RANGE);
        multiDepartmentRangeFilter.setOperator(Operator.HASANYOF);
        multiDepartmentRangeFilter.setFieldValues(departmentIds.stream().map(Object::toString).collect(Collectors.toList()));

        if (I_DESCRIBE_CACHE_SERVICE.isExistField(user.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, TPMActivityFields.DEPARTMENT_RANGE)) {
            pattern.append(" and (").append(++number).append(" or ").append(++number).append(" ) ");
            query.getFilters().addAll(Lists.newArrayList(departmentRangeFilter, multiDepartmentRangeFilter));
        } else {
            pattern.append(" and ").append(++number).append(" ");
            query.getFilters().add(multiDepartmentRangeFilter);
        }

        query.setPattern(pattern.toString());
        SearchQueryUtil.handleDataPrivilege(dataPrivilegeService, user, query, activityDescribe, activitySearchTemplate, isAdminRequest);
        return CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_ACTIVITY_OBJ, query);
    }

    @NotNull
    private List<IFilter> filterDeepCopy(List<IFilter> filters) {
        List<IFilter> newFilterList = new ArrayList<>();
        filters.forEach(filter -> newFilterList.add(filter.copy()));
        return newFilterList;
    }

    private List<IFilter> setAdditionFilterForDealer(String tenantId, User user, IObjectData activity, String dealerId, String customerType, List<String> dealerRecordType, String dealerFieldApiName) {
        List<IFilter> filtersForDealer = new ArrayList<>();
        boolean needAnd = true;
        if (Strings.isNullOrEmpty(dealerId)) {
            needAnd = setUnifiedFilter(tenantId, user, activity, dealerFieldApiName, filtersForDealer, customerType);
        } else {
            Filter dealerSelfFilter = new Filter();
            dealerSelfFilter.setFieldName(CommonFields.ID);
            dealerSelfFilter.setOperator(Operator.EQ);
            dealerSelfFilter.setFieldValues(Lists.newArrayList(dealerId));
            dealerSelfFilter.setFilterGroup("1");
            filtersForDealer.add(dealerSelfFilter);

            Filter dealerIdFilter = new Filter();
            dealerIdFilter.setFieldName(dealerFieldApiName);
            dealerIdFilter.setOperator(Operator.EQ);
            dealerIdFilter.setFieldValues(Lists.newArrayList(dealerId));
            dealerIdFilter.setFilterGroup("2");
            filtersForDealer.add(dealerIdFilter);
        }

        //添加参与客户业务类型校验
        if (ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.IN);
            recordTypeFilter.setFieldValues(dealerRecordType);
            addAndFilterForDealer(recordTypeFilter, filtersForDealer);

        } else if (ActivityCustomerTypeEnum.STORE.value().equals(customerType)) {
            Filter recordTypeFilter = new Filter();
            recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
            recordTypeFilter.setOperator(Operator.NIN);
            recordTypeFilter.setFieldValues(dealerRecordType);
            addAndFilterForDealer(recordTypeFilter, filtersForDealer);
        }
        return filtersForDealer;
    }

    void addAndFilterForDealer(Filter filter, List<IFilter> filtersForDealer) {
        if (CollectionUtils.isEmpty(filtersForDealer)) {
            filter.setFilterGroup("1");
            filtersForDealer.add(filter);
        } else {
            List<String> groups = filtersForDealer.stream().map(IFilter::getFilterGroup).collect(Collectors.toList());
            for (String group : groups) {
                filter.setFilterGroup(group);
                filtersForDealer.add(filter);
            }
        }
    }

    private boolean setUnifiedFilter(String tenantId, User user, IObjectData activity, String dealerFieldApiName, List<IFilter> filtersForDealer, String customerType) {
        String unifiedActivityId = activity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class);
        if (!Strings.isNullOrEmpty(unifiedActivityId)) {
            IObjectData unifiedActivity = serviceFacade.findObjectDataIncludeDeleted(user, unifiedActivityId, ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            Set<String> dealerIds = unifiedActivityCommonLogicBusiness.getDealerIdsOfUnifiedActivity(tenantId, unifiedActivity);
            if (CollectionUtils.isEmpty(dealerIds)) {
                dealerIds.add("no_dealer");
            }
            if (!dealerIds.contains("-1")) {
                if (ActivityCustomerTypeEnum.DEALER_STORE.value().equals(customerType) || ActivityCustomerTypeEnum.DEALER.value().equals(customerType)) {
                    Filter dealerIdFilter = new Filter();
                    dealerIdFilter.setFieldName(dealerFieldApiName);
                    dealerIdFilter.setOperator(Operator.IN);
                    dealerIdFilter.setFieldValues(new ArrayList<>(dealerIds));
                    dealerIdFilter.setFilterGroup("1");
                    filtersForDealer.add(dealerIdFilter);
                }
                Filter dealerIdSelfFilter = new Filter();
                dealerIdSelfFilter.setFieldName(CommonFields.ID);
                dealerIdSelfFilter.setOperator(Operator.IN);
                dealerIdSelfFilter.setFieldValues(new ArrayList<>(dealerIds));
                dealerIdSelfFilter.setFilterGroup("2");
                filtersForDealer.add(dealerIdSelfFilter);
                return true;
            }
        }
        return false;
    }

    private List<String> queryStoreIds(String tenantId, String activityId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        query.setFilters(Lists.newArrayList(activityIdFilter));

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_STORE_OBJ, query)
                .stream().map(m -> m.get(TPMActivityStoreFields.STORE_ID, String.class))
                .collect(Collectors.toList());
    }

}
