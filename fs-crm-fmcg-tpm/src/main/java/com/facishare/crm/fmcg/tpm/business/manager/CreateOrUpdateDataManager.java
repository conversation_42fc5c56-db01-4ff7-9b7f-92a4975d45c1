package com.facishare.crm.fmcg.tpm.business.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDetailFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDisplayImgFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.abstraction.AbstractDisPlayReportBaseService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityDisplayImgDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class CreateOrUpdateDataManager extends AbstractDisPlayReportBaseService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ProofPeriodManager proofPeriodManager;
    @Resource
    private QueryDataManager queryDataManager;
    @Resource
    private ActivityDisplayImgDAO activityDisplayImgDAO;

    public void updateProofFail(String tenantId, String proofId) {
        try {
            IObjectData proofObj = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), proofId, ApiNames.TPM_ACTIVITY_PROOF_OBJ);
            Map<String, Object> updateMap = new HashMap<>();
            updateMap.put(TPMActivityProofFields.AI_IDENTIFY_STATUS, TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFY_FAILED);
            serviceFacade.updateWithMap(User.systemUser(tenantId), proofObj, updateMap);
        } catch (Exception ex) {
            log.error("更新举证失败，proofId: {}", proofId, ex);
        }

    }

    public void updateProofPeriod(TPMDisplayReportService.AIProcessingContext context) {
        try {
            String proofPeriodDetailId = context.getProofObj().get(TPMActivityProofFields.PROOF_TIME_PERIOD_DETAIL_ID, String.class);
            if (StringUtils.isNotBlank(proofPeriodDetailId)) {
                IObjectData objectData = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), proofPeriodDetailId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ);
                proofPeriodManager.processOnePeriodDetail(context.getTenantId(), objectData);
            }
        } catch (Exception ex) {
            log.error("更新举证时段失败，proofId: {}", context.getProofId(), ex);
        }

    }


    public void updateProof(TPMDisplayReportService.AIProcessingContext context) {
        log.info("updateProof proofId:{}", context.getProofId());
        List<IObjectData> proofDisplayImgs = context.getProofDisplayImgs();
        // 申诉重拍，更新举证需要查所有的陈列举证图片
        if (context.isRetake()) {
            proofDisplayImgs = queryDataManager.queryTPMActivityProofDisplayImgsByProofIds(context.getTenantId(), Lists.newArrayList(context.getProofId()));
        }
        if (CollectionUtils.isEmpty(proofDisplayImgs)) {
            return;
        }

        List<String> systemJudgmentStatusList = proofDisplayImgs.stream()
                .filter(img -> StringUtils.isNotBlank(img.get(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, String.class)))
                .map(img -> img.get(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, String.class))
                .collect(Collectors.toList());
        String systemJudgmentStatus = super.getProofMasterSystemJudgmentStatus(context.getTenantId(), systemJudgmentStatusList);

        List<ActivityDisplayImgPO> errorRecordList = activityDisplayImgDAO.findByProofIds(context.getTenantId(), Lists.newArrayList(context.getProofId()));
        String aiIdentifyStatus = CollectionUtils.isEmpty(errorRecordList)
                ? TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED : TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFY_FAILED;

        IObjectData proofObj = serviceFacade.findObjectDataIgnoreAll(
                User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build()
                , context.getProofId()
                , ApiNames.TPM_ACTIVITY_PROOF_OBJ
        );
        proofObj.set(TPMActivityProofFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
        proofObj.set(TPMActivityProofFields.AI_IDENTIFY_STATUS, aiIdentifyStatus);

        serviceFacade.updateObjectData(User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build(), proofObj);
    }

    public void updateDisplayImgStatus(TPMDisplayReportService.AIProcessingContext context) {
        List<IObjectData> proofDetails = context.getProofDetails();
        Map<String, List<IObjectData>> displayFormIdActivityProofDetailsMap = proofDetails.stream().collect(Collectors.groupingBy(k -> k.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID, String.class)));
        List<IObjectData> proofDisplayImgs;
        if (context.isRetake()) {
            proofDisplayImgs = context.getProofDisplayImgs();
        } else {
            proofDisplayImgs = queryDataManager.queryTPMActivityProofDisplayImgsByProofIds(context.getTenantId(), Lists.newArrayList(context.getProofId()));
        }
        Set<String> errorActivityDisplayImgIds = new HashSet<>();
        List<ActivityDisplayImgPO> errorRecordList = activityDisplayImgDAO.findByProofIds(context.getTenantId(), Lists.newArrayList(context.getProofId()));
        if (CollectionUtils.isNotEmpty(errorRecordList)) {
            errorActivityDisplayImgIds = errorRecordList.stream().map(ActivityDisplayImgPO::getActivityDisplayImgId).collect(Collectors.toSet());
        }

        Map<String, TPMDisplayReportService.AIDetectionResults> aiResultsMap = context.getAiResultsMapGroupByProofDisplayImgId();
        log.info("updateDisplayImgStatus proofId:{},aiResultsMap:{},proofDisplayImgs ids:{}", context.getProofId(), JSON.toJSONString(aiResultsMap), proofDisplayImgs.stream().map(DBRecord::getId).collect(Collectors.toList()));
        for (IObjectData img : proofDisplayImgs) {
            String displayFormId = img.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class);
            if (StringUtils.isNotBlank(displayFormId)) {
                List<IObjectData> activityProofDetails = displayFormIdActivityProofDetailsMap.get(displayFormId);
                if (CollectionUtils.isNotEmpty(activityProofDetails)) {
                    List<String> systemJudgmentStatusList = activityProofDetails.stream()
                            .map(activityProofDetail -> activityProofDetail.get(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, String.class))
                            .collect(Collectors.toList());

                    String systemJudgmentStatus = super.getProofImgSystemJudgmentStatus(systemJudgmentStatusList);
                    if (StringUtils.isNotBlank(systemJudgmentStatus)) {
                        img.set(TPMActivityProofDisplayImgFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
                    }
                    String auditStatus = (StringUtils.isBlank(systemJudgmentStatus) || TPMActivityProofDisplayImgFields.NOT_DISPLAY_SYSTEM_JUDGMENT_STATUS.equals(systemJudgmentStatus)) ? TPMActivityProofDisplayImgFields.FAIL_STATUS : systemJudgmentStatus;
                    img.set(TPMActivityProofDisplayImgFields.AUDIT_STATUS, auditStatus);
                    String aiIdentifyStatus = errorActivityDisplayImgIds.contains(img.getId()) ? TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS_IDENTIFY_FAILED : TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS_IDENTIFIED;
                    img.set(TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS, aiIdentifyStatus);
                }
            }

            TPMDisplayReportService.AIDetectionResults aiResults = aiResultsMap.get(img.getId());
            if (Objects.nonNull(aiResults)) {
                img.set(TPMActivityProofDisplayImgFields.AI_DISPLAY_FORM_ID, aiResults.getAiDisplayFormIdList());
                img.set(TPMActivityProofDisplayImgFields.AI_LAYER_NUMBER, aiResults.getAiLayerNumber());
                img.set(TPMActivityProofDisplayImgFields.AI_GROUP_NUMBER, aiResults.getAiGroupNumber());
                img.set(TPMActivityProofDisplayImgFields.AI_VISIBLE_NUMBER, aiResults.getAiVisibleNumber());
            }
            serviceFacade.updateObjectData(User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build(), img);
        }

        context.setProofDisplayImgs(proofDisplayImgs);
    }



    public void updateRetakeProofDetail(List<IObjectData> proofDetails, TPMDisplayReportService.AIProcessingContext context) {
        List<String> displayFormIds = context.getProofDisplayImgs().stream().map(v -> v.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID, String.class)).collect(Collectors.toList());

        String proofId = context.getProofId();
        if (CollectionUtils.isNotEmpty(displayFormIds)) {
            // 根据陈列图片的数据 id ，去查询举证项目的数据
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setLimit(-1);
            query.setOffset(0);
            query.setNeedReturnQuote(false);
            query.setNeedReturnCountNum(false);
            query.setSearchSource("db");

            Filter proofIdFilter = new Filter();
            proofIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
            proofIdFilter.setOperator(Operator.EQ);
            proofIdFilter.setFieldValues(Lists.newArrayList(proofId));

            Filter proofDisplayImgIdFilter = new Filter();
            proofDisplayImgIdFilter.setFieldName(TPMActivityProofDetailFields.DISPLAY_FORM_ID);
            proofDisplayImgIdFilter.setOperator(Operator.IN);
            proofDisplayImgIdFilter.setFieldValues(displayFormIds);

            query.setFilters(Lists.newArrayList(proofIdFilter, proofDisplayImgIdFilter));

            // ACTIVITY_PROOF_ID + DISPLAY_FORM_ID + ACTIVITY_ITEM_ID 共同组成一组举证项目
            List<IObjectData> proofItemDetails = QueryDataUtil.find(serviceFacade, context.getTenantId(), ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query);
            if (CollectionUtils.isNotEmpty(proofItemDetails)) {
                // 根据陈列图片数据 Id 和活动项目 id 去更新数据
                for (IObjectData proofItemDetail : proofItemDetails) {
                    String activityItemId = proofItemDetail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);
                    String displayFormId = proofItemDetail.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID, String.class);
                    String activityProofId = proofItemDetail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID, String.class);
                    // 从 proofDetails 里面获取跟这个 activityItemId 和 displayFormId 和 activityProofId 值相同的数据对象
                    IObjectData proofDetail = proofDetails.stream()
                            .filter(detail -> detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class).equals(activityItemId) &&
                                    detail.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID, String.class).equals(displayFormId) &&
                                    detail.get(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID, String.class).equals(activityProofId))
                            .findFirst()
                            .orElse(null);
                    if (Objects.nonNull(proofDetail)) {
                        // 更新举证项目的数据
                        proofItemDetail.set(TPMActivityProofDetailFields.AI_FACE_NUMBER, proofDetail.get(TPMActivityProofDetailFields.AI_FACE_NUMBER, String.class));
                        proofItemDetail.set(TPMActivityProofDetailFields.AI_NUMBER, proofDetail.get(TPMActivityProofDetailFields.AI_NUMBER, String.class));
                        proofItemDetail.set(TPMActivityProofDetailFields.AI_SKU_NUMBER, proofDetail.get(TPMActivityProofDetailFields.AI_SKU_NUMBER, String.class));
                        proofItemDetail.set(TPMActivityProofDetailFields.AI_VISIBLE_NUMBER, proofDetail.get(TPMActivityProofDetailFields.AI_VISIBLE_NUMBER, String.class));

                    }
                }
                List<String> updateFields = Lists.newArrayList(TPMActivityProofDetailFields.AI_FACE_NUMBER, TPMActivityProofDetailFields.AI_NUMBER, TPMActivityProofDetailFields.AI_SKU_NUMBER, TPMActivityProofDetailFields.AI_VISIBLE_NUMBER);
                // 更新举证项目的数据
                serviceFacade.batchUpdateByFields(User.builder().tenantId(context.getTenantId()).userId(context.getUserId()).build(), proofItemDetails, updateFields);
            }

        }

    }


}
