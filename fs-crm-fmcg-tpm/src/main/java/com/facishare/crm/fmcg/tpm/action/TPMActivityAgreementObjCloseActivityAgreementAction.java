package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.business.BudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActionModeType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import javax.swing.*;
import java.util.*;

/**
 * Author: wuyx
 * Date: 2024/3/18 14:26
 */
public class TPMActivityAgreementObjCloseActivityAgreementAction extends BaseObjectApprovalAction<TPMActivityAgreementObjCloseActivityAgreementAction.Arg, TPMActivityAgreementObjCloseActivityAgreementAction.Result> implements TransactionService<TPMActivityAgreementObjCloseActivityAgreementAction.Arg, TPMActivityAgreementObjCloseActivityAgreementAction.Result> {

    private IObjectData objectData;
    private IObjectData dbData;
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);
    private static final BudgetConsumeV2Service budgetConsumeV2Service = SpringUtil.getContext().getBean(BudgetConsumeV2Service.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CLOSE_ACTIVITY_AGREEMENT.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected Result doAct(Arg arg) {
        Result validateResult = validateAgreement();
        if (Objects.nonNull(validateResult)) {
            return validateResult;
        }

        if (this.needTriggerApprovalFlow()) {
            Map<String, Map<String, Object>> changeData = Maps.newHashMap();
            Map<String, Object> changeDatum = Maps.newHashMap();
            changeDatum.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE);
            changeData.put(this.objectData.getId(), changeDatum);

            Map<String, ApprovalFlowStartResult> startApprovalResult = this.startApprovalFlow(
                    Lists.newArrayList(this.objectData),
                    ApprovalFlowTriggerType.CLOSE_ACTIVITY_AGREEMENT,
                    changeData,
                    Maps.newHashMap(),
                    null
            );

            if (MapUtils.isNotEmpty(startApprovalResult)
                    && startApprovalResult.containsKey(this.objectData.getId())
                    && startApprovalResult.get(this.objectData.getId()).isSuccess()) {

                Map<String, Object> update = new HashMap<>();
                update.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
                serviceFacade.updateWithMap(actionContext.getUser(), this.objectData, update);

                return Result.success(objectData);
            }
        }
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CLOSE_ACTIVITY_AGREEMENT.getButtonApiName();
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
            dbData = ObjectDataExt.of(objectData).copy();
        }
    }

    @Override
    public Result doActTransaction(Arg arg) {
        String agreementId = arg.getDataId();
        IObjectData agreement = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        Map<String, Object> update = new HashMap<>();
        long closeTime = System.currentTimeMillis();

        update.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE);
        update.put(TPMActivityAgreementFields.CLOSE_TIME, closeTime);
        this.objectData.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE);
        this.objectData.set(TPMActivityAgreementFields.CLOSE_TIME, closeTime);

        serviceFacade.updateWithMap(actionContext.getUser(), agreement, update);
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, this.objectDescribe, this.objectData, update, dbData);
        if (actionContext.getTenantId().equals("84931") || actionContext.getTenantId().equals("85494")){
            //测试终止协议的 中间释放。
            budgetConsumeV2Service.middleRelease(User.systemUser(actionContext.getTenantId()), agreement.getDescribeApiName(), agreement.getId(), ActionModeType.AGREEMENT_ABANDON.getKey());
        }

        return Result.success(objectData);
    }

    private Result validateAgreement() {
        IObjectData agreement = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        if (Objects.isNull(agreement)) {
            throw new ValidateException("activity agreement not found.");
        }

        String agreementStatus = (String) agreement.get(TPMActivityAgreementFields.AGREEMENT_STATUS);
        if (TPMActivityAgreementFields.AGREEMENT_STATUS__CLOSE.equals(agreementStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_CLOSE_ACTIVITY_AGREEMENT_ACTION_0));
        }

        if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(agreementStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_CLOSE_ACTIVITY_AGREEMENT_ACTION_1));
        }

        String lifeStatus = agreement.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_CLOSE_ACTIVITY_AGREEMENT_ACTION_2));
        }

        // 确认弹框
        if (!Boolean.TRUE.equals(arg.needConfirm)) {
            return Result.showTips(agreement, true, I18N.text(I18NKeys.SHOWTIPS_ACTIVITY_AGREEMENT_OBJ_CLOSE_ACTIVITY_AGREEMENT_ACTION_0));
        }
        return null;
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;

        @SerializedName("need_confirm")
        @JSONField(name = "need_confirm")
        @JsonProperty("need_confirm")
        private Boolean needConfirm;
    }


    @Data
    public static class Result {

        private ObjectDataDocument objectData;

        private String tips;

        private Boolean isShowTips;

        public static Result showTips(IObjectData objectData, Boolean isShowTips, String tips) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setIsShowTips(isShowTips);
            result.setTips(tips);
            return result;
        }

        public static Result success(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            result.setIsShowTips(false);
            result.setTips(null);
            return result;
        }
    }
}
