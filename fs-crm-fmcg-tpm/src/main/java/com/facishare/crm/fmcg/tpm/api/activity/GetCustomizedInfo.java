package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface GetCustomizedInfo {

    @Data
    @ToString
    class Arg implements Serializable {


        @SerializedName("object_api_name")
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("activity_ids")
        @J<PERSON><PERSON>ield(name = "activity_ids")
        @JsonProperty("activity_ids")
        private List<String> activityIds;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<GroupCustomizedInfoVO> data;
    }

    @Data
    @ToString
    @Builder
    class GroupCustomizedInfoVO implements Serializable {

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        private List<CustomizedInfoVO> data;
    }

    @Data
    @ToString
    class CustomizedInfoVO implements Serializable {

        private String label;

        private String value;
    }

}