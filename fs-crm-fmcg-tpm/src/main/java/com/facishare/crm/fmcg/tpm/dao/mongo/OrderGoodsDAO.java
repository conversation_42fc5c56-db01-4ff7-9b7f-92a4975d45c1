package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.OrderGoodsPO;
import org.mongodb.morphia.query.Query;

/**
 * <AUTHOR>
 * @date 2024/11/26 20:59
 */
public class OrderGoodsDAO extends UniqueIdBaseDAO<OrderGoodsPO> {

    protected OrderGoodsDAO(Class<OrderGoodsPO> clazz) {
        super(clazz);
    }


    public OrderGoodsPO find(String tenantId, String objectId, String objectApiName) {
        Query<OrderGoodsPO> query = mongoContext.createQuery(OrderGoodsPO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(OrderGoodsPO.F_REF_OBJECT_ID).equal(objectId);
        query.field(OrderGoodsPO.F_REF_OBJECT_API_NAME).equal(objectApiName);
        query.order(OrderGoodsPO.F_LAST_UPDATE_TIME);
        query.limit(1);
        return query.get();
    }
}
