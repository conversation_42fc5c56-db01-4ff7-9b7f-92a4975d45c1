package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class OrderGoodsPromotionPolicyRuleVO extends CommonVO implements Serializable {

    private long version;

    private long order;

    @JSONField(name = "object_apiName")
    @JsonProperty(value = "object_apiName")
    @SerializedName("object_apiName")
    private String objectApiName;

    @JSONField(name = "object_id")
    @JsonProperty(value = "object_id")
    @SerializedName("object_id")
    private String objectId;

    @JSONField(name = "product_gift_data")
    @JsonProperty(value = "product_gift_data")
    @SerializedName("product_gift_data")
    private String productGiftData;

    @J<PERSON>NField(name = "parent_object_id")
    @JsonProperty(value = "parent_object_id")
    @SerializedName("parent_object_id")
    private String parentObjectId;

    @JSONField(name = "order_goods_id")
    @JsonProperty(value = "order_goods_id")
    @SerializedName("order_goods_id")
    private String orderGoodsId;
}
