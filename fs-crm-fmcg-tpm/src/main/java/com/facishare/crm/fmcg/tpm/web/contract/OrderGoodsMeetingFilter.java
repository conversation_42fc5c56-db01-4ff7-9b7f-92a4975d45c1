package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface OrderGoodsMeetingFilter {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "account_id")
        @JsonProperty(value = "account_id")
        @SerializedName("account_id")
        private String accountId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<IFilter> filters;
    }
}