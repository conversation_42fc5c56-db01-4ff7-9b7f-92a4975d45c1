package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.google.common.collect.Lists;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * author: wuyx
 * description:
 * createTime: 2023/3/20 17:58
 */

public enum ConfigEnum {

    DEALER_RECORD_TYPE("dealer_record_type", Lists.newArrayList("dealer__c")),
    STORE_DEALER("store_dealer", "dealer_id"),
    REWARD_TAG("reward_tag", Lists.newArrayList(new RewardTagEntity("本企业业代激励","#FF4A66","1"),new RewardTagEntity("下游业代激励","#FF522A","2"),new RewardTagEntity("门店激励","#FF8000","3"))),
    ACTIVATED_SCAN_CODE_ACTION("activated_scan_code_action", Lists.newArrayList("STORE_STOCK_CHECK")),
    APL_DISPLAY_CUSTOMIZED_INFORMATION("apl_display_customized_information","");

    private static final Map<String, Object> innerMap = Stream.of(ConfigEnum.values()).collect(Collectors.toMap(ConfigEnum::key, ConfigEnum::value));

    ConfigEnum(String key, Object value) {
        this.key = key;
        this.value = value;
    }

    private String key;
    private Object value;

    public String key() {
        return this.key;
    }

    public Object value() {
        return this.value;
    }


    public static Map<String, Object> getDefault() {
        return innerMap;
    }
}
