package com.facishare.crm.fmcg.tpm.reward.handler;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.ActivityService;
import com.facishare.crm.fmcg.tpm.business.FmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BizCodeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.dto.StockCheckReward;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.lock.DistributedLock;
import com.facishare.crm.fmcg.tpm.web.contract.GetConfig;
import com.facishare.crm.fmcg.tpm.web.service.ConfigService;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/6/12 18:05
 */
@Component
@Slf4j
public class StockCheckRewardHandler implements ActivityRewardHandler<StockCheckReward.Arg, StockCheckReward.Result> {

    @Resource
    private DistributedLock distributedLock;

    @Resource
    private ServiceFacade serviceFacade;

    @Autowired
    private ConfigService configService;

    @Resource
    private FmcgSerialNumberService fmcgSerialNumberService;

    @Resource
    private ActivityService activityService;

    @Resource
    protected ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    private BizCodeDAO bizCodeDAO;


    public static final String LOCK_KEY = "StockCheckRewardHandler:%s:%s";


    /**
     * 先按码进行激活，存在就不激活了，接着按门店库存盘点的关联对象相关的码做盘点核销 此时没有激活的码做激活，激活过的码且在这个门店在库做更新，不在这门店或出库的不处理，不在盘点列表且是这家门店激活的做出库，门店必须一致
     *
     * @param arg
     * @return
     */
    @Override
    public StockCheckReward.Result handle(StockCheckReward.Arg arg) {
        log.info("arg:{}", arg);
        StockCheckReward.Result result = new StockCheckReward.Result();
        if (!TPMGrayUtils.isSupportStockCheckReward(arg.getUpperTenantId())) {
            result.setStatus(3);
            return result;
        }
        if (!judgeExistsObject(arg.getUpperTenantId(), Lists.newArrayList(ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ))) {
            log.warn("对象不存在。");
            return result;
        }
        List<String> activationActionList = getActivationActionList(arg.getUpperTenantId());
        if (Strings.isNullOrEmpty(arg.getStoreCheckId())) {
            User systemUser = User.systemUser(arg.getUpperTenantId());
            IObjectData triggerStatusObj = serviceFacade.findObjectData(systemUser, arg.getSerialNumberStatusId(), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ);
            String actionId = triggerStatusObj.get(FMCGSerialNumberStatusFields.ACTION_ID, String.class);
            String actionUniqueId = fmcgSerialNumberService.getActionUniqueIdByActionId(arg.getUpperTenantId(), actionId);

            if (!activationActionList.contains(actionUniqueId)) {
                log.info("不需要激活的动作，actionUniqueId:{}", actionUniqueId);
                return result;
            }

            String serialNumberId = triggerStatusObj.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
            String storeId = triggerStatusObj.get(FMCGSerialNumberStatusFields.ACCOUNT_ID, String.class);
            IObjectData store = serviceFacade.findObjectData(systemUser, storeId, ApiNames.ACCOUNT_OBJ);
            if (!actionUniqueId.equals(ScanCodeActionConstants.STORE_STOCK_CHECK)) {
                dealOtherActive(arg, activationActionList, actionUniqueId, store, result, serialNumberId, triggerStatusObj);
            }
        } else {

            IObjectData masterData = serviceFacade.findObjectData(User.systemUser(arg.getDataTenantId()), arg.getStoreCheckId(), "StoreStockCheck__c");
            IObjectData store = serviceFacade.findObjectData(User.systemUser(arg.getUpperTenantId()), masterData.get("account_id__c", String.class), ApiNames.ACCOUNT_OBJ);
            String personApiName;
            String personId;
            if (CollectionUtils.isNotEmpty(masterData.getOutOwner())) {
                personApiName = ApiNames.PUBLIC_EMPLOYEE_OBJ;
                personId = String.valueOf(masterData.getOutOwner().get(0));
            } else {
                personApiName = ApiNames.PERSONNEL_OBJ;
                personId = masterData.getCreatedBy();
            }
            dealVerify(arg.getUpperTenantId(), activationActionList, store, result, arg.getDataTenantId(), arg.getStoreCheckId(), personApiName, personId);
        }

        return result;
    }

    private void dealVerify(String upperTenantId, List<String> activationActionList, IObjectData store, StockCheckReward.Result result, String currentTenantId, String relatedMasterId, String personApiName, String personId) {
        String lockKey = String.format(LOCK_KEY, currentTenantId, relatedMasterId);
        distributedLock.executeOnlyOneThread(lockKey, data -> {
            List<IObjectData> details = getStoreStockCheckDetails(currentTenantId, relatedMasterId);
            BizCodePO po = getBizCodePo(currentTenantId, lockKey, details.stream().map(DBRecord::getId).collect(Collectors.toList()));
            if (po.getStatus() == BizCodeStatusEnum.USED.code()) {
                log.warn("已经处理过。");
                return result;
            }
            List<String> serialNumberIds = Lists.newArrayList();
            details.stream().map(v -> v.get("unique_product_code_combination__c", List.class, new ArrayList<>())).forEach(v -> v.forEach(id -> serialNumberIds.add(id.toString())));
            boolean existsActivity = existsActivity(upperTenantId, store);
            //激活
            if (existsActivity && activationActionList.contains(ScanCodeActionConstants.STORE_STOCK_CHECK)) {
                List<IObjectData> activatedRecords = queryActivatedRecordBySN(upperTenantId, serialNumberIds);
                Set<String> activatedSNIds = activatedRecords.stream().map(v -> v.get(ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID, String.class)).collect(Collectors.toSet());
                Map<String, IObjectData> needActivatedSerialNumberMap = getNeedActivatedSerialNumberMap(upperTenantId, serialNumberIds, activatedSNIds);
                needActivatedSerialNumberMap.forEach((id, serialNumber) -> {
                    distributedLock.executeByLock(String.format(LOCK_KEY, upperTenantId, id), () -> {
                        IObjectData record = getActivatedRecord(upperTenantId, id);
                        if (record == null) {
                            String productId = serialNumber.get(FMCGSerialNumberFields.PRODUCT_ID, String.class);
                            createActivationRecord(upperTenantId, id, store, details.get(0).getCreateTime(), productId);
                        }
                    });
                });
            }
            //核销
            if (existsActivity) {
                List<IObjectData> inStatusRecords = queryActivatedRecordByStore(upperTenantId, store.getId(), ActivityBarcodeActivationRecordFields.InventoryStatus.IN);
                inStatusRecords.forEach(inStatusData -> {
                    Map<String, Object> updateMap = new HashMap<>();
                    String snId = inStatusData.get(ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID, String.class);
                    updateMap.put(ActivityBarcodeActivationRecordFields.VERIFICATION_TIME, details.get(0).getCreateTime());
                    updateMap.put(ActivityBarcodeActivationRecordFields.VERIFICATION_DOCUMENT_API_NAME, "StoreStockCheck__c");
                    updateMap.put(ActivityBarcodeActivationRecordFields.VERIFICATION_DOCUMENT_ID, relatedMasterId);
                    if (!serialNumberIds.contains(snId)) {
                        updateMap.put(ActivityBarcodeActivationRecordFields.VERIFICATION_PERSON_API_NAME, personApiName);
                        updateMap.put(ActivityBarcodeActivationRecordFields.VERIFICATION_DOCUMENT_TENANT_ID, String.valueOf(currentTenantId));
                        updateMap.put(ActivityBarcodeActivationRecordFields.VERIFICATION_PERSON_ID, personId);
                        updateMap.put(ActivityBarcodeActivationRecordFields.INVENTORY_STATUS, ActivityBarcodeActivationRecordFields.InventoryStatus.OUT);
                    }
                    serviceFacade.updateWithMap(User.systemUser(upperTenantId), inStatusData, updateMap);
                });
            }
            bizCodeDAO.setStatus(currentTenantId, lockKey, BizCodeStatusEnum.USED);
            return result;
        }, null);
    }

    private BizCodePO getBizCodePo(String tenantId, String bizCode, List<String> details) {
        BizCodePO po = bizCodeDAO.getByBizCode(bizCode);
        if (po == null) {
            po = new BizCodePO();
            po.setBizCode(bizCode);
            po.setBizType(BizTypeEnum.STOCK_CHECK.code());
            po.setStatus(BizCodeStatusEnum.INIT.code());
            po.setRelatedBizIds(details);
            bizCodeDAO.add(tenantId, -10000, po);
        }
        return po;
    }

    private List<IObjectData> queryStoreCheckSnStatus(String tenantId, List<String> snIds, String actionId) {
        if (CollectionUtils.isEmpty(snIds)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, Operator.IN, snIds),
                SearchQueryUtil.filter(FMCGSerialNumberStatusFields.ACTION_ID, Operator.EQ, Lists.newArrayList(actionId))
        ));
        Map<String, IObjectData> lastStatusMap = new HashMap<>();
        CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID, FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, CommonFields.CREATE_TIME)).forEach(status -> {
            String snId = status.get(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, String.class);
            IObjectData dataInMap = lastStatusMap.get(snId);
            if (dataInMap == null || status.getCreateTime() > dataInMap.getCreateTime()) {
                lastStatusMap.put(snId, status);
            }
        });
        return new ArrayList<>(lastStatusMap.values());
    }

    private Map<String, IObjectData> getNeedActivatedSerialNumberMap(String tenantId, List<String> serialNumberIds, Set<String> activatedSNIds) {
        List<String> needActivatedSNIds = serialNumberIds.stream().filter(id -> !activatedSNIds.contains(id)).collect(Collectors.toList());
        List<IObjectData> data = serviceFacade.findObjectDataByIds(tenantId, needActivatedSNIds, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        return data.stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
    }

    private boolean judgeExistsObject(String tenantId, List<String> apiNames) {
        for (String apiName : apiNames) {
            try {
                serviceFacade.findObject(tenantId, apiName);
            } catch (ObjectDefNotFoundError ex) {
                log.info("api name: {} not found", apiName);
                return false;
            }
        }
        return true;
    }

    private List<IObjectData> queryActivatedRecordBySN(String tenantId, List<String> snIds) {
        if (CollectionUtils.isEmpty(snIds)) {
            return new ArrayList<>();
        }
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID, Operator.IN, snIds)
        ));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ, query, Lists.newArrayList(CommonFields.ID, ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID));
    }

    private List<IObjectData> queryActivatedRecordByStore(String tenantId, String id, String status) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(ActivityBarcodeActivationRecordFields.ACTIVE_STORE_ID, Operator.EQ, Lists.newArrayList(id)),
                SearchQueryUtil.filter(ActivityBarcodeActivationRecordFields.INVENTORY_STATUS, Operator.EQ, Lists.newArrayList(status))
        ));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ, query);
    }

    private List<IObjectData> getStoreStockCheckDetails(String currentTenantId, String relatedMasterId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter("store_stock_check_id__c", Operator.EQ, Lists.newArrayList(relatedMasterId))
        ));

        return CommonUtils.queryData(serviceFacade, User.systemUser(currentTenantId), "StoreStockCheckDetail__c", query);
    }

    private void dealOtherActive(StockCheckReward.Arg arg, List<String> activationActionList, String actionUniqueId, IObjectData store, StockCheckReward.Result result, String serialNumberId, IObjectData triggerData) {
        String key = String.format(LOCK_KEY, arg.getUpperTenantId(), serialNumberId);


        distributedLock.executeByLock(key, data -> {
            if (activationActionList.contains(actionUniqueId)) {
                IObjectData activationRecord = getActivatedRecord(arg.getUpperTenantId(), serialNumberId);
                if (activationRecord == null && existsActivity(arg.getUpperTenantId(), store)) {
                    String productId = getProductId(arg.getUpperTenantId(), serialNumberId);
                    createActivationRecord(arg.getUpperTenantId(), serialNumberId, store, triggerData.get(FMCGSerialNumberStatusFields.BUSINESS_OCCURRENCE_TIME, Long.class), productId);
                    result.setStatus(1);
                }
            } else {
                log.info("actionId:{},暂不支持在此处执行 激活。", actionUniqueId);
            }
            return result;
        }, arg);
    }

    private String getProductId(String tenantId, String serialNumberId) {
        IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), serialNumberId, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
        return data.get(FMCGSerialNumberFields.PRODUCT_ID, String.class);
    }

    private void createActivationRecord(String tenantId, String serialNumberId, IObjectData store, long activeTime, String productId) {
        IObjectData data = new ObjectData();
        data.setTenantId(tenantId);
        data.setDescribeApiName(ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ);
        data.setOwner(store.getOwner());
        data.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
        data.set(ActivityBarcodeActivationRecordFields.ACTIVE_STORE_ID, store.getId());
        data.set(ActivityBarcodeActivationRecordFields.ACTIVATION_TIME, activeTime);
        data.set(ActivityBarcodeActivationRecordFields.INVENTORY_STATUS, ActivityBarcodeActivationRecordFields.InventoryStatus.IN);
        data.set(ActivityBarcodeActivationRecordFields.IS_MOTIVATING, false);
        data.set(ActivityBarcodeActivationRecordFields.PRODUCT_ID, productId);
        data.set(ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID, serialNumberId);
        serviceFacade.saveObjectData(User.systemUser(tenantId), data);
    }


    private boolean existsActivity(String tenantId, IObjectData store) {
        List<String> departmentIds = activityService.getDepartmentByStore(tenantId, store);
        String actionId = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, ScanCodeActionConstants.STORE_CHECK_WRITE_OFFS);
        List<String> activityIds = activityRewardRuleDAO.queryByTriggerTypeAndDimension(tenantId, ScanCodeActionConstants.SELF_DEFINE_REWARD_TEMPLATE_ID, actionId, "all").stream().map(ActivityRewardRulePO::getRelatedObjectId).collect(Collectors.toList());
        activityIds = activityIds.isEmpty() ? Lists.newArrayList("all") : activityIds;
        boolean exists = !activityService.findActivityByStore(tenantId, departmentIds, store, activityIds, Lists.newArrayList("all")).isEmpty();
        log.info("activity exists:{}", exists);
        return exists;
    }


    private IObjectData getActivatedRecord(String tenantId, String serialNumberId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(ActivityBarcodeActivationRecordFields.SERIAL_NUMBER_ID, Operator.EQ, Lists.newArrayList(serialNumberId))));
        List<IObjectData> data = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.ACTIVITY_BARCODE_ACTIVATION_RECORD_OBJ, query, Lists.newArrayList(CommonFields.ID));
        return data.isEmpty() ? null : data.get(0);
    }

    private List<String> getActivationActionList(String tenantId) {
        ApiContextManager.getContext().setTenantId(tenantId);
        GetConfig.Result result = configService.get(GetConfig.Arg.builder().key(ConfigEnum.ACTIVATED_SCAN_CODE_ACTION.key()).build());
        return result.getConfig().getValue();
    }


}
