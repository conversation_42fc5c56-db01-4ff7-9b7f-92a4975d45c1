package com.facishare.crm.fmcg.tpm.web.designer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.web.contract.model.APLDisplayCustomizedInfoVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.FunctionInfo;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component(value = "aplDisplayCustomizedInfoConfigHandler")
public class APLDisplayCustomizedInfoConfigHandler implements IConfigHandler {
    private static Set<String> APL_NAME_SPACES = Sets.newHashSet();

    static {
        APL_NAME_SPACES.add("fmcg_tpm_activity_display_customized_information");
    }

    @Override
    public void validation(String tenantId, ConfigVO vo) {

        if (!(vo.getValue() instanceof String)) {
            throw new ValidateException("value must be string");
        }
        String value = vo.getValue();
        List<APLDisplayCustomizedInfoVO> aplDisplayCustomizedInfoVOS = JSON.parseArray(value, APLDisplayCustomizedInfoVO.class);
        for (APLDisplayCustomizedInfoVO aplDisplayCustomizedInfoVO : aplDisplayCustomizedInfoVOS) {

            if (StringUtils.isEmpty(aplDisplayCustomizedInfoVO.getObjectApiName())) {
                throw new ValidateException(" object api name  null error");
            }

            FunctionInfo functionInfo = aplDisplayCustomizedInfoVO.getFunctionInfo();
            if (StringUtils.isEmpty(functionInfo.getNameSpace()) || !APL_NAME_SPACES.contains(functionInfo.getNameSpace())) {
                throw new ValidateException("function apl name space error");
            }

            if (StringUtils.isEmpty(functionInfo.getApiName())) {
                throw new ValidateException("function api name null error");
            }

            if (StringUtils.isEmpty(functionInfo.getBindingObjectApiName())) {
                throw new ValidateException("function bind object null error");
            }
        }

    }

    @Override
    public void after(String tenantId, ConfigVO vo) {

    }
}
