package com.facishare.crm.fmcg.tpm.business.dto;

import com.facishare.crm.fmcg.common.apiname.TPMActivityProofProductDetailFields;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface TPMDisplayReport {

    @Data
    @Builder
    class LayerAndMete implements Serializable {
        private String displayFormId;
        private Integer layer;
        private Double mete;

        public static Map<String, LayerAndMete> of(List<IObjectData> allProofProducts) {
            Map<String, LayerAndMete> map = Maps.newHashMap();
            for (IObjectData proofProduct : allProofProducts) {
                String displayFormId = proofProduct.get(TPMActivityProofProductDetailFields.DISPLAY_FORM_ID, String.class);
                Double currentProductMete = proofProduct.get(TPMActivityProofProductDetailFields.PRODUCT_PACKAGE_WIDTH_METE, Double.class);
                Integer currentRowNumber = proofProduct.get(TPMActivityProofProductDetailFields.AI_NUMBER, Integer.class);
                Integer currentLayer = proofProduct.get(TPMActivityProofProductDetailFields.LAYER, Integer.class);
                LayerAndMete layerAndMete = map.get(displayFormId);
                if (layerAndMete == null) {
                    layerAndMete = builder().displayFormId(displayFormId).layer(currentLayer).mete(multiply(currentProductMete, currentRowNumber)).build();
                    map.put(displayFormId, layerAndMete);
                    continue;
                }
                Integer existsLayer = layerAndMete.getLayer();
                Double existsMete = layerAndMete.getMete();
                layerAndMete = builder().displayFormId(displayFormId).layer(currentLayer + existsLayer).mete(existsMete + multiply(currentProductMete, currentRowNumber)).build();
                map.put(displayFormId, layerAndMete);
            }
            return map;
        }

        private static Double multiply(Double currentProductMete, Integer currentRowNumber) {
            if (Objects.isNull(currentProductMete) || Objects.isNull(currentRowNumber)) {
                return 0D;
            }
            BigDecimal mete = new BigDecimal(currentProductMete);
            BigDecimal rowNumber = new BigDecimal(currentRowNumber);
            return mete.multiply(rowNumber).setScale(2, RoundingMode.HALF_UP).doubleValue();

        }
    }

}
