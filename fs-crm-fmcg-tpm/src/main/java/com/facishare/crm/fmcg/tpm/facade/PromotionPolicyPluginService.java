package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.PricePolicyFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * author: wuyx
 * description:
 * createTime: 2023/7/11 19:34
 */
@Slf4j
@Service
@ServiceModule("tpm_price_policy_action")
public class PromotionPolicyPluginService {

    @Resource
    private PromotionPolicyService promotionPolicyService;

    @Resource
    protected ServiceFacade serviceFacade;

    @ServiceMethod("add_before")
    public AddActionDomainPlugin.Result before(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        boolean validationActivityIds = true;
        IObjectData objectData = arg.getObjectData().toObjectData();
        try {
            if (Objects.nonNull(objectData)) {
                log.info("tpm_price_policy_action add obj={}", JSON.toJSONString(objectData));
                log.info("tpm_price_policy_action add before detail={}", JSON.toJSONString(arg.getDetailObjectData()));
                validationActivityIds = validationActivityId(objectData);
            }
        } catch (Exception e) {
            log.info("tpm_price_policy_action add before err = ", e);
        }

        if (!validationActivityIds) {
            throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_PLUGIN_SERVICE_0));
        }

        String activityId = objectData.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
        if (Strings.isEmpty(activityId)) {
            return new AddActionDomainPlugin.Result();
        }

        AddActionDomainPlugin.Result result = new AddActionDomainPlugin.Result();
        try {
            List<ObjectDataDocument> dataDocuments = arg.getDetailObjectData().get(ApiNames.PRICE_POLICY_RULE_OBJ);
            List<ObjectDataDocument> updateDocs = fillArgParam(dataDocuments, serviceContext, activityId);
            if (CollectionUtils.isNotEmpty(updateDocs)) {
                Map<String, List<ObjectDataDocument>> detailsToUpdate = Maps.newHashMap();
                detailsToUpdate.put(ApiNames.PRICE_POLICY_RULE_OBJ, updateDocs);
                result.setDetailsToUpdate(detailsToUpdate);
            }
        } catch (Exception e) {
            log.info("tpm_price_policy_action add dataDocuments err", e);
        }
        log.info("tpm_price_policy_action add result={}", JSON.toJSONString(result));
        return result;
    }

    private List<ObjectDataDocument> fillArgParam(List<ObjectDataDocument> dataDocuments, ServiceContext serviceContext, String activityId) {
        IObjectData activity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(serviceContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(activity)) {
            return dataDocuments;
        }
        Boolean orderGoodsMeetingFlag = activity.get(TPMActivityFields.ORDER_GOODS_MEETING_FLAG, Boolean.class, false);

        if (TPMGrayUtils.allowAddUnActivityPromotionPolicy(serviceContext.getTenantId()) && !orderGoodsMeetingFlag) {
            return dataDocuments;
        }
        List<ObjectDataDocument> updateDocs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataDocuments)) {
            log.info("tpm_price_policy_action add activity={}", JSON.toJSONString(activity));
            String objectApiName = ApiNames.SALES_ORDER_OBJ;
            String objectApiNameS = "销售订单";
            if (orderGoodsMeetingFlag) {
                objectApiName = ApiNames.QUOTE_OBJ;
                objectApiNameS = "报价单";
            }
            String activityName = activity.getName();
            String ruleCondition = String.format("[\n" +
                    "    {\n" +
                    "        \"filters\": [\n" +
                    "            {\n" +
                    "                \"field_name\": \"activity_id\",\n" +
                    "                \"field_name__s\": \"活动申请\",\n" +
                    "                \"operator\": \"EQ\",\n" +
                    "                \"operator__s\": \"等于\",\n" +
                    "                \"field_values\": [\n" +
                    "                    \"%s\"\n" +
                    "                ],\n" +
                    "                \"field_values__s\": \"%s\",\n" +
                    "                \"type\": \"object_reference\",\n" +
                    "                \"object_api_name\": \"%s\",\n" +
                    "                \"object_api_name__s\": \"%s\",\n" +
                    "                \"field_name_type\": \"field\",\n" +
                    "                \"field_value_type\": \"value\",\n" +
                    "                \"operator_name\": \"等于\"\n" +
                    "            }\n" +
                    "        ],\n" +
                    "        \"connector\": \"OR\"\n" +
                    "    }\n" +
                    "]", activityId, activityName, objectApiName, objectApiNameS);

            for (ObjectDataDocument document : dataDocuments) {
                ObjectDataDocument updateDoc = new ObjectDataDocument();
                updateDoc.toObjectData().setId(document.getId());
                String condition = document.toObjectData().get("rule_condition", String.class, "");
                if (StringUtils.isNotEmpty(condition)) {
                    log.info("tpm_price_policy_action condition ={}", JSON.toJSONString(condition));

                    JSON ruleFilter = JSON.parseArray(ruleCondition).getJSONObject(0).getJSONArray("filters").getJSONObject(0);

                    log.info("tpm_price_policy_action ruleFilters ={}", JSON.toJSONString(ruleFilter));
                    JSONArray newArray = new JSONArray();
                    JSONArray array = JSON.parseArray(condition);
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject groupObj = array.getJSONObject(i);
                        if (groupObj.containsKey("filters")) {
                            groupObj.getJSONArray("filters").add(ruleFilter);
                            newArray.add(groupObj);
                        }
                    }
                    if (newArray.isEmpty()) {
                        newArray = array;
                    }

                    log.info("tpm_price_policy_action newArray condition ={}", newArray.toString());
                    updateDoc.toObjectData().set("rule_condition", newArray.toString());
                } else {
                    updateDoc.toObjectData().set("rule_condition", ruleCondition);
                }
                updateDocs.add(updateDoc);
            }
        }
        return updateDocs;
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result before(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData objectData = arg.getObjectData().toObjectData();
        if (Objects.nonNull(objectData)) {
            boolean validationActivityIds = validationActivityId(objectData);
            if (!validationActivityIds && !TPMGrayUtils.isRioTenant(serviceContext.getTenantId())) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_PLUGIN_SERVICE_1));
            }
        }

        String activityId = objectData.get(PricePolicyFields.ACTIVITY_ID, String.class, "");
        if (Strings.isEmpty(activityId)) {
            return new EditActionDomainPlugin.Result();
        }
        EditActionDomainPlugin.Result result = new EditActionDomainPlugin.Result();
        try {
            List<ObjectDataDocument> dataDocuments = arg.getDetailObjectData().get(ApiNames.PRICE_POLICY_RULE_OBJ);
            List<ObjectDataDocument> updateDocs = fillArgParam(dataDocuments, serviceContext, activityId);
            if (CollectionUtils.isNotEmpty(updateDocs)) {
                Map<String, List<ObjectDataDocument>> detailsToUpdate = Maps.newHashMap();
                detailsToUpdate.put(ApiNames.PRICE_POLICY_RULE_OBJ, updateDocs);
                result.setDetailsToUpdate(detailsToUpdate);
            }
        } catch (Exception e) {
            log.info("tpm_price_policy_action edit dataDocuments err", e);
        }

        log.info("tpm_price_policy_action edit result={}", JSON.toJSONString(result));
        return result;
    }

    @ServiceMethod("bulkInvalid_before")
    public BulkInvalidActionDomainPlugin.Result before(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        if (promotionPolicyService.isFromTPM(arg.getExtraData())) {
            return new BulkInvalidActionDomainPlugin.Result();
        }

        List<ObjectDataDocument> objectDataList = arg.getObjectDataList();
        List<String> excludeNames = promotionPolicyService.queryIsNotValidaNames(objectDataList);

        if (CollectionUtils.isNotEmpty(excludeNames)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.PROMOTION_POLICY_PLUGIN_SERVICE_2), excludeNames.toString()));
        }

        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("invalid_before")
    public InvalidActionDomainPlugin.Result before(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        if (promotionPolicyService.isFromTPM(arg.getExtraData())) {
            return new InvalidActionDomainPlugin.Result();
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (Objects.nonNull(objectData)) {
            boolean validationIsInvalid = promotionPolicyService.validationIsInvalid(objectData);
            if (!validationIsInvalid) {
                throw new ValidateException(I18N.text(I18NKeys.PROMOTION_POLICY_PLUGIN_SERVICE_3));
            }
        }
        return new InvalidActionDomainPlugin.Result();
    }

    private boolean validationActivityId(IObjectData objectData) {
        return promotionPolicyService.validationIsNeedActivityId(objectData);
    }
}
