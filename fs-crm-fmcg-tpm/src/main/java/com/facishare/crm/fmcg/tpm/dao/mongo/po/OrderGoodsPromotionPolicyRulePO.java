package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.OrderGoodsPromotionPolicyRuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2024/11/26 11:41
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_order_goods_promotion_policy_rule", noClassnameStored = true)
public class OrderGoodsPromotionPolicyRulePO extends MongoPO implements Serializable {

    public static final String F_ORDER = "order";
    public static final String F_VERSION = "version";
    public static final String F_REF_OBJECT_API_NAME = "ref_object_api_name";
    public static final String F_REF_OBJECT_ID = "ref_object_id";
    public static final String F_PRODUCT_GIFT_DATA_JSON = "product_gift_data_json";
    public static final String F_PARENT_OBJECT_ID = "parent_object_id";
    public static final String F_ORDER_GOODS_ID = "order_goods_id";

    @Property(F_VERSION)
    private long version;

    @Property(F_ORDER)
    private long order;

    @Property(F_REF_OBJECT_API_NAME)
    private String objectApiName;

    @Property(F_REF_OBJECT_ID)
    private String objectId;

    @Property(F_PARENT_OBJECT_ID)
    private String parentObjectId;

    @Property(F_ORDER_GOODS_ID)
    private String orderGoodsId;

    @Property(F_PRODUCT_GIFT_DATA_JSON)
    private String productGiftData;

    public static OrderGoodsPromotionPolicyRulePO voToPo(OrderGoodsPromotionPolicyRuleVO vo) {
        OrderGoodsPromotionPolicyRulePO po = new OrderGoodsPromotionPolicyRulePO();
        po.setObjectApiName(vo.getObjectApiName());
        po.setObjectId(vo.getObjectId());
        po.setOrder(vo.getOrder());
        po.setVersion(vo.getVersion());
        po.setParentObjectId(vo.getParentObjectId());
        po.setProductGiftData(vo.getProductGiftData());
        return po;
    }

    public static OrderGoodsPromotionPolicyRuleVO poToVo(OrderGoodsPromotionPolicyRulePO po) {
        OrderGoodsPromotionPolicyRuleVO vo = new OrderGoodsPromotionPolicyRuleVO();
        vo.setObjectApiName(po.getObjectApiName());
        vo.setObjectId(po.getObjectId());
        vo.setParentObjectId(po.getParentObjectId());
        vo.setOrderGoodsId(po.getOrderGoodsId());
        vo.setProductGiftData(po.getProductGiftData());
        return vo;
    }
}