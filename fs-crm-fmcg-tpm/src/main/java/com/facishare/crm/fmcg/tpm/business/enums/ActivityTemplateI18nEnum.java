package com.facishare.crm.fmcg.tpm.business.enums;


import com.facishare.paas.I18N;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/12/13 15:07
 */
public enum ActivityTemplateI18nEnum {

    DISPLAY_NAME("陈列/铺货类", "fmcg.activity.template.display.name"),
    DISPLAY_ACTIVITY_NAME("极简-陈列/铺货类活动", "fmcg.activity.template.display.activity.name"),

    DISPLAY_OBJECT_TYPE_NAME("陈列/铺货类申请单", "fmcg.activity.template.display_object_type_name"),
    DISPLAY_DETAIL_OBJECTS_TYPE_NAME("陈列/铺货类品项范围", "fmcg.activity.template.display_detail_objects_type_name"),
    DISPLAY_STANDARD_NAME("标准-陈列/铺货类活动", "fmcg.activity.template.display_standard_name"),
    DISPLAY_complex_NAME("精细-陈列/铺货类活动", "fmcg.activity.template.display_complex_name"),

    DISPLAY_TEMPLATE_NAME("极简", "fmcg.activity.template.display.template_name"),
    DISPLAY_STANDARD_TEMPLATE_NAME("标准", "fmcg.activity.template.display_standard_template_name"),
    DISPLAY_complex_TEMPLATE_NAME("精细", "fmcg.activity.template.display_complex_template_name"),


    TASTING_NAME("品鉴类", "fmcg.activity.template.tasting_name"),
    TASTING_STANDARD_NAME("标准-品鉴类活动", "fmcg.activity.template.tasting_standard_name"),
    TASTING_OBJECT_TYPE_NAME("品鉴类申请单", "fmcg.activity.template.tasting_object_type_name"),
    TASTING_DETAIL_OBJECTS_TYPE_NAME("品鉴类品项范围", "fmcg.activity.template.tasting_detail_objects_type_name"),
    TASTING_COMPLEX_NAME("精细-品鉴类活动", "fmcg.activity.template.tasting_complex_name"),

    PROMOTION_NAME("渠道促销类", "fmcg.activity.template.promotion_name"),
    PROMOTION_SIMPLE_NAME("极简-渠道促销类活动", "fmcg.activity.template.promotion_simple_name"),
    PROMOTION_OBJECT_TYPE_NAME("渠道促销类申请单", "fmcg.activity.template.promotion_object_type_name"),
    PROMOTION_PLAN_TEMPLATE_OBJECT_TYPE_NAME("渠道促销类活动方案", "fmcg.activity.template.promotion_plan_template_object_type_name"),
    PROMOTION_DETAIL_OBJECTS_TYPE_NAME("品鉴类品项范围", "fmcg.activity.template.promotion_detail_objects_type_name"),
    PROMOTION_STANDARD_NAME("标准-渠道促销类活动", "fmcg.activity.template.promotion_standard_name"),

    ONLINE_NAME("直播/电商类", "fmcg.activity.template.online_name"),
    ONLINE_SIMPLE_NAME("极简-直播/电商类活动", "fmcg.activity.template.online_simple_name"),
    ONLINE_OBJECT_TYPE_NAME("直播/电商类申请单", "fmcg.activity.template.online_object_type_name"),

    REWARD_NAME("营销激励类", "fmcg.activity.template.reward_name"),
    REWARD_SCAN_CODE_GET_REWARD_NAME("码上有礼", "fmcg.activity.template.reward_scan_code_get_reward_name"),

    REWARD_STOCK_UP_REWARD_NAME("囤货激励", "fmcg.activity.template.reward_stock_up_reward_name"),
    REWARD_BIG_DATE_NAME("产品代售", "fmcg.activity.template.reward_big_date_name"),

    REWARD_SALES_REWARD_NAME("动销激励", "fmcg.activity.template.reward_sales_reward_name"),
    SELF_DEFINE_REWARD_NAME("自定义激励", "fmcg.activity.template.self_define_reward_name"),
    STORE_SCAN_CODE_REWARD_NAME("门店开箱有礼", "fmcg.activity.template.store_scan_code_reward_name"),


    CUSTOM_NAME("自定义新建", "fmcg.activity.template.custom_name"),
    CUSTOM_EMPTY_NAME("自定义", "fmcg.activity.template.custom_empty_name"),
    ORDER_GOODS_SIMPLE_NAME("订货会", "fmcg.activity.template.orderGoods_simple_name"),

    CUSTOM_DESCRIPTION("可按需自定义管理节点及对各种范围的定义", "fmcg.activity.template.custom_description"),
    ONLINE_DESCRIPTION("极简活动费用管理", "fmcg.activity.template.online_description"),
    DISPLAY_DESCRIPTION("默认支持<span>「品项范围」</span>及<span>「陈列标准」</span>的定义及控制", "fmcg.activity.template.display_description"),
    TASTING_DESCRIPTION("默认支持<span>「用酒范围」</span>及<span>「品鉴场地」</span>的定义及控制", "fmcg.activity.template.tasting_description"),
    PROMOTION_DESCRIPTION("活动支持定义<span>「促销规则」</span>，与<span>「销售订单」</span>打通，完成促销活动闭环", "fmcg.activity.template.promotion_description"),
    REWARD_DESCRIPTION("默认活动支持定义<span>「激励策略」</span>，与<span>「一物一码」</span>能力结合，完成码营销活动闭环", "fmcg.activity.template.reward_description"),
    REWARD_SCAN_CODE_GET_REWARD_DESCRIPTION("通过<span>「消费者扫码领红包」</span>，任选<span>「奖励方式」</span>激励<span>「消费者」</span>", "fmcg.activity.template.reward_scan_code_get_reward_description"),
    REWARD_STOCK_UP_REWARD_DESCRIPTION("通过<span>「门店签收」</span>，任选<span>「奖励方式」</span>激励<span>「门店」</span>", "fmcg.activity.template.reward_stock_up_reward_description"),
    REWARD_BIG_DATE_DESCRIPTION("通过<span>「消费者扫码」</span>，以<span>「减价支付」</span>激励<span>「消费者」</span>", "fmcg.activity.template.reward_big_date_description"),
    REWARD_SELF_DEFINE_REWARD_DESCRIPTION("通过触达门店的<span>「扫码动作」</span>，自由配置激励策略", "fmcg.activity.template.reward_self_define_reward_description"),
    ORDER_GOODS_SIMPLE_DESCRIPTION("订货会活动管理", "fmcg.activity.template.orderGoods_simple_description"),
    REWARD_STORE_SCAN_CODE_REWARD_DESCRIPTION("通过<span>「门店扫箱码」</span>，任选<span>「奖励方式」</span>激励<span>「门店」</span>", "fmcg.activity.template.reward_store_scan_code_reward_description"),


    ;

    ActivityTemplateI18nEnum(String value, String label) {
        this.label = label;
        this.value = value;
    }

    private final String label;
    private final String value;

    public String value() {
        return this.value;
    }

    public String label() {
        return this.label;
    }

    public static Map<String, String> getInnerMap(){
        return Stream.of(values())
                .collect(Collectors.toMap(ActivityTemplateI18nEnum::value, v -> I18N.text(v.label), (before, after) -> before));
    }

}
