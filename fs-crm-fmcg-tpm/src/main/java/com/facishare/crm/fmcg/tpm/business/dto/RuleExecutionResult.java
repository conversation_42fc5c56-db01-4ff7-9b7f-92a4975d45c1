package com.facishare.crm.fmcg.tpm.business.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class RuleExecutionResult implements Serializable {
    @JSONField(name = "cycle_info")
    @JsonProperty("cycle_info")
    private CycleInfo cycleInfo;

    @JSONField(name = "expressions")
    @JsonProperty("expressions")
    private List<ExpressionsBean> expressions;

    @JSONField(name = "calculate_type")
    @JsonProperty("expressions")
    private String calculateType;

    @JSONField(name = "condition")
    @JsonProperty("condition")
    private List<String> condition;

    @JSONField(name = "object_api_name")
    @JsonProperty("object_api_name")
    private String objectApiName;

    @J<PERSON><PERSON>ield(name = "function_execution")
    @JsonProperty("function_execution")
    private String functionExecution;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CycleInfo implements Serializable {
        @JSONField(name = "cycle_data")
        @JsonProperty("cycle_data")
        private List<CycleDataBean> cycleData;

        @JSONField(name = "max_amount")
        @JsonProperty("max_amount")
        private String maxAmount;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LeftBean implements Serializable {
        @JSONField(name = "object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;

        @JSONField(name = "object_api_name__s")
        @JsonProperty("object_api_name__s")
        private String objectApiNames;

        @JSONField(name = "field_name")
        @JsonProperty("field_name")
        private String fieldName;

        @JSONField(name = "field_name__s")
        @JsonProperty("field_name__s")
        private String fieldNames;

        @JSONField(name = "field_name_type")
        @JsonProperty("field_name_type")
        private String fieldNameType;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CycleDataBean implements Serializable {
        @JSONField(name = "execute_type")
        @JsonProperty("execute_type")
        private String executeType;

        @JSONField(name = "left")
        @JsonProperty("left")
        private LeftBean left;

        @JSONField(name = "field_value")
        @JsonProperty("field_value")
        private String fieldValue;

        @JSONField(name = "field_value__s")
        @JsonProperty("field_value__s")
        private String fieldValues;

        @JSONField(name = "used_amount")
        @JsonProperty("used_amount")
        private String usedAmount;

        @JSONField(name = "used_amount__s")
        @JsonProperty("used_amount__s")
        private String usedAmounts;

        @JSONField(name = "rowId")
        @JsonProperty("rowId")
        private String rowId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExpressionsBean implements Serializable {
        @JSONField(name = "execute_type")
        @JsonProperty("execute_type")
        private String executeType;

        @JSONField(name = "execute_type__s")
        @JsonProperty("execute_type__s")
        private String executeTypes;

        @JSONField(name = "right")
        @JsonProperty("right")
        private String right;

        @JSONField(name = "right__s")
        @JsonProperty("right__s")
        private String rights;

        @JSONField(name = "rowId")
        @JsonProperty("rowId")
        private String rowId;
    }
}
