package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetAccrualRulePO;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetAccrualRuleVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/10 上午10:36
 */
public interface IBudgetAccrualRuleManager {


    void accrualRuleInfoValidate(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO);

    void accrualRuleEditValidate(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO, BudgetAccrualRulePO oldPO);

    void enclosureConditionFilter(String tenantId, Integer employeeId, BudgetAccrualRuleVO budgetAccrualRuleVO);

    void bindObjectPluginInstance(String integer, Integer employeeId, String apiName, String pluginName);

    void publishSyncAccrualRuleFieldTask(String tenantId);

    void syncAddTriggerRuleStatusField(String tenantId, String objectApiName);

    Boolean isExistAccrualDataRelated(String tenantId, String apiName, String ruleId);

    void deleteObjectPluginInstance(String tenantId, String apiName, String recordType, String accrualPlugin);

    Map<String, String> findObjectsByTenantId(String tenantId);

    Map<String, String> getAccrualObjects(String tenantId);

    void deleteAccrualRuleByTypeId(String tenantId, int employeeId, String budgetTypeId);

    void editPlatI18Key(String tenantId, BudgetAccrualRuleVO budgetAccrualRuleVO);
}
