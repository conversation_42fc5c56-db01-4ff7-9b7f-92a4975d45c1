package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.designer.IActivityNode;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/24 下午5:59
 */
@Data
@ToString
public class ActivityNodeEntity implements IActivityNode, Serializable {

    @Property("name")
    private String name;

    @Property("template_id")
    private String templateId;

    @Property("package")
    private String packageType;

    @Property("type")
    private String type;

    @Property("status")
    private String status;

    @JSONField(name = "exception_status")

    @Property("exception_status")
    private String exceptionStatus;

    @Property("object_api_name")
    private String objectApiName;

    @Property("object_record_type")
    private String objectRecordType;

    @Property("reference_activity_field_api_name")
    private String referenceActivityFieldApiName;

    @Embedded("activity_plan_config")
    private ActivityPlanConfigEntity activityPlanConfig;

    @Embedded("activity_agreement_config")
    private ActivityAgreementConfigEntity activityAgreementConfig;

    @Embedded("activity_proof_config")
    private ActivityProofConfigEntity activityProofConfig;

    @Embedded("activity_write_off_config")
    private ActivityWriteOffConfigEntity activityWriteOffConfig;

    @Embedded("activity_proof_audit_config")
    private ActivityProofAuditConfigEntity activityProofAuditConfig;

    @Embedded("activity_cost_assign_config")
    private ActivityCostAssignConfigEntity activityCostAssignConfigEntity;

    @Embedded("activity_store_write_off_config")
    private ActivityStoreWriteOffConfigEntity activityStoreWriteOffConfig;

    @Embedded("node_exception_info")
    private NodeExceptionInfoEntity nodeExceptionInfo;

    public static ActivityNodeEntity fromVO(ActivityNodeVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityNodeEntity po = new ActivityNodeEntity();
        po.setName(vo.getName());
        po.setTemplateId(vo.getTemplateId());
        po.setPackageType(vo.getPackageType());
        po.setType(vo.getType());
        po.setObjectApiName(vo.getObjectApiName());
        po.setObjectRecordType(vo.getObjectRecordType());
        po.setExceptionStatus(vo.getExceptionStatus() == null ? ExceptionStatusType.NORMAL.value() : vo.getExceptionStatus());
        po.setNodeExceptionInfo(NodeExceptionInfoEntity.fromVO(vo.getNodeExceptionInfo()));
        po.setReferenceActivityFieldApiName(vo.getReferenceActivityFieldApiName());
        switch (NodeType.of(vo.getType())) {
            case PLAN:
                po.setActivityPlanConfig(ActivityPlanConfigEntity.fromVO(vo.getActivityPlanConfig()));
            case PROOF:
                po.setActivityProofConfig(ActivityProofConfigEntity.fromVO(vo.getActivityProofConfig()));
                break;
            case WRITE_OFF:
                po.setActivityWriteOffConfig(ActivityWriteOffConfigEntity.fromVO(vo.getActivityWriteOffConfig()));
                break;
            case AUDIT:
                po.setActivityProofAuditConfig(ActivityProofAuditConfigEntity.fromVO(vo.getActivityProofAuditConfig()));
                break;
            case AGREEMENT:
                po.setActivityAgreementConfig(ActivityAgreementConfigEntity.fromVO(vo.getActivityAgreementConfig()));
                break;
            case COST_ASSIGN:
                po.setActivityCostAssignConfigEntity(ActivityCostAssignConfigEntity.fromVO(vo.getActivityCostAssignConfig()));
                break;
            case STORE_WRITE_OFF:
                po.setActivityStoreWriteOffConfig(ActivityStoreWriteOffConfigEntity.fromVO(vo.getActivityStoreWriteOffConfig()));
                break;
            default:
                break;
        }
        return po;
    }

    @Override
    public String getName() {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = "-10000";
        if (Objects.nonNull(context)) {
            tenantId = context.getTenantId();
        }

        if (Strings.isNullOrEmpty(this.templateId) || Strings.isNullOrEmpty(this.objectApiName) || Strings.isNullOrEmpty(this.name)) {
            return name;
        }
        String text = TPMI18Utils.getActivitySystemNodeText(tenantId, this.templateId, this.objectApiName, this.name, this.type);
        if (!Strings.isNullOrEmpty(text)) {
            name = text;
        }
        return name;
    }

}
