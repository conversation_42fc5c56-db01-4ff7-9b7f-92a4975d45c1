package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.api.activity.GetCustomizedInfo;
import com.facishare.crm.fmcg.tpm.dao.mongo.APLReturnType;
import com.facishare.crm.fmcg.tpm.dao.mongo.ConfigDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigPO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.APLDisplayCustomizedInfoVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.FunctionInfo;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityCustomizedInfoService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class ActivityCustomizedInfoService implements IActivityCustomizedInfoService {
    @Resource
    private ConfigDAO configDAO;
    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public GetCustomizedInfo.Result getCustomizedInfo(GetCustomizedInfo.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        ConfigPO po = configDAO.queryByKey(context.getTenantId(), ConfigEnum.APL_DISPLAY_CUSTOMIZED_INFORMATION.key());
        if (Objects.isNull(po)) {
            return GetCustomizedInfo.Result.builder().build();
        }
        ConfigVO vo = ConfigPO.toVO(po);
        List<APLDisplayCustomizedInfoVO> aplDisplayCustomizedInfoVOS = JSON.parseArray(vo.getValue(), APLDisplayCustomizedInfoVO.class);

        if (CollectionUtils.isEmpty(aplDisplayCustomizedInfoVOS)) {
            return GetCustomizedInfo.Result.builder().build();
        }

        APLDisplayCustomizedInfoVO funcInfo = null;
        for (APLDisplayCustomizedInfoVO aplDisplayCustomizedInfoVO : aplDisplayCustomizedInfoVOS) {
            if (Objects.equals(aplDisplayCustomizedInfoVO.getObjectApiName(), arg.getObjectApiName())) {
                funcInfo = aplDisplayCustomizedInfoVO;
                break;
            }
        }

        if (funcInfo == null) {
            return GetCustomizedInfo.Result.builder().build();
        }

        GetCustomizedInfo.Result result = GetCustomizedInfo.Result.builder().data(Lists.newArrayList()).build();
        for (String activityId : arg.getActivityIds()) {
            List<GetCustomizedInfo.CustomizedInfoVO> customizedInfos = getGroupCustomizedInfos(context.getTenantId(), activityId, arg.getStoreId(), funcInfo);
            result.getData().add(GetCustomizedInfo.GroupCustomizedInfoVO.builder().activityId(activityId).data(customizedInfos).build());
        }

        return result;
    }

    private List<GetCustomizedInfo.CustomizedInfoVO> getGroupCustomizedInfos(String tenantId, String activityId, String storeId, APLDisplayCustomizedInfoVO aplDisplayCustomizedInfoVO) {


        Map<String, Object> parameters = Maps.newHashMap();

        parameters.put("activity_id", activityId);
        parameters.put("store_id", storeId);

        FunctionInfo functionInfo = aplDisplayCustomizedInfoVO.getFunctionInfo();
        IUdefFunction function = initFunction(User.systemUser(tenantId), functionInfo.getApiName(), functionInfo.getBindingObjectApiName());
        RunResult functionRunResult = serviceFacade.getFunctionLogicService().executeUDefFunction(
                User.systemUser(tenantId),
                function,
                parameters,
                new ObjectData(),
                null
        );
        Object value = fetchResultValue(functionInfo, functionRunResult);

        if (value == null) {
            return Lists.newArrayList();
        }

        List<GetCustomizedInfo.CustomizedInfoVO> customizedInfoVOS = JSON.parseArray(JSON.toJSONString(value), GetCustomizedInfo.CustomizedInfoVO.class);
        if (CollectionUtils.isEmpty(customizedInfoVOS)) {
            return Lists.newArrayList();
        }
        return customizedInfoVOS;
    }

    private Object fetchResultValue(FunctionInfo functionInfo, RunResult functionRunResult) {
        Object functionResult = fetchObjectResult(functionInfo.getApiName(), functionRunResult);
        if (Objects.isNull(functionResult)) {
            return null;
        }

        List<Class<?>> clazz;
        try {
            clazz = APLReturnType.valueOf(functionInfo.getReturnType().toUpperCase()).getClassTypes();
        } catch (IllegalArgumentException ex) {
            throw new ValidateException(ex.getMessage());
        }
        boolean validate = false;
        for (Class<?> aClazz : clazz) {
            if (aClazz.isInstance(functionResult)) {
                validate = true;
                break;
            }
        }
        if (validate) {
            return functionResult;
        } else {
            throw new ValidateException(I18N.text(I18NKeys.APL_EXECUTE_FAIL));
        }
    }

    private Object fetchObjectResult(String functionApiName, RunResult runResult) {
        if (!runResult.isSuccess()) {
            log.error("run apl {} cause exception : {}", functionApiName, runResult.getErrorInfo());
            throw new ValidateException(runResult.getErrorInfo());
        }

        return runResult.getFunctionResult();
    }

    private IUdefFunction initFunction(User user, String functionApiName, String bindingObjectAPIName) {
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, functionApiName, bindingObjectAPIName);
        if (Objects.isNull(function)) {
            throw new ValidateException("function is null");
        }

        if (Boolean.TRUE.equals(function.isDeleted())) {
            throw new ValidateException("function is deleted");
        }

        if (!function.isActive()) {
            throw new ValidateException("function is disable");
        }

        return function;
    }
}
