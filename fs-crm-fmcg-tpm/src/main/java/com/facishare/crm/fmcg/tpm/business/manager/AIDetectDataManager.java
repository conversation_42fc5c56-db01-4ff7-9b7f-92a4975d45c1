package com.facishare.crm.fmcg.tpm.business.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.fmcg.framework.http.contract.fmcg.ai.BatchQueryAIRuleByIds;
import com.fs.fmcg.sdk.ai.contract.DetectResultDTO;
import com.fs.fmcg.sdk.ai.contract.ObjectDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class AIDetectDataManager {

    public List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> calculateStandardProductSkuRowNumber(
            String tenantId,
            String displayFormId,
            List<TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList,
            List<DetectResultDTO> detectResultDTOS,
            Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap
    ) {

        if (CollectionUtils.isEmpty(detectResultDTOS)) {
            return Lists.newArrayList();
        }

        BatchQueryAIRuleByIds.Field aiRowNumberField = objectFieldListMap.values().stream()
                .flatMap(List::stream)
                .filter(field -> "aiRowNumber".equals(field.getFieldKey()))
                .findFirst()
                .orElseThrow(null);

        if (Objects.isNull(aiRowNumberField)) {
            return Lists.newArrayList();
        }

        Integer calculateType = aiRowNumberField.getCalculateType();

        Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> effectiveNPathTPMProductSkuRowNumberDTOMap = getEffectiveNPathTPMProductSkuRowNumberDTOMap(tenantId, displayFormId, tenantDetectFaultTolerantForDisPlayFormList, detectResultDTOS);

        // 第1步：对每个路径下的数据按displayFormId+productId分组求和rowNumber
        Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> groupedByPathResult = new HashMap<>();

        if (calculateType == 3) {
            for (Map.Entry<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> entry : effectiveNPathTPMProductSkuRowNumberDTOMap.entrySet()) {
                String path = entry.getKey();
                List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> pathProducts = entry.getValue();

                // 按displayFormId+productId分组
                Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> groupedByDisplayAndProduct = pathProducts.stream()
                        .collect(Collectors.groupingBy(dto -> dto.getDisplayFormId() + ":" + dto.getProductId()));

                // 对每组数据求和rowNumber
                List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> summedList = new ArrayList<>();
                for (Map.Entry<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> group : groupedByDisplayAndProduct.entrySet()) {
                    String[] keys = group.getKey().split(":");
                    String groupDisplayFormId = keys[0];
                    String productId = keys[1];

                    double sumRowNumber = group.getValue().stream()
                            .mapToDouble(TPMDisplayReportService.TPMProductSkuRowNumberDTO::getRowNumber)
                            .sum();

                    TPMDisplayReportService.TPMProductSkuRowNumberDTO sumDto = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder()
                            .displayFormId(groupDisplayFormId)
                            .productId(productId)
                            .rowNumber(sumRowNumber)
                            .build();

                    summedList.add(sumDto);
                }

                groupedByPathResult.put(path, summedList);
            }
        } else if (calculateType == 5) {
            for (Map.Entry<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> entry : effectiveNPathTPMProductSkuRowNumberDTOMap.entrySet()) {
                String path = entry.getKey();
                List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> pathProducts = entry.getValue();

                // 按displayFormId+productId分组
                Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> groupedByDisplayAndProduct = pathProducts.stream()
                        .collect(Collectors.groupingBy(dto -> dto.getDisplayFormId() + ":" + dto.getProductId()));

                List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> summedList = new ArrayList<>();
                for (Map.Entry<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> group : groupedByDisplayAndProduct.entrySet()) {
                    String[] keys = group.getKey().split(":");
                    String groupDisplayFormId = keys[0];
                    String productId = keys[1];
                    group.getValue().forEach(dto -> {
                        TPMDisplayReportService.TPMProductSkuRowNumberDTO sumDto = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder()
                                .displayFormId(groupDisplayFormId)
                                .productId(productId)
                                .rowNumber(dto.getRowNumber())
                                .build();

                        summedList.add(sumDto);
                    });

                }

                groupedByPathResult.put(path, summedList);
            }
        }

        // 第2步：对所有路径中的所有displayFormId+productId分组，找出每组rowNumber最大的
        Map<String, TPMDisplayReportService.TPMProductSkuRowNumberDTO> maxRowNumberMap = new HashMap<>();

        // 汇总所有路径的数据
        for (List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> pathProducts : groupedByPathResult.values()) {
            for (TPMDisplayReportService.TPMProductSkuRowNumberDTO dto : pathProducts) {
                String key = dto.getDisplayFormId() + ":" + dto.getProductId();

                // 如果当前产品的rowNumber比已存在的大，或者该key还不存在，则更新/添加
                if (!maxRowNumberMap.containsKey(key) || dto.getRowNumber() > maxRowNumberMap.get(key).getRowNumber()) {
                    maxRowNumberMap.put(key, dto);
                }
            }
        }

        // 转换最终结果为List

        return new ArrayList<>(maxRowNumberMap.values());
    }

    public List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> calculateMNProductSkuRowNumber(
            String tenantId,
            String displayFormId,
            List<TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList,
            List<DetectResultDTO> detectResultDTOS,
            Map<String, List<BatchQueryAIRuleByIds.Field>> objectFieldListMap
    ) {

        if (CollectionUtils.isEmpty(detectResultDTOS)) {
            return Lists.newArrayList();
        }

        BatchQueryAIRuleByIds.Field aiRowNumberField = objectFieldListMap.values().stream()
                .flatMap(List::stream)
                .filter(field -> "aiRowNumber".equals(field.getFieldKey()))
                .findFirst()
                .orElseThrow(null);

        if (Objects.isNull(aiRowNumberField)) {
            return Lists.newArrayList();
        }

        Integer calculateType = aiRowNumberField.getCalculateType();

        //group by img path
        Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> effectiveNPathTPMProductSkuRowNumberDTOMap = getEffectiveNPathMNTPMProductSkuRowNumberDTOMap(tenantId, displayFormId, tenantDetectFaultTolerantForDisPlayFormList, detectResultDTOS);

        // 第1步：对每个路径下的数据按displayFormId+productId分组求和rowNumber

        Map<String, TPMDisplayReportService.TPMProductSkuRowNumberDTO> rowNumberMap = new HashMap<>();
        if (calculateType == 3) {
            for (Map.Entry<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> entry : effectiveNPathTPMProductSkuRowNumberDTOMap.entrySet()) {
                List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> allDetectProducts = entry.getValue();
                for (TPMDisplayReportService.TPMProductSkuRowNumberDTO detectProduct : allDetectProducts) {
                    //根据display+product分组
                    String displayIdAndProductIdKey = detectProduct.getDisplayFormId() + ":" + detectProduct.getProductId();
                    TPMDisplayReportService.TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = rowNumberMap.get(displayIdAndProductIdKey);
                    if (tpmProductSkuRowNumberDTO == null) {
                        tpmProductSkuRowNumberDTO = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder()
                                .displayFormId(detectProduct.getDisplayFormId())
                                .productId(detectProduct.getProductId())
                                .rowNumber(detectProduct.getRowNumber())
                                .layer(detectProduct.getLayer())
                                .build();
                        rowNumberMap.put(displayIdAndProductIdKey, tpmProductSkuRowNumberDTO);
                        continue;
                    }
                    Double alreadyExistRowNumber = tpmProductSkuRowNumberDTO.getRowNumber();
                    Integer alreadyExistLayer = tpmProductSkuRowNumberDTO.getLayer();
                    tpmProductSkuRowNumberDTO = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder()
                            .displayFormId(detectProduct.getDisplayFormId())
                            .productId(detectProduct.getProductId())
                            .rowNumber(detectProduct.getRowNumber() + alreadyExistRowNumber)//排面数相加
                            .layer(detectProduct.getLayer() + alreadyExistLayer)//层数相加
                            .build();
                    rowNumberMap.put(displayIdAndProductIdKey, tpmProductSkuRowNumberDTO);
                }
            }
        }


        // 转换最终结果为List

        return new ArrayList<>(rowNumberMap.values());
    }

    @NotNull
    private static Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> getEffectiveNPathTPMProductSkuRowNumberDTOMap(String tenantId, String displayFormId, List<TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList, List<DetectResultDTO> detectResultDTOS) {
        //应拍陈列形式数据
        Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> effectiveNPathTPMProductSkuRowNumberDTOMap = Maps.newHashMap();
        for (DetectResultDTO detectResultDTO : detectResultDTOS) {
            List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
            for (JSONObject jsonObject : detectResultDTO.getFormData()) {
                String scene = jsonObject.getString("scene");
                if (StringUtils.isNotBlank(scene) && scene.equals(displayFormId)) {
                    TPMDisplayReportService.TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder().productId(jsonObject.getString("product_id"))
                            .displayFormId(displayFormId)
                            .rowNumber(jsonObject.getDouble("row_number"))
                            .build();
                    tpmProductSkuRowNumberDTOS.add(tpmProductSkuRowNumberDTO);
                }
            }
            effectiveNPathTPMProductSkuRowNumberDTOMap.put(detectResultDTO.getPath(), tpmProductSkuRowNumberDTOS);
        }

        if (TPMGrayUtils.isRioTenant(tenantId)) {
            for (DetectResultDTO detectResultDTO : detectResultDTOS) {
                String path = detectResultDTO.getPath();
                //单张照片中，同SKU不同陈列形式识别，需要SKU排面数求和。陈列形式为应拍陈列形式
                boolean existMainShelf = detectResultDTO.getFormData().stream().anyMatch(jsonObject -> displayFormId.equals(jsonObject.getString("scene")));
                if (existMainShelf) {
                    List<String> productIdList = detectResultDTO.getFormData().stream()
                            .filter(jsonObject -> displayFormId.equals(jsonObject.getString("scene")))
                            .map(jsonObject -> jsonObject.getString("product_id"))
                            .distinct()
                            .collect(Collectors.toList());
                    List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
                    detectResultDTO.getFormData().forEach(jsonObject -> {
                        String scene = jsonObject.getString("scene");
                        String productId = jsonObject.getString("product_id");
                        if (!scene.equals(displayFormId) && productIdList.contains(productId)) {
                            TPMDisplayReportService.TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder().productId(productId)
                                    .displayFormId(displayFormId)
                                    .rowNumber(jsonObject.getDouble("row_number"))
                                    .build();
                            tpmProductSkuRowNumberDTOS.add(tpmProductSkuRowNumberDTO);
                        }
                    });

                    if (CollectionUtils.isNotEmpty(tpmProductSkuRowNumberDTOS)) {
                        if (!effectiveNPathTPMProductSkuRowNumberDTOMap.containsKey(path)) {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.put(path, tpmProductSkuRowNumberDTOS);
                        } else {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.get(path).addAll(tpmProductSkuRowNumberDTOS);
                        }
                    }
                }
            }
        }

        //容错处理
        if (CollectionUtils.isNotEmpty(tenantDetectFaultTolerantForDisPlayFormList)) {
            Map<String, List<String>> detectFaultTolerantForDisPlayFormMap = tenantDetectFaultTolerantForDisPlayFormList.stream().collect(Collectors.toMap(TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm::getReliabilityValue, TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm::getFaultValues, (a, b) -> a));
            for (DetectResultDTO detectResultDTO : detectResultDTOS) {
                String path = detectResultDTO.getPath();
                if (detectFaultTolerantForDisPlayFormMap.containsKey(displayFormId)) {
                    List<String> detectFaultTolerantForDisPlayFormMapOrDefault = detectFaultTolerantForDisPlayFormMap.getOrDefault(displayFormId, Lists.newArrayList());
                    if (CollectionUtils.isEmpty(detectFaultTolerantForDisPlayFormMapOrDefault)) {
                        continue;
                    }
                    List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
                    detectResultDTO.getFormData().forEach(jsonObject -> {
                        String scene = jsonObject.getString("scene");
                        if (!scene.equals(displayFormId) && detectFaultTolerantForDisPlayFormMapOrDefault.contains(scene)) {
                            TPMDisplayReportService.TPMProductSkuRowNumberDTO tpmProductSkuRowNumberDTO = TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder().productId(jsonObject.getString("product_id"))
                                    .displayFormId(displayFormId)
                                    .rowNumber(jsonObject.getDouble("row_number"))
                                    .build();
                            tpmProductSkuRowNumberDTOS.add(tpmProductSkuRowNumberDTO);
                        }
                    });
                    if (CollectionUtils.isNotEmpty(tpmProductSkuRowNumberDTOS)) {
                        if (!effectiveNPathTPMProductSkuRowNumberDTOMap.containsKey(path)) {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.put(path, tpmProductSkuRowNumberDTOS);
                        } else {
                            effectiveNPathTPMProductSkuRowNumberDTOMap.get(path).addAll(tpmProductSkuRowNumberDTOS);
                        }
                    }
                }
            }
        }
        return effectiveNPathTPMProductSkuRowNumberDTOMap;
    }

    @NotNull
    private static Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> getEffectiveNPathMNTPMProductSkuRowNumberDTOMap(String tenantId, String displayFormId, List<TPMDisplayReportService.TenantDetectFaultTolerantForDisPlayForm> tenantDetectFaultTolerantForDisPlayFormList, List<DetectResultDTO> detectResultDTOS) {
        //应拍陈列形式数据
        Map<String, List<TPMDisplayReportService.TPMProductSkuRowNumberDTO>> effectiveNPathTPMProductSkuRowNumberDTOMap = Maps.newHashMap();
        for (DetectResultDTO detectResultDTO : detectResultDTOS) {
            effectiveNPathTPMProductSkuRowNumberDTOMap.put(detectResultDTO.getPath(), Lists.newArrayList());
        }

        for (DetectResultDTO detectResultDTO : detectResultDTOS) {
            List<TPMDisplayReportService.TPMProductSkuRowNumberDTO> tpmProductSkuRowNumberDTOS = Lists.newArrayList();
            for (ObjectDTO objectDTO : detectResultDTO.getObjectList()) {
                if (!Objects.equals(ApiNames.PRODUCT_OBJ, objectDTO.getApiName())) {
                    continue;
                }
                String skuSn = objectDTO.getSkuSn();
                if (StringUtils.isEmpty(skuSn)) {
                    continue;
                }
                String[] split = skuSn.split("-");
                if (split.length < 2) {
                    log.warn("skuSN length less than 2:{}", JSON.toJSONString(objectDTO));
                    continue;
                }
                if (!Objects.equals("1", split[1])) {
                    continue;
                }
                if (StringUtils.isNotBlank(objectDTO.getScene()) && Objects.equals(objectDTO.getScene(), displayFormId)) {
                    tpmProductSkuRowNumberDTOS.add(TPMDisplayReportService.TPMProductSkuRowNumberDTO.builder()
                            .productId(objectDTO.getDataId())
                            .rowNumber(1D)
                            .displayFormId(displayFormId)
                            .layer(objectDTO.getLayer()).build());

                }
            }
            effectiveNPathTPMProductSkuRowNumberDTOMap.get(detectResultDTO.getPath()).addAll(tpmProductSkuRowNumberDTOS);
        }
        return effectiveNPathTPMProductSkuRowNumberDTOMap;
    }
}
