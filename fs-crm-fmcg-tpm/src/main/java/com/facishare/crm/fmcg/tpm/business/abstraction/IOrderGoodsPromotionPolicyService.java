package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public interface IOrderGoodsPromotionPolicyService {

    Boolean isOpenPromotionPolicy(String tenantId);

    Boolean isOpenRebateByObj(int tenantId, String objApiName);

    OrderGoodsInit.Result init(OrderGoodsInit.Arg arg);

    void defaultValidateOrderGoods(ActivityTypePO activityTypePO, ActionContext actionContext, IObjectData getObjectData);

    OrderGoodsPromotionPolicyEdit.Result enableEditOrderGoodsPromotionPolicy(OrderGoodsPromotionPolicyEdit.Arg arg);

    OrderGoodsPromotionPolicyGet.Result get(OrderGoodsPromotionPolicyGet.Arg arg);

    /**
     * 保存订货会促销规则和返利规则，并冗余对应的 _copy 字段，方便在活动申请审批中状态显示
     *
     * @param toObjectData          活动申请
     * @param productPromotionJson  促销规则
     * @param rechargePromotionJson 返利规则
     */
    void saveOrUpdateOrderGoodsPromotionPolicyForApproval(String tenantId, String userId, IObjectData toObjectData, String productPromotionJson, String rechargePromotionJson);

    void createOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData, String productPromotionJson, String rechargePromotionJson);

    void updateOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData, String productPromotionJson, String rechargePromotionJson);

    void disableOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData);

    void enableOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData);

    void batchInvalidOrderGoodsSFAData(String tenantId, String userId, List<IObjectData> activities);

    /**
     * 过滤出使用了订货会促销规则的活动名称
     */
    List<String> queryUsedOrderGoodsPromotionActivityNames(String tenantId, String userId, List<IObjectData> activities);

    /**
     * 订货会活动申请适配的门店Filter
     */
    OrderGoodsQueryAccountFilter.Result orderGoodsQueryAccountFilter(OrderGoodsQueryAccountFilter.Arg arg);

    /**
     * 根据客户id查询可用的订货会活动申请
     */
    OrderGoodsMeetingFilter.Result orderGoodsMeetingFilter(OrderGoodsMeetingFilter.Arg arg);

    /**
     * 脚本代码，作废活动下的价格政策和返利产生政策
     */
    OrderGoodsInvalidByActivityId.Result invalidByActivityId(OrderGoodsInvalidByActivityId.Arg arg);

    /**
     * 脚本代码，恢复作废活动下的价格政策和返利产生政策
     */
    OrderGoodsEnableByActivityId.Result enableByActivityId(OrderGoodsEnableByActivityId.Arg arg);
}
