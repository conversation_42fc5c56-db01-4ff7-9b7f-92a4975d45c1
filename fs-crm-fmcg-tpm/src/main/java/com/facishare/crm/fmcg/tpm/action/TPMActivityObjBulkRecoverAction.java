package com.facishare.crm.fmcg.tpm.action;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.business.OrderGoodsPromotionPolicyService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/12 下午5:50
 */
public class TPMActivityObjBulkRecoverAction extends StandardBulkRecoverAction {

    public static final Logger log = LoggerFactory.getLogger(TPMActivityObjBulkRecoverAction.class);
    private final OrderGoodsPromotionPolicyService orderGoodsPromotionPolicyService = SpringUtil.getContext().getBean(OrderGoodsPromotionPolicyService.class);

    @Override
    protected void before(Arg arg) {
        List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getIdList(), ApiNames.TPM_ACTIVITY_OBJ);

        List<IObjectData> enableList = new ArrayList<>();
        List<String> laji = new ArrayList<>();
        for (IObjectData activity : activities) {
            String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE) == null ? "" : (String) activity.get(TPMActivityFields.BUDGET_TABLE);
            if (!Strings.isNullOrEmpty(budgetId)) {
                laji.add(activity.getName());
            } else {
                enableList.add(activity);
            }
        }
        if (!CollectionUtils.isEmpty(laji)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_RELATED_BUDGET_CAN_NOT_BE_RECOVER) + laji.toString());
        }
        enableList = enableList.stream()
                .filter(activity -> activity.get(TPMActivityFields.ORDER_GOODS_MEETING_FLAG, Boolean.class, false))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(enableList)) {
            log.info("enableList is not empty. ids : {}", enableList.stream().map(IObjectData::getId).collect(Collectors.toList()));
            List<IObjectData> finalEnableList = enableList;
            ParallelUtils.createParallelTask().submit(() -> {
                for (IObjectData activity : finalEnableList) {
                    orderGoodsPromotionPolicyService.enableOrderGoodsSFAData(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), activity);
                }
            }).run();
        }
        super.before(arg);
    }
}
