package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeTemplatePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ExceptionStatusType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.utils.ListParameterUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.NodeExceptionInfoVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ObjectVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityNodeTemplateManager;
import com.facishare.crm.fmcg.tpm.web.manager.dto.ApiNameCheckResult;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityNodeTemplateService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 向外暴露活动节点接口
 * <p>
 * create by @yangqf
 * create time 2021/11/15 17:42
 */
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class ActivityNodeTemplateService extends BaseService implements IActivityNodeTemplateService {

    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;
    @Resource
    private IActivityNodeTemplateManager activityNodeTemplateManager;
    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Override
    public AddActivityNodeTemplate.Result add(AddActivityNodeTemplate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        ActivityNodeTemplateVO vo = arg.getActivityNodeTemplate();

        activityNodeTemplateManager.basicInformationValidation(context.getTenantId(), vo);
        activityNodeTemplateManager.apiNameValidation(context.getTenantId(), vo);

        String id = activityNodeTemplateDAO.add(context.getTenantId(), context.getEmployeeId(),
                ActivityNodeTemplatePO.fromVO(vo));
        return AddActivityNodeTemplate.Result.builder().id(id).build();
    }

    @Override
    public DeleteActivityNodeTemplate.Result delete(DeleteActivityNodeTemplate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        if (activityNodeTemplateManager.isUsed(context.getTenantId(), arg.getId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_CAN_NOT_DELETE_ERROR));
        }
        if (activityNodeTemplateManager.isSystem(context.getTenantId(), arg.getId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_CAN_NOT_DELETE_ERROR));
        }
        activityNodeTemplateDAO.delete(context.getTenantId(), context.getEmployeeId(), arg.getId());
        return new DeleteActivityNodeTemplate.Result();
    }

    @Override
    public EditActivityNodeTemplate.Result edit(EditActivityNodeTemplate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        ActivityNodeTemplateVO vo = arg.getActivityNodeTemplate();
        vo.setExceptionStatus(ExceptionStatusType.NORMAL.value());
        vo.setNodeExceptionInfo(new NodeExceptionInfoVO(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.NORMAL.value()));

        ActivityNodeTemplatePO po = activityNodeTemplateDAO.get(context.getTenantId(), vo.getId());
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_FOUND_ERROR));
        }
        if (!po.getObjectApiName().equals(vo.getObjectApiName()) || !po.getReferenceFieldApiName().equals(vo.getReferenceFieldApiName())) {
            if (activityNodeTemplateManager.isUsed(context.getTenantId(), vo.getId())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_CAN_NOT_EDIT_OBJECT_ERROR));
            }
            if (activityNodeTemplateManager.isSystem(context.getTenantId(), vo.getId())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_CAN_NOT_EDIT_OBJECT_ERROR));
            }
        }
        activityNodeTemplateManager.basicInformationValidation(context.getTenantId(), vo.getId(), vo);
        activityNodeTemplateManager.apiNameValidation(context.getTenantId(), vo.getId(), vo);

        activityNodeTemplateDAO.edit(context.getTenantId(), context.getEmployeeId(), vo.getId(), vo);

        activityNodeTemplateManager.editPlatI18Key(context.getTenantId(), vo);
        return EditActivityNodeTemplate.Result.builder().build();
    }

    @Override
    public GetActivityNodeTemplate.Result get(GetActivityNodeTemplate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        if (Strings.isNullOrEmpty(arg.getId())) {
            throw new ValidateException("Invalid parameter error. Parameter '_id' can not be null or empty.");
        }
        ActivityNodeTemplatePO po = activityNodeTemplateDAO.get(context.getTenantId(), arg.getId());
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_EXISTS_ERROR));
        }

        ApiNameCheckResult apiNameCheckResult =
                activityNodeTemplateManager.apiNameCheck(context.getTenantId(),
                        po.getObjectApiName(),
                        po.getReferenceFieldApiName());

        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), po.getObjectApiName());
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(po.getReferenceFieldApiName());
        if (!Strings.isNullOrEmpty(po.getExceptionStatus())) {
            String exceptionStatus = po.getExceptionStatus();
            activityNodeTemplateManager.checkValidation(context.getTenantId(), po);
            if (!exceptionStatus.equals(po.getExceptionStatus())) {
                activityNodeTemplateDAO.editExceptionStatus(context.getTenantId(), po.getId().toHexString(), po);
            }
        }

        List<ActivityTypePO> relatedActivityTypes = activityTypeDAO.queryByNodeTemplateId(context.getTenantId(), arg.getId());

        return GetActivityNodeTemplate.Result.builder()
                .activityNodeTemplate(ActivityNodeTemplateVO.fromPO(
                        po,
                        relatedActivityTypes,
                        apiNameCheckResult,
                        describe.getDisplayName(),
                        fieldDescribe == null ? "" : fieldDescribe.getLabel())
                )
                .build();
    }

    @Override
    public ListActivityNodeTemplate.Result list(ListActivityNodeTemplate.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        activityNodeTemplateManager.tryInitSystemTemplate(context.getTenantId(), -10000);

        ListParameterUtils.correctListParameter(arg);

        long total = activityNodeTemplateDAO.count(context.getTenantId(), arg.getKeyword());
        List<ActivityNodeTemplateVO> data = activityNodeTemplateDAO.list(context.getTenantId(), arg.getKeyword(), arg.getLimit(), arg.getOffset())
                .stream()
                .map(ActivityNodeTemplateVO::fromPO)
                .collect(Collectors.toList());

        activityNodeTemplateManager.fillObjectDisplayName(context.getTenantId(), data);
        activityNodeTemplateManager.fillRelatedActivityTypes(context.getTenantId(), data);

        return ListActivityNodeTemplate.Result.builder()
                .data(data)
                .total(total)
                .build();
    }

    @Override
    public ActivityNodeTemplateEnableObjects.Result enableObjects(ActivityNodeTemplateEnableObjects.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        List<String> usedApiNames = activityNodeTemplateDAO.usedApiNames(context.getTenantId());
        List<IObjectDescribe> describes = serviceFacade.findObjectsByTenantId(context.getTenantId(), false, true, true, true);

        ActivityNodeTemplateEnableObjects.Result result = ActivityNodeTemplateEnableObjects.Result.builder().objects(Lists.newArrayList()).build();
        for (IObjectDescribe describe : describes) {
            if (!usedApiNames.contains(describe.getApiName())) {
                result.getObjects().add(ObjectVO.builder()
                        .apiName(describe.getApiName())
                        .displayName(describe.getDisplayName())
                        .build());
            }
        }
        return result;
    }

    @Override
    public DeleteActivityNodeTemplate.Result delete810(DeleteActivityNodeTemplate.Arg arg) {
        log.info("delete810 start");
        List<String> tenantIds = Lists.newArrayList("590268", "663116", "683675", "683722", "683723", "712664", "724456", "734676", "735029", "737219");
        List<ActivityNodeTemplatePO> activityNodeTemplatePOS = activityNodeTemplateDAO.allCostAssignList(tenantIds);
        for (ActivityNodeTemplatePO po : activityNodeTemplatePOS) {
            log.info("delete810  tenantId={}", po.getTenantId());
            activityNodeTemplateDAO.delete(po.getTenantId(), -10000, po.getId().toHexString());
        }
        log.info("delete810 end");
        return new DeleteActivityNodeTemplate.Result();
    }
}