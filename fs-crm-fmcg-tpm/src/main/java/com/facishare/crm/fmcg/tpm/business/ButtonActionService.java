package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IButtonActionService;
import com.facishare.crm.fmcg.tpm.common.constant.ButtonConstants;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.core.predef.service.dto.button.*;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.impl.UdefAction;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.omg.PortableInterceptor.SYSTEM_EXCEPTION;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/15 下午3:02
 */
@Slf4j
@Component
public class ButtonActionService implements IButtonActionService {


    private static final String SYSTEM_ROLE = "00000000000000000000000000000006";
    @Resource
    private ButtonService buttonService;

    public List<ActionPojo> getActions(ServiceContext serviceContext, String describeApiName, String buttonApiName) {
        FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
        arg.setButtonApiName(buttonApiName);
        arg.setDescribeApiName(describeApiName);
        FindButtonInfo.Result result = buttonService.findButtonInfo(arg, serviceContext);
        return result.getPost_actions();
    }

    public void deleteButtonActions(ServiceContext serviceContext, String describeApiName, String buttonApiName, List<String> actionIds) {
        if (CollectionUtils.isEmpty(actionIds)) {
            return;
        }
        DeleteButtonPostAction.Arg arg = new DeleteButtonPostAction.Arg();
        arg.setActionIds(actionIds);
        arg.setDescribeApiName(describeApiName);
        arg.setButtonApiName(buttonApiName);
        DeleteButtonPostAction.Result result = buttonService.deleteButtonPostAction(arg, serviceContext);
    }

    public void addUniqueButtonAction(List<ActionPojo> existsActions, List<IUdefAction> addActions, String describeApiName, String label, String stage, String bizKey) {
        for (ActionPojo actionPojo : existsActions) {
            if (Objects.equals(stage, actionPojo.getStage()) && Objects.equals(describeApiName, actionPojo.getDescribe_api_name())
                    && Objects.equals(bizKey, actionPojo.getBizKey()) && Objects.equals(ButtonConstants.Action.CUSTOM_BIZ, actionPojo.getAction_type())) {
                return;
            }
        }
        addActions.add(formUDefAction(describeApiName, label, stage, bizKey, null, null));
    }

    public IUdefAction formUDefAction(String describeApiName, String label, String stage, String bizKey, String remark, String actionParameters) {
        IUdefAction uDefAction = new UdefAction();
        uDefAction.setDescribeApiName(describeApiName);
        uDefAction.setActionType(ButtonConstants.Action.CUSTOM_BIZ);
        uDefAction.setLabel(label);
        uDefAction.setStage(stage);
        uDefAction.setRemark(remark);
        uDefAction.setActionParamter(actionParameters);
        uDefAction.set(ButtonConstants.Action.BIZ_KEY, bizKey);
        return uDefAction;
    }

    public void addActionsToButton(ServiceContext serviceContext, String describeApiName, String buttonApiName, List<IUdefAction> actions) {
        if (CollectionUtils.isEmpty(actions)) {
            return;
        }
        List<ActionPojo> actionPojos = actions.stream().map(ActionPojo::fromUDefAction).collect(Collectors.toList());
        UpdateButtonPostAction.Arg arg = UpdateButtonPostAction.Arg.builder().describeApiName(describeApiName).buttonApiName(buttonApiName)
                .udefAction(actionPojos).build();
        buttonService.updateButtonPostAction(arg, serviceContext);
    }

    public List<ButtonDocument> findButtonList(ServiceContext serviceContext, String describeApiName, boolean excludeUIButton) {
        FindButtonList.Arg arg = new FindButtonList.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setExcludeUIButton(excludeUIButton);
        FindButtonList.Result buttonList = buttonService.findButtonList(arg, serviceContext);
        return buttonList.getButtonList();
    }

    public void createButtonByRole(ServiceContext serviceContext, String buttonStr) {
        CreateButton.Arg arg = new CreateButton.Arg();
        arg.setButton(buttonStr);
        arg.setRoles(Lists.newArrayList(SYSTEM_ROLE));
        arg.setPost_actions(Lists.newArrayList());
        arg.setIgnoreDefaultRole(false);
        try {
            CreateButton.Result result = buttonService.create(arg, serviceContext);
        } catch (Exception e) {
            log.info("create button error, e: ", e);
        }
    }
}
