package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeTemplatePO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityNodeTemplate;
import com.facishare.crm.fmcg.tpm.web.manager.dto.ApiNameCheckResult;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 15:17
 */
public interface IActivityNodeTemplateManager {

    void fillObjectDisplayName(String tenantId, List<ActivityNodeTemplateVO> data);

    void fillRelatedActivityTypes(String tenantId, List<ActivityNodeTemplateVO> data);

    void basicInformationValidation(String tenantId, IActivityNodeTemplate nodeTemplate);

    void basicInformationValidation(String tenantId, String id, IActivityNodeTemplate nodeTemplate);

    void apiNameValidation(String tenantId, IActivityNodeTemplate nodeTemplate);

    void apiNameValidation(String tenantId, String id, IActivityNodeTemplate nodeTemplate);

    ApiNameCheckResult apiNameCheck(String tenantId, String objectApiName, String referenceFieldApiName);

    boolean isUsed(String tenantId, String id);

    boolean isSystem(String tenantId, String id);

    void tryInitSystemTemplate(String tenantId, int operator);

    String checkValidation(String tenantId, ActivityNodeTemplatePO nodeTemplate);

    void editPlatI18Key(String tenantId, ActivityNodeTemplateVO vo);
}
