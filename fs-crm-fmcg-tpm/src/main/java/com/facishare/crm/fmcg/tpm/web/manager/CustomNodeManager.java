package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.tpm.dao.TableColumnInfo;
import com.facishare.crm.fmcg.tpm.dao.TableFieldUtil;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.pg.ActivityUdefNodeMapper;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityPlanReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.ICustomNodeManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectReferenceWrapper;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 19:29
 */
@Component
public class CustomNodeManager implements ICustomNodeManager {

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityUdefNodeMapper activityUdefNodeMapper;

    @Override
    public ActivityTypeReportDatumVO loadActivityTypeReport(String tenantId, String activityTypeId, ActivityNodeEntity node, Map<String, String> displayNameMap, ActivityTypePO activityType) {
        ActivityTypeReportDatumVO defaultDatum = ActivityTypeReportDatumVO.builder()
                .nodeDisplayName(node.getName())
                .type(node.getType())
                .objectDisplayName(displayNameMap.getOrDefault(node.getObjectApiName(), "--"))
                .objectApiName(node.getObjectApiName())
                .count(0L)
                .build();

        IObjectDescribe describe = serviceFacade.findObject(tenantId, node.getObjectApiName());
        if (describe == null) {
            return defaultDatum;
        }

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        ObjectReferenceWrapper referenceField = null;
        for (ObjectReferenceWrapper field : describeExt.getReferenceFieldDescribes()) {
            if (field.getApiName().equals(node.getReferenceActivityFieldApiName())) {
                referenceField = field;
            }
        }
        if (referenceField == null) {
            return defaultDatum;
        }

        String tableName = Strings.isNullOrEmpty(describeExt.getStoreTableName()) ? "mt_data" : describeExt.getStoreTableName();
        boolean isMtData = tableName.equals("mt_data");
        TableColumnInfo referenceActivityColumn = TableFieldUtil.columnInfo(describe, node.getReferenceActivityFieldApiName());
        TableColumnInfo lifeStatusColumn = TableFieldUtil.columnInfo(describe, CommonFields.LIFE_STATUS);

        long count = activityUdefNodeMapper.setTenantId(tenantId).queryActivityTypeStatisticsData(
                tenantId,
                activityTypeId,
                tableName,
                isMtData,
                referenceActivityColumn.isExtend(),
                referenceActivityColumn.getColumnName(),
                lifeStatusColumn.isExtend(),
                lifeStatusColumn.getColumnName());

        return ActivityTypeReportDatumVO.builder()
                .templateId(node.getTemplateId())
                .type(node.getType())
                .nodeDisplayName(node.getName())
                .objectDisplayName(displayNameMap.getOrDefault(node.getObjectApiName(), "--"))
                .objectApiName(node.getObjectApiName())
                .count(count)
                .build();
    }

    @Override
    public ActivityPlanReportDatumVO loadActivityPlanReport(String tenantId, String activityPlanId) {
        return ActivityPlanReportDatumVO.builder().build();
    }

    public ActivityPlanReportDatumVO loadActivityPlanReport(String tenantId, String activityPlanId, String apiName, String relatedFieldName, String recordType) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setFindExplicitTotalNum(true);
        query.setNeedReturnCountNum(true);
        query.setNeedReturnQuote(false);
        query.setNeedRightJoin(false);

        query.setLimit(1);
        query.setOffset(0);

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(relatedFieldName);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityPlanId));

        IFilter recordTypeFilter = new Filter();
        recordTypeFilter.setFieldName(CommonFields.RECORD_TYPE);
        recordTypeFilter.setOperator(Operator.EQ);
        recordTypeFilter.setFieldValues(Lists.newArrayList(recordType));

        query.setFilters(Lists.newArrayList(activityIdFilter, recordTypeFilter));

        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(tenantId), apiName, query);
        return ActivityPlanReportDatumVO.builder()
                .count(result.getTotalNumber())
                .build();
    }
}
