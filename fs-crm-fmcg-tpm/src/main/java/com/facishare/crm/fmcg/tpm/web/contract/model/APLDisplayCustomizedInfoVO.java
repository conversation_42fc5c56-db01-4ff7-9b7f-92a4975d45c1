package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class APLDisplayCustomizedInfoVO implements Serializable {
    @JSONField(name = "tenant_id")
    @SerializedName("tenant_id")
    @JsonProperty("tenant_id")
    private String tenantId;

    @JSONField(name = "object_api_name")
    @SerializedName("object_api_name")
    @JsonProperty("object_api_name")
    private String objectApiName;

    @JSONField(name = "function_info")
    @SerializedName("function_info")
    @JsonProperty("function_info")
    private FunctionInfo functionInfo;
}

