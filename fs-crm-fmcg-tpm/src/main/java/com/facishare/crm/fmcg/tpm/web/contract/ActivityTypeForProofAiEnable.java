package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


public interface ActivityTypeForProofAiEnable {

    @Data
    @ToString
    class Arg implements Serializable {

        private List<String> ids;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<EnableVO> data;

    }

    @Data
    @ToString
    class EnableVO implements Serializable {

        private String id;

        @JsonProperty(value = "enable_ai")
        @SerializedName("enable_ai")
        @JSONField(name = "enable_ai")
        private boolean enableAi;
    }

}
