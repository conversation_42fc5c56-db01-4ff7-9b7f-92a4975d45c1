package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetProvisionService;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetNewConsumeRuleVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IBudgetConsumeRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetConsumeRuleService;
import com.facishare.crm.fmcg.tpm.web.utils.ListParameterUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/15 上午10:52
 */
@Slf4j
@Service
public class BudgetNewConsumeRuleService extends BaseService implements IBudgetConsumeRuleService {


    @Resource
    private BudgetNewConsumeRuleDAO budgetNewConsumeRuleDAO;
    @Resource
    private IBudgetConsumeRuleManager budgetConsumeRuleManager;
    @Resource
    private IBudgetAccountDetailService budgetAccountDetailService;
    @Resource
    private IBudgetProvisionService budgetProvisionService;

    private static final String PLUGIN_NAME = "budget_consume_new_mode";

    private static final Set<String> VALIDATE_API_NAME = new HashSet<>();

    static {
        VALIDATE_API_NAME.add(ApiNames.TPM_ACTIVITY_OBJ);
        VALIDATE_API_NAME.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
    }

    @Override
    public AddBudgetConsumeRule.Result add(AddBudgetConsumeRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        String tenantId = context.getTenantId();
        //规则校验
        budgetConsumeRuleManager.consumeRuleInfoValidate(tenantId, arg.getBudgetNewConsumeRuleVO());
        //规则条件选择配置
        budgetConsumeRuleManager.enclosureConditionFilter(tenantId, context.getEmployeeId(), arg.getBudgetNewConsumeRuleVO());

        BudgetNewConsumeRulePO budgetNewConsumeRulePO = BudgetNewConsumeRulePO.fromVO(arg.getBudgetNewConsumeRuleVO());
        //add
        String id = budgetNewConsumeRuleDAO.add(tenantId, context.getEmployeeId(), budgetNewConsumeRulePO);
        //启用 - 给规则的业务对象绑定 插件
        // 如果规则类型是冻结扣减，并且扣减的类型是其他对象，需要给其他对象也绑定上插件。
        bindPluginByObject(budgetNewConsumeRulePO.getRuleStatus(), context, budgetNewConsumeRulePO);

        //当自定义对象配置消费规则，且消费方式为 冻结扣减，在业务对象中添加 结案按钮。
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRulePO.getRuleType())
                && !VALIDATE_API_NAME.contains(budgetNewConsumeRulePO.getApiName())) {
            budgetConsumeRuleManager.addClosureComponents(tenantId, context.getEmployeeId(), budgetNewConsumeRulePO.getApiName());
        }
        //启用了预提，则判断预提的字段是否都存在
        if (ConsumeConfigType.PROVISION_ENABLE.value().equals(budgetNewConsumeRulePO.getProvisionStatus())){
            budgetConsumeRuleManager.addBudgetProvisionField(tenantId, budgetNewConsumeRulePO);
        }
        asyncAddBudgetConsumerRuleLog(context.getEmployeeId(), tenantId, budgetNewConsumeRulePO);
        return AddBudgetConsumeRule.Result.builder().budgetNewConsumeRuleVO(loadBudgetConsumeRuleVO(tenantId, id)).build();
    }

    private void asyncAddBudgetConsumerRuleLog(int employeeId, String tenantId, BudgetNewConsumeRulePO consumeRule) {
        String apiName = consumeRule.getApiName();
        if (apiName.endsWith("__c")) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_CUSTOM_OBJ, BuryOperation.CREATE);
        } else {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_PRESET_OBJ, BuryOperation.CREATE);
        }
        if (apiName.equals(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ)) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_PRESET_OBJ + "_" + ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, BuryOperation.CREATE);
        } else if (apiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_PRESET_OBJ + "_" + ApiNames.TPM_ACTIVITY_OBJ, BuryOperation.CREATE);
        }
        List<BudgetTableAutomaticNodeEntity> automaticNodes = consumeRule.getBudgetTableAutomaticNodes();
        if (automaticNodes != null && automaticNodes.size() > 1) {
            BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_OBJ_COUNT, BuryOperation.CREATE);
        }

        String ruleType = consumeRule.getRuleType();
        switch (ConsumeRuleType.of(ruleType)) {
            case FREEZE_DEDUCTION:
                BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_FREEZE_DEDUCTION, BuryOperation.CREATE);
                break;
            case DEDUCTION:
                BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_DEDUCTION, BuryOperation.CREATE);
                break;
            default:
                break;
        }
        Integer deductType = consumeRule.getDeductType();
        switch (ConsumeDeductType.of(deductType)) {
            case DEDUCT_SELF:
                BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_DEDUCT_SELF, BuryOperation.CREATE);
                break;
            case DEDUCT_OTHER:
                BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_DEDUCT_OTHER, BuryOperation.CREATE);
                break;
            default:
                break;
        }

        String budgetMethod = consumeRule.getBudgetMethod();
        switch (BudgetMethodEnum.of(budgetMethod)) {
            case AUTOMATIC:
                BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_AUTOMATIC, BuryOperation.CREATE);
                break;
            case MANUAL:
                BuryService.asyncBudgetLog(tenantId, employeeId, BuryModule.Budget.BUDGET_CONSUME_RULE_MANUAL, BuryOperation.CREATE);
                break;
            default:
                break;
        }

    }


    @Override
    public GetBudgetConsumeRule.Result get(GetBudgetConsumeRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (Strings.isNullOrEmpty(arg.getId())) {
            throw new ValidateException("Invalid parameter error. Parameter '_id' can not be null or empty.");
        }
        //查询流水
        boolean flag = budgetAccountDetailService.existsConsumeRuleDetailByRuleId(context.getTenantId(), arg.getId());
        BudgetNewConsumeRuleVO budgetNewConsumeRuleVO = loadBudgetConsumeRuleVO(context.getTenantId(), arg.getId());
        //预存规则状态校验
        if (ConsumeConfigType.PROVISION_ENABLE.value().equals(budgetNewConsumeRuleVO.getProvisionStatus())
                && Boolean.FALSE.equals(flag)){
            flag = budgetProvisionService.existsBudgetProvisionByRuleId(context.getTenantId(), arg.getId());
        }
        return GetBudgetConsumeRule.Result.builder()
                .budgetNewConsumeRuleVO(budgetNewConsumeRuleVO)
                .flowState(flag)
                .build();

    }

    @Override
    public ListBudgetConsumeRule.Result list(ListBudgetConsumeRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        ListParameterUtils.correctListParameter(arg);
        List<BudgetNewConsumeRulePO> data = budgetNewConsumeRuleDAO.list(
                context.getTenantId(),
                arg.getKeyword(),
                arg.getObjectApiName(),
                arg.getRuleType(),
                arg.getBudgetMethod(),
                arg.getLimit(), arg.getOffset());

        long count = budgetNewConsumeRuleDAO.countAll(context.getTenantId(),
                arg.getKeyword(), arg.getObjectApiName(), arg.getRuleType(), arg.getBudgetMethod());
        return ListBudgetConsumeRule.Result.builder()
                .data(loadListBudgetConsumeRuleVO(context.getTenantId(), data))
                .total(count)
                .build();
    }


    @Override
    public EditBudgetConsumeRule.Result edit(EditBudgetConsumeRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        String id = arg.getBudgetNewConsumeRuleVO().getId();
        String ruleStatus = arg.getBudgetNewConsumeRuleVO().getRuleStatus();
        String tenantId = context.getTenantId();
        BudgetNewConsumeRulePO oldPO = budgetNewConsumeRuleDAO.get(tenantId, id);
        if (oldPO == null) {
            throw new ValidateException("budgetNewConsumeRulePO is not found...");
        }
        // version 校验 ....
        if (oldPO.getVersion() != arg.getBudgetNewConsumeRuleVO().getVersion()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_VERSION_VALIDATION_ERROR));
        }
        //查询流水
        boolean flag = budgetAccountDetailService.existsConsumeRuleDetailByRuleId(tenantId, id);
        //预存规则状态校验
        if (ConsumeConfigType.PROVISION_ENABLE.value().equals(arg.getBudgetNewConsumeRuleVO().getProvisionStatus())
                && Boolean.FALSE.equals(flag)){
            flag = budgetProvisionService.existsBudgetProvisionByRuleId(tenantId, id);
        }
        //规则状态为禁用，消费规则未被数据关联时，才支持编辑
        if (StatusType.ENABLE.value().equals(ruleStatus) || flag) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_STATUS_EDIT_ERROR));
        }
        //规则校验
        budgetConsumeRuleManager.consumeRuleEditValidate(tenantId, arg.getBudgetNewConsumeRuleVO(), oldPO);
        //规则条件选择配置
        budgetConsumeRuleManager.enclosureConditionFilter(tenantId, context.getEmployeeId(), arg.getBudgetNewConsumeRuleVO());

        BudgetNewConsumeRulePO consumeRulePO = BudgetNewConsumeRulePO.fromEditVO(oldPO, arg.getBudgetNewConsumeRuleVO());
        //规则状态=禁用 and 消费规则未被数据关联时，支持编辑
        budgetNewConsumeRuleDAO.edit(tenantId, context.getEmployeeId(), id, consumeRulePO);

        budgetConsumeRuleManager.editPlatI18Key(tenantId, arg.getBudgetNewConsumeRuleVO());
        return EditBudgetConsumeRule.Result.builder()
                .budgetNewConsumeRuleVO(loadBudgetConsumeRuleVO(context.getTenantId(), arg.getBudgetNewConsumeRuleVO().getId()))
                .build();
    }

    @Override
    public SetBudgetConsumeRuleStatus.Result setRuleStatus(SetBudgetConsumeRuleStatus.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        BudgetNewConsumeRulePO budgetNewConsumeRulePO = budgetNewConsumeRuleDAO.get(context.getTenantId(), arg.getId());
        if (budgetNewConsumeRulePO == null) {
            throw new ValidateException("BudgetNewConsumeRulePO is not found...");
        }
        //如果更改状态为启用，判断是否已有启用的规则
        validateEnableStatus(arg, context, budgetNewConsumeRulePO);
        //更新状态
        budgetNewConsumeRuleDAO.setRuleStatus(context.getTenantId(), context.getEmployeeId(), arg.getId(), arg.getRuleStatus());
        //启用 - 给规则的业务对象绑定 插件
        bindPluginByObject(arg.getRuleStatus(), context, budgetNewConsumeRulePO);

        return SetBudgetConsumeRuleStatus.Result.builder()
                .budgetNewConsumeRuleVO(BudgetNewConsumeRulePO.toVO(budgetNewConsumeRuleDAO.get(context.getTenantId(), arg.getId())))
                .build();
    }

    private void bindPluginByObject(String status, ApiContext context, BudgetNewConsumeRulePO budgetNewConsumeRulePO) {
        if (StatusType.ENABLE.value().equals(status)) {
            budgetConsumeRuleManager.bindObjectPluginInstance(context.getTenantId(), context.getEmployeeId(), budgetNewConsumeRulePO.getApiName(), PLUGIN_NAME);
            if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRulePO.getRuleType())
                    && ConsumeDeductType.DEDUCT_OTHER.value().equals(budgetNewConsumeRulePO.getDeductType())) {
                budgetConsumeRuleManager.bindObjectPluginInstance(context.getTenantId(), context.getEmployeeId(), budgetNewConsumeRulePO.getDeductApiName(), PLUGIN_NAME);
            }
        }
        // todo 释放对象的配置项，给对应的对象绑定插件。
        if (ConsumeConfigType.RELEASE_ENABLE.value().equals(budgetNewConsumeRulePO.getReleaseStatus())){
            budgetNewConsumeRulePO.getReleaseList().forEach(node -> {
                budgetConsumeRuleManager.bindObjectPluginInstance(context.getTenantId(), context.getEmployeeId(), node.getReleaseApiName(), PLUGIN_NAME);
            });
        }
    }

    private void validateEnableStatus(SetBudgetConsumeRuleStatus.Arg arg, ApiContext context, BudgetNewConsumeRulePO budgetNewConsumeRulePO) {
        if (StatusType.ENABLE.value().equals(arg.getRuleStatus())) {
            if (!arg.isForceEnable()) {
                budgetConsumeRuleManager.existsEnableRuleValidate(context.getTenantId(), budgetNewConsumeRulePO);
                //启用规则校验
            }
        } else {
            // 更改为禁用，判断是否为 先冻结后扣减的，如果有 ，则需要判断 是否没有走完流程，，
            if ((ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRulePO.getRuleType())
                    || ApprovalTriggerTime.BEFORE.value().equals(budgetNewConsumeRulePO.getRuleTypeNodes().get(0).getApprovalTriggerTime()))
                    && !budgetConsumeRuleManager.isProcessFlowCompleted(context.getTenantId(), arg.getId())) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_FLOW_COMPLETED_ERROR));
            }
        }
    }

    @Override
    public DeleteBudgetConsumeRule.Result delete(DeleteBudgetConsumeRule.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        BudgetNewConsumeRulePO budgetNewConsumeRulePO = budgetNewConsumeRuleDAO.get(context.getTenantId(), arg.getId());
        if (budgetNewConsumeRulePO == null) {
            throw new ValidateException("budgetNewConsumeRulePO is not found...");
        }
        //-- 判断是否被业务数据关联上。
        boolean flag = budgetAccountDetailService.existsConsumeRuleDetailByRuleId(context.getTenantId(), arg.getId());
        //预存规则状态校验
        if (ConsumeConfigType.PROVISION_ENABLE.value().equals(budgetNewConsumeRulePO.getProvisionStatus())
                && Boolean.FALSE.equals(flag)){
            flag = budgetProvisionService.existsBudgetProvisionByRuleId(context.getTenantId(), arg.getId());
        }
        if (budgetNewConsumeRulePO.getRuleStatus().equals(StatusType.DISABLE.value()) && !flag) {
            //删除
            budgetNewConsumeRuleDAO.delete(context.getTenantId(), context.getEmployeeId(), arg.getId());
            //删除插件
            deleteObjectPlugin(context, budgetNewConsumeRulePO);
        } else {
            // 规则状态必须是为禁用且未被业务数据关联。
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_RULE_STATUS_DELETE_ERROR));
        }
        return DeleteBudgetConsumeRule.Result.builder().build();
    }

    private void deleteObjectPlugin(ApiContext context, BudgetNewConsumeRulePO budgetNewConsumeRulePO) {
        budgetConsumeRuleManager.deleteObjectPluginInstance(context.getTenantId(),
                budgetNewConsumeRulePO.getApiName(), budgetNewConsumeRulePO.getRecordType(), PLUGIN_NAME);
        // 如果是扣减其他对象，其他对象也需判断删除插件，
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(budgetNewConsumeRulePO.getRuleType())
                && ConsumeDeductType.DEDUCT_OTHER.value().equals(budgetNewConsumeRulePO.getDeductType())) {
            budgetConsumeRuleManager.deleteObjectPluginInstance(context.getTenantId(),
                    budgetNewConsumeRulePO.getDeductApiName(), budgetNewConsumeRulePO.getDeductRecordType(), PLUGIN_NAME);
        }
    }


    private BudgetNewConsumeRuleVO loadBudgetConsumeRuleVO(String tenantId, String id) {
        BudgetNewConsumeRuleVO budgetConsumeRuleVO = BudgetNewConsumeRulePO.toVO(budgetNewConsumeRuleDAO.get(tenantId, id));
        // 业务对象 的业务类型，label
        IObjectDescribe describe = serviceFacade.findObject(tenantId, budgetConsumeRuleVO.getApiName());
        if (Objects.nonNull(describe)) {
            if (VALIDATE_API_NAME.contains(budgetConsumeRuleVO.getApiName())) {
                budgetConsumeRuleVO.setRecordTypeLabel(fillActivityTypeLabel(describe, budgetConsumeRuleVO.getRecordType()));
            } else {
                budgetConsumeRuleVO.setRecordTypeLabel(fillRecordTypeLabel(describe, budgetConsumeRuleVO.getRecordType()));
            }
        }
        return budgetConsumeRuleVO;
    }

    public BudgetNewConsumeRuleVO findConsumeRuleById(String tenantId, String id) {
        return BudgetNewConsumeRulePO.toVO(budgetNewConsumeRuleDAO.get(tenantId, id));
    }

    private List<BudgetNewConsumeRuleVO> loadListBudgetConsumeRuleVO(String tenantId, List<BudgetNewConsumeRulePO> data) {
        //业务类型 label
        Set<String> apiNames = data.stream().map(BudgetNewConsumeRulePO::getApiName).collect(Collectors.toSet());
        Map<String, IObjectDescribe> describes = serviceFacade.findObjects(tenantId, apiNames);

        List<BudgetNewConsumeRuleVO> budgetConsumeRuleVOS = new ArrayList<>();
        for (BudgetNewConsumeRulePO po : data) {
            BudgetNewConsumeRuleVO budgetConsumeRuleVO = BudgetNewConsumeRulePO.toVO(po);
            //填充业务类型 label
            IObjectDescribe objectDescribe = describes.get(po.getApiName());
            if (objectDescribe != null && Objects.nonNull(budgetConsumeRuleVO)) {
                if (VALIDATE_API_NAME.contains(budgetConsumeRuleVO.getApiName())) {
                    budgetConsumeRuleVO.setRecordTypeLabel(fillActivityTypeLabel(objectDescribe, budgetConsumeRuleVO.getRecordType()));
                } else {
                    budgetConsumeRuleVO.setRecordTypeLabel(fillRecordTypeLabel(objectDescribe, budgetConsumeRuleVO.getRecordType()));
                }
            }
            budgetConsumeRuleVOS.add(budgetConsumeRuleVO);
        }
        return budgetConsumeRuleVOS;
    }

    private String fillRecordTypeLabel(IObjectDescribe describe, String recordType) {
        // 业务对象 的业务类型，label
        IFieldDescribe fieldDescribe = describe.getFieldDescribe("record_type");
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        if (!CollectionUtils.isEmpty(options)) {
            return options.stream()
                    .filter(v -> recordType.equals(v.get("api_name")))
                    .map(v -> v.get("label"))
                    .findFirst().orElse("");
        }
        return null;
    }


    private String fillActivityTypeLabel(IObjectDescribe describe, String activityType) {
        IFieldDescribe fieldDescribe = describe.getFieldDescribe("activity_type");
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        if (!CollectionUtils.isEmpty(options)) {
            return options.stream()
                    .filter(v -> activityType.equals(v.get("value")))
                    .map(v -> v.get("label"))
                    .findFirst().orElse("");
        }
        return null;
    }


    public ListBusinessObject.Result listBusinessObject(ListBusinessObject.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        return ListBusinessObject.Result.builder()
                .objectLabels(budgetConsumeRuleManager.findObjectsByTenantId(context.getTenantId()))
                .enableRelease(TPMGrayUtils.isConsumeRuleEnableRelease(context.getTenantId()))
                .enableProvision(TPMGrayUtils.isConsumeRuleEnableProvision(context.getTenantId()))
                .releaseObjectLabels(budgetConsumeRuleManager.getReleaseObjectLabels(context.getTenantId()))
                .build();
    }

    public GetConsumeObject.Result getConsumeObjects(GetConsumeObject.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        Map<String, Map<String, String>> consumeObjects = budgetConsumeRuleManager.getConsumeObjects(context.getTenantId());
        return GetConsumeObject.Result.builder()
                .consumeObjects(consumeObjects.get(BudgetNewConsumeRulePO.F_API_NAME))
                .deductObjects(consumeObjects.get(BudgetNewConsumeRulePO.F_DEDUCT_API_NAME))
                .build();
    }

    public DeletePluginObject.Result deletePlugin(DeletePluginObject.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        return DeletePluginObject.Result.builder()
                .nums(budgetConsumeRuleManager.deleteUseLessPlugin(context.getTenantId(), arg.getPluginName()))
                .build();
    }
}
