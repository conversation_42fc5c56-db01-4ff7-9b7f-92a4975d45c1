package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.rule.RewardRuleDTO;
import com.facishare.crm.fmcg.tpm.api.rule.UpdateRewardRule;
import com.facishare.crm.fmcg.tpm.common.constant.OrderGoodsContents;
import com.facishare.crm.fmcg.tpm.business.OrderGoodsPromotionPolicyService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityRewardRuleService;
import com.facishare.crm.fmcg.tpm.api.consume.EndConsume;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.business.UnifiedActivityCommonLogicBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IEnableCacheService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.GlobalConstant;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.PromotionPolicyService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/30 11:25
 */
@Slf4j
@SuppressWarnings("Duplicates,unused")
public class TPMActivityObjFlowCompletedAction extends StandardFlowCompletedAction {

    private final UnifiedActivityCommonLogicBusiness unifiedActivityCommonLogicBusiness = SpringUtil.getContext().getBean(UnifiedActivityCommonLogicBusiness.class);

    private final IBudgetConsumeV2Service budgetConsumeV2 = SpringUtil.getContext().getBean(IBudgetConsumeV2Service.class);

    private final IActivityRewardRuleService activityRewardRuleService = SpringUtil.getContext().getBean(IActivityRewardRuleService.class);

    private IObjectData beforeDoActActivity;
    private IObjectData afterDoActActivity;

    private final IEnableCacheService enableCacheService = SpringUtil.getContext().getBean(IEnableCacheService.class);
    private final PromotionPolicyService promotionPolicyService = SpringUtil.getContext().getBean(PromotionPolicyService.class);
    private final OrderGoodsPromotionPolicyService orderGoodsPromotionPolicyService = SpringUtil.getContext().getBean(OrderGoodsPromotionPolicyService.class);
    public final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private String ACTIVITY_TYPE_TEMPLATE_ID = "";
    private String PRODUCT_PROMOTION_JSON = "";
    private String RECHARGE_PROMOTION_JSON = "";

    @Override
    protected void before(Arg arg) {
        log.info("TPMActivityObj flow complete call back parameters : {}", arg);
        super.before(arg);
        initData();
    }

    private void initData() {
        this.beforeDoActActivity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getDataId(), arg.getDescribeApiName());
        String activityTypeId = this.beforeDoActActivity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        if (!Strings.isNullOrEmpty(activityTypeId)) {
            log.info("activity type id is {}", activityTypeId);
            ActivityTypeExt activityTypeExt = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
            ACTIVITY_TYPE_TEMPLATE_ID = activityTypeExt.get().getTemplateId();
        }
        Object callbackData = arg.getCallbackData();
        if (Objects.nonNull(callbackData)) {
            JSONObject callback = JSON.parseObject(JSON.toJSONString(callbackData));
            PRODUCT_PROMOTION_JSON = callback.getString(TPMActivityFields.PRODUCT_PROMOTION_JSON);
            RECHARGE_PROMOTION_JSON = callback.getString(TPMActivityFields.RECHARGE_PROMOTION_JSON);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Result finalResult = super.doAct(arg);

        this.afterDoActActivity = serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), arg.getDataId(), arg.getDescribeApiName());

        switch (arg.getTriggerType()) {
            case "1":
                this.createActionCompleted();
                break;
            case "2":
                this.updateActionCompleted();
                break;
            case "CloseTPMActivity_button_default":
                this.closeActionCompleted();
                break;
            default:
                break;
        }
        return finalResult;
    }

    private void closeActionCompleted() {
        long now = System.currentTimeMillis();
        long end = this.afterDoActActivity.get(TPMActivityFields.END_DATE, Long.class);

        Map<String, Object> update = new HashMap<>();
        update.put(CommonFields.LIFE_STATUS, "normal");

        if (arg.isPass()) {
            update.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
            update.put(TPMActivityFields.CLOSE_TIME, now);
            if (end >= now) {
                update.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__CLOSED);
            } else {
                update.put(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__END);
            }
            EndConsume.Result endResult = budgetConsumeV2.endConsume(actionContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, arg.getDataId(), true);
            if (endResult.getNeedConfirm()) {
                throw new ValidateException(endResult.getTips());
            }
        } else {
            String status = calculateActivityStatus(afterDoActActivity);
            update.put(TPMActivityFields.ACTIVITY_STATUS, status);
        }

        unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), afterDoActActivity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));
        serviceFacade.updateWithMap(actionContext.getUser(), afterDoActActivity, update);
        this.disableProofEnableCacheAsync();
        //结案后更新促销类活动的政策状态
        handlePromotionPolicy(afterDoActActivity);
        //结案后禁用订货会价格政策及充值返现
        handleOrderGoodsPromotion(afterDoActActivity);
    }

    private void handleOrderGoodsPromotion(IObjectData objectData) {
        if (arg.isPass()) {
            if (orderGoodsPromotionPolicyService.judgedIsOrderGoodsPromotionData(objectData)) {
                orderGoodsPromotionPolicyService.disableOrderGoodsSFAData(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);
            }
        }
    }

    private void handlePromotionPolicy(IObjectData objectData) {
        if (arg.isPass()) {
            if (promotionPolicyService.judgedIsPromotionPolicyData(objectData)) {
                promotionPolicyService.disablePromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);
            }
        }
    }

    private void createActionCompleted() {

        String status;
        if (arg.isPass()) {
            status = calculateActivityStatus(afterDoActActivity);
        } else {
            status = TPMActivityFields.ACTIVITY_STATUS__INEFFECTIVE;
        }

        Map<String, Object> updater = new HashMap<>();
        updater.put(TPMActivityFields.ACTIVITY_STATUS, status);
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), afterDoActActivity, updater);

        unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), afterDoActActivity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));

        this.disableProofEnableCacheAsync();

        // 促销类 新增促销规则
        createSFAPromotionPolicy(this.afterDoActActivity);
        // 订货类
        createOrderGoodsPromotion(this.afterDoActActivity, PRODUCT_PROMOTION_JSON, RECHARGE_PROMOTION_JSON);
    }

    private void createOrderGoodsPromotion(IObjectData objectData, String productPromotionJson, String rechargePromotionJson) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) &&
                !ACTIVITY_TYPE_TEMPLATE_ID.startsWith(OrderGoodsContents.ORDER_GOODS_TEMPLATE_ID) ||
                !Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
            return;
        }
        if (arg.isPass()) {
            objectData.set(TPMActivityFields.PRODUCT_PROMOTION_JSON, PRODUCT_PROMOTION_JSON);
            objectData.set(TPMActivityFields.RECHARGE_PROMOTION_JSON, RECHARGE_PROMOTION_JSON);
            orderGoodsPromotionPolicyService.createOrderGoodsSFAData(actionContext.getTenantId(),
                    actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                    objectData,
                    productPromotionJson,
                    rechargePromotionJson
            );
        }
    }

    private void createSFAPromotionPolicy(IObjectData objectData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) &&
                !ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion") ||
                !Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
            return;
        }
        if (arg.isPass()) {
            promotionPolicyService.createSFAPromotionPolicy(actionContext.getTenantId(),
                    actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                    objectData);
        } else {
            promotionPolicyService.revertPromotionPolicyRule(actionContext.getTenantId(),
                    actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                    objectData);
        }
    }

    private void updateActionCompleted() {
        String beforeDoActCloseStatus = beforeDoActActivity.get(TPMActivityFields.CLOSED_STATUS, String.class);

        String status;

        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(beforeDoActCloseStatus)) {
            status = TPMActivityFields.ACTIVITY_STATUS__CLOSED;
        } else {
            status = calculateActivityStatus(this.afterDoActActivity);
            if (TPMGrayUtils.isLjjUpdateStatusByCloseDate(actionContext.getTenantId())) {
                status = calculateActivityStatusByCloseData(status);
            }
        }

        Map<String, Object> updater = new HashMap<>();
        updater.put(TPMActivityFields.ACTIVITY_STATUS, status);
        serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), afterDoActActivity, updater);

        unifiedActivityCommonLogicBusiness.recalculateUnifiedAmountField(actionContext.getTenantId(), afterDoActActivity.get(TPMActivityFields.ACTIVITY_UNIFIED_CASE_ID, String.class));

        this.disableProofEnableCacheAsync();


        if (arg.isPass()) {
            // 促销类 更新促销规则
            updateSFAPromotionPolicy(this.afterDoActActivity);
            // 订货类
            updateOrderGoodsPromotion(this.afterDoActActivity);
            //更新记录
            editRewardRule();
        }
    }

    private void updateOrderGoodsPromotion(IObjectData objectData) {
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) &&
                !ACTIVITY_TYPE_TEMPLATE_ID.startsWith(OrderGoodsContents.ORDER_GOODS_TEMPLATE_ID) ||
                !Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
            return;
        }

        if (StringUtils.isNotBlank(PRODUCT_PROMOTION_JSON) || StringUtils.isNotBlank(RECHARGE_PROMOTION_JSON)) {
            objectData.set(TPMActivityFields.PRODUCT_PROMOTION_JSON, PRODUCT_PROMOTION_JSON);
            objectData.set(TPMActivityFields.RECHARGE_PROMOTION_JSON, RECHARGE_PROMOTION_JSON);
            orderGoodsPromotionPolicyService.updateOrderGoodsSFAData(actionContext.getTenantId(),
                    actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                    objectData,
                    PRODUCT_PROMOTION_JSON,
                    RECHARGE_PROMOTION_JSON);
        }
        orderGoodsPromotionPolicyService.enableOrderGoodsSFAData(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);
    }

    private String calculateActivityStatusByCloseData(String status) {
        long now = System.currentTimeMillis();
        // 亮晶晶 自定义字段，取消活动执行时间
        Long closeDate = (Long) this.afterDoActActivity.get("field_f1sJK__c");
        if (closeDate != null && now > closeDate) {
            status = TPMActivityFields.ACTIVITY_STATUS__CLOSED;
        }
        return status;
    }

    private void updateSFAPromotionPolicy(IObjectData objectData) {
        // 状态为正常。
        if (Objects.nonNull(ACTIVITY_TYPE_TEMPLATE_ID) &&
                !ACTIVITY_TYPE_TEMPLATE_ID.startsWith("promotion") ||
                !Objects.equals(objectData.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
            return;
        }

        promotionPolicyService.enablePromotionPolicy(actionContext.getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId(), objectData);

        // flag: true 包括更新 价格政策明细限量
        promotionPolicyService.updateSFAPromotionPolicy(actionContext.getTenantId(),
                actionContext.getUser().getUpstreamOwnerIdOrUserId(),
                objectData);
    }

    private String calculateActivityStatus(IObjectData activity) {
        Long begin = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long end = activity.get(TPMActivityFields.END_DATE, Long.class);

        String status;
        if (Objects.isNull(begin) && Objects.isNull(end)) {
            status = TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS;
        } else {
            long now = System.currentTimeMillis();
            if (begin > now) {
                status = TPMActivityFields.ACTIVITY_STATUS__SCHEDULE;
            } else if (now <= end) {
                status = TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS;
            } else {
                status = TPMActivityFields.ACTIVITY_STATUS__END;
            }
        }
        return status;
    }

    private void disableProofEnableCacheAsync() {
        if (TPMGrayUtils.skipEnableCheck(actionContext.getTenantId())) {
            return;
        }

        String lifeStatus = this.afterDoActActivity.get(CommonFields.LIFE_STATUS, String.class);
        if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
            return;
        }

        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            enableCacheService.resetCacheByActivity(actionContext.getTenantId(), afterDoActActivity);
        })).run();
    }

    private String getDataFromCallBack(String key) {

        return (String) callbackData.get(key);
    }

    private void editRewardRule() {
        String data = getDataFromCallBack(GlobalConstant.REWARD_UPDATE_DATA_KEY);
        if (!Strings.isNullOrEmpty(data)) {
            try {
                activityRewardRuleService.update(UpdateRewardRule.Arg.builder().tenantId(actionContext.getTenantId()).rewardRule(JSON.parseObject(data, RewardRuleDTO.class)).build());
            } catch (BizException e) {
                throw new ValidateException(e.getMessage());
            }
        }
    }
}
