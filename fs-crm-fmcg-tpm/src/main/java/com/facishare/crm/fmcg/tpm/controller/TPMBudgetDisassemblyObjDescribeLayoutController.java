package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetDisassemblyNewDetailsFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-08-05
 */
@SuppressWarnings("all")
@Slf4j
public class TPMBudgetDisassemblyObjDescribeLayoutController extends AbstractStandardDescribeLayoutController<TPMBudgetDisassemblyObjDescribeLayoutController.Arg> {

    public static final BudgetTypeDAO budgetTypeDAO = SpringUtil.getContext().getBean(BudgetTypeDAO.class);

    public static final Set<String> TIME_DIMENSION_FIELD_API_NAME = Sets.newHashSet();
    public static final Set<String> DIMENSION_FIELD_API_NAME = Sets.newHashSet();

    public static final Set<String> READ_ONLY_DIMENSION_FIELD_API_NAME = Sets.newHashSet();

    static {
        TIME_DIMENSION_FIELD_API_NAME.add("budget_period_year");
        TIME_DIMENSION_FIELD_API_NAME.add("budget_period_quarter");
        TIME_DIMENSION_FIELD_API_NAME.add("budget_period_month");

        DIMENSION_FIELD_API_NAME.add("dealer_id");
        DIMENSION_FIELD_API_NAME.add("budget_subject_id");
        DIMENSION_FIELD_API_NAME.add("product_id");
        DIMENSION_FIELD_API_NAME.add("product_category_id");

        READ_ONLY_DIMENSION_FIELD_API_NAME.add("dealer_id");
        READ_ONLY_DIMENSION_FIELD_API_NAME.add("product_id");
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        if (!Strings.isNullOrEmpty(arg.getTargetBudgetTemplateId())) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_OBJ_DESCRIBE_LAYOUT_CONTROLLER_0));
        }
    }

    @Override
    protected StandardDescribeLayoutController.Result after(Arg arg, StandardDescribeLayoutController.Result result) {
        StandardDescribeLayoutController.Result layoutResult = super.after(arg, result);
        String typeId = arg.getBudgetTypeId();
        String targetNodeId = arg.getTargetBudgetNodeId();
        String sourceNodeId = arg.getSourceBudgetNodeId();

        if (!Strings.isNullOrEmpty(arg.getData_id())) {
            IObjectData data = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getData_id(), ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
            typeId = (String) data.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID);
            sourceNodeId = (String) data.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID);
            targetNodeId = (String) data.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID);
        }

        final String finalTargetNodeId = targetNodeId;
        final String finalParentNodeId = sourceNodeId;

        if (!Strings.isNullOrEmpty(typeId) && !Strings.isNullOrEmpty(finalTargetNodeId) && !Strings.isNullOrEmpty(finalParentNodeId)) {
            BudgetTypePO type = budgetTypeDAO.get(controllerContext.getTenantId(), typeId);

            BudgetTypeNodeEntity sourceNode = type.getNodes().stream().filter(f -> f.getNodeId().equals(finalParentNodeId)).findFirst().orElse(null);
            BudgetTypeNodeEntity targetNode = type.getNodes().stream().filter(f -> f.getNodeId().equals(finalTargetNodeId)).findFirst().orElse(null);

            List<String> dimensions = targetNode.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());

            Set<String> uselessFields = Sets.newHashSet(TPMBudgetDisassemblyNewDetailsFields.BUDGET_TYPE_ID, TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID);
            uselessFields.addAll(TIME_DIMENSION_FIELD_API_NAME.stream().filter(dimension -> !dimension.endsWith(targetNode.getTimeDimension())).collect(Collectors.toSet()));
            uselessFields.addAll(DIMENSION_FIELD_API_NAME.stream().filter(dimension -> !dimensions.contains(dimension)).collect(Collectors.toSet()));

            Set<String> readonlyFields = Sets.newHashSet();
            readonlyFields.add(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS);
            readonlyFields.add(TPMBudgetDisassemblyFields.DISASSEMBLY_FAILED_MESSAGE);
            readonlyFields.add(TPMBudgetDisassemblyFields.DISASSEMBLY_COMPLETED_TIME);
            if (sourceNode.getDepartmentDimensionLevel() == targetNode.getDepartmentDimensionLevel()) {
                readonlyFields.add(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DEPARTMENT);
            }

            log.info("source period : {}, target period : {}.", sourceNode.getTimeDimension(), targetNode.getTimeDimension());

            if (sourceNode.getTimeDimension().equals(targetNode.getTimeDimension())) {
                String periodFieldApiName = "budget_period_" + targetNode.getTimeDimension();
                readonlyFields.add(periodFieldApiName);
            }
            Map<String, Integer> parentDimensionLevelMap = sourceNode.getDimensions().stream().collect(Collectors.toMap(BudgetDimensionEntity::getApiName, BudgetDimensionEntity::getLevel));
            for (BudgetDimensionEntity dimension : targetNode.getDimensions()) {
                if (parentDimensionLevelMap.containsKey(dimension.getApiName()) && dimension.getLevel() == parentDimensionLevelMap.get(dimension.getApiName())) {
                    readonlyFields.add(dimension.getApiName());
                }
            }

            log.info("read only fields : {}", readonlyFields);
            LayoutExt masterLayout = LayoutExt.of(layoutResult.getLayout());
            List<FormComponentExt> formComponents = masterLayout.getFormComponents();
            for (FormComponentExt formComponent : formComponents) {

                for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                    for (IFormField field : fieldSection.getFields()) {
                        if (readonlyFields.contains(field.getFieldName())) {
                            field.setReadOnly(true);
                        }
                    }
                }
            }

            List<DetailObjectListResult> detailObjectList = layoutResult.getDetailObjectList();
            for (DetailObjectListResult detailObjectListResult : detailObjectList) {
                if (Objects.equals(detailObjectListResult.getObjectApiName(), ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ)) {
                    for (RecordTypeLayoutStructure structure : detailObjectListResult.getLayoutList()) {
                        Map detailLayout = structure.getDetail_layout();
                        LayoutExt layoutExt = LayoutExt.of(detailLayout);

                        Optional<FormComponentExt> component = layoutExt.getFormComponent();
                        if (component.isPresent()) {
                            FormComponentExt formComponent = component.get();
                            addReqiredFields(formComponent, targetNode);
                            for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                                fieldSection.setFields(fieldSection.getFields().stream().filter(field -> !uselessFields.contains(field.getFieldName())).collect(Collectors.toList()));
                                for (IFormField field : fieldSection.getFields()) {
                                    if (DIMENSION_FIELD_API_NAME.contains(field.getFieldName())) {
                                        field.setRequired(true);
                                    }
                                    if (TIME_DIMENSION_FIELD_API_NAME.contains(field.getFieldName())) {
                                        field.setRequired(true);
                                    }
                                    if (dimensions.contains(field.getFieldName())) {
                                        field.setRequired(true);
                                    }
                                    if (readonlyFields.contains(field.getFieldName())) {
                                        field.setReadOnly(true);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return layoutResult;
    }

    private void addReqiredFields(FormComponentExt formComponent, BudgetTypeNodeEntity targetNode) {
        List<String> existsFileds = Lists.newArrayList();
        for (IFieldSection fieldSection : formComponent.getFieldSections()) {
            existsFileds.addAll(fieldSection.getFields().stream().map(field -> field.getFieldName()).collect(Collectors.toList()));
        }

        List<IFormField> dimensionFormFields = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(targetNode.getDimensions())) {
            targetNode.getDimensions().forEach(dimension -> {
                if (!existsFileds.contains(dimension.getApiName())) {
                    dimensionFormFields.add(getFormField(dimension.getApiName(), dimension.getType(), false, true));
                }
            });
        }

        if (!CollectionUtils.isEmpty(dimensionFormFields)) {
            for (IFieldSection fieldSection : formComponent.getFieldSections()) {
                if ("base_field_section__c".equals(fieldSection.getName())) {
                    List<IFormField> fields = Lists.newArrayList();
                    fields.addAll(fieldSection.getFields());
                    fields.addAll(dimensionFormFields);
                    fieldSection.setFields(fields);
                }
            }
        }
    }

    private IFormField getFormField(String apiName, String type, Boolean readOnly, Boolean required) {
        IFormField formField = new FormField();
        formField.setFieldName(apiName);
        formField.setRequired(required);
        formField.setReadOnly(readOnly);
        formField.setRenderType(type);
        return formField;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends StandardDescribeLayoutController.Arg {

        @JSONField(name = "budget_type_id")
        @JsonProperty("budget_type_id")
        private String budgetTypeId;

        @JSONField(name = "source_budget_node_id")
        @JsonProperty("source_budget_node_id")
        private String sourceBudgetNodeId;

        @JSONField(name = "target_budget_node_id")
        @JsonProperty("target_budget_node_id")
        private String targetBudgetNodeId;

        @JSONField(name = "target_budget_template_id")
        @JsonProperty("target_budget_template_id")
        private String targetBudgetTemplateId;
    }
}
