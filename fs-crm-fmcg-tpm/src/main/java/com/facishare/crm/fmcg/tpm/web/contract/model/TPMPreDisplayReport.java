package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fmcg.framework.http.contract.checkin.GetCheckinActionInfoV2;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface TPMPreDisplayReport {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "proof_id")
        @JsonProperty(value = "proof_id")
        @SerializedName("proof_id")
        private String proofId;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        // 驼峰格式，前端要求

        private String pageTemplateId;

        private String actionId;

        private String checkinsId;

        private String accountId;

        private String activityId;

        private String proofId;

        private String sourceActionId;


        private List<JumpActionButton> showButton;

        // 0-页面底部 1-每个陈列形式
        private int btnDisplay;


        public static List<TPMPreDisplayReport.JumpActionButton> buildShowButton(List<GetCheckinActionInfoV2.JumpActionButton> showButton) {

            List<TPMPreDisplayReport.JumpActionButton> showButtonList = new ArrayList<>();
            if (showButton != null) {
                for (GetCheckinActionInfoV2.JumpActionButton btn : showButton) {
                    TPMPreDisplayReport.JumpActionButton jumpBtn = new TPMPreDisplayReport.JumpActionButton();
                    jumpBtn.setBtnName(btn.getBtnName());
                    jumpBtn.setBtnType(btn.getBtnType());
                    jumpBtn.setActionId(btn.getActionId());
                    jumpBtn.setFuncApiName(btn.getFuncApiName());
                    jumpBtn.setCustomParams(btn.getCustomParams());
                    jumpBtn.setNeedPopup(btn.isNeedPopup());
                    jumpBtn.setPopupBtnText(btn.getPopupBtnText());
                    jumpBtn.setActionCode(btn.getActionCode());
                    showButtonList.add(jumpBtn);
                }
            }
            return showButtonList;
        }
    }

    @Data
    @ToString
    class JumpActionButton implements Serializable {
        private String btnName;//按钮名称
        private int btnType;
        private String actionId;//跳转动作id
        private String funcApiName;

        private Map<String,Object> customParams;
        private boolean needPopup;
        private String popupBtnText;
        private String actionCode;
    }

}
