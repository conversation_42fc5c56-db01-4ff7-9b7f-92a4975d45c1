package com.facishare.crm.fmcg.tpm.web.utils;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.PreActivityType;
import com.facishare.paas.I18N;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2021/11/24 17:06
 */
@UtilityClass
@Slf4j
public class TPMI18Utils {

    public static final String TERMINAL_KEY = "fs.fmcg.tpm";
    public static final String TERMINAL_KEY_BUDGET = "fs.fmcg.budget";

    public static String getPlatActivityI18nKey(String tenantId, String id) {
        return getKey(TERMINAL_KEY, tenantId, id);
    }

    public static String getPlatBudgetI18nKey(String tenantId, String id) {
        return getKey(TERMINAL_KEY_BUDGET, tenantId, id);
    }


    public static String getActivitySystemNodeNameI18nKey(String uniqueId) {
        return getKey("fmcg.activity.node.system", null, uniqueId);
    }

    public static String getActivitySystemTypeNameI18nKey(String uniqueId) {
        return getKey("fmcg.activity.type.system", null, uniqueId);
    }

    public static String getKey(String terminalKey, String tenantId, String uniqueId) {
        if (Strings.isNullOrEmpty(tenantId)) {
            return String.format("%s.%s", terminalKey, uniqueId);
        }
        return String.format("%s.%s.%s", terminalKey, tenantId, uniqueId);
    }

    public static String getActivitySystemNodeText(String tenantId, String nodeId, String objectApiName, String name, String type) {
        if (StringUtils.isNotBlank(objectApiName) && objectApiName.contains(ApiNames.SALES_ORDER_OBJ)) {
            return I18N.text(TPMI18Utils.getActivitySystemNodeNameI18nKey(objectApiName));
        }
        NodeType nodeType = NodeType.of(type);
        if (Objects.nonNull(nodeType)) {
            String defaultName = nodeType.defaultName();
            if (StringUtils.isNotBlank(defaultName) && defaultName.equals(name)) {
                String text = getPlatActivityI18Text(tenantId, nodeId);
                if (StringUtils.isNotBlank(text)) {
                    return text;
                }
                return I18N.text(TPMI18Utils.getActivitySystemNodeNameI18nKey(objectApiName));
            }
        }

        return getPlatActivityI18Text(tenantId, nodeId);
    }

    public static String getActivitySystemTypeText(String tenantId, String typeId, String apiMame, String name) {
        PreActivityType of = PreActivityType.of(apiMame);
        if (Objects.nonNull(of)) {
            String defaultName = of.defaultName();
            if (StringUtils.isNotBlank(defaultName) && defaultName.equals(name)) {
                //翻译工作台翻译
                String text = getPlatActivityI18Text(tenantId, typeId);
                if (StringUtils.isNotBlank(text)) {
                    return text;
                }
                //预置词条翻译
                return I18N.text(TPMI18Utils.getActivitySystemTypeNameI18nKey(apiMame));
            }
        }
        return getPlatActivityI18Text(tenantId, typeId);
    }

    private static String getPlatActivityI18Text(String tenantId, String typeId) {
        String i18nKey = TPMI18Utils.getPlatActivityI18nKey(tenantId, typeId);
        String text = I18N.text(i18nKey);
        log.info("getPlatActivityI18Text i18nKey:{} text:{}", i18nKey, text);
        return text;
    }

    public static String getBudgeText(String tenantId, String id) {
        if (!Strings.isNullOrEmpty(id)) {
            String i18nKey = TPMI18Utils.getPlatBudgetI18nKey(tenantId, id);
            String text = I18N.text(i18nKey);
            if (!Strings.isNullOrEmpty(text)) {
                return text;
            }
        }
        return null;
    }

    public static void editPlatActivityI18Text(String tenantId, String id, String name, String lang) {
        if (Strings.isNullOrEmpty(id) || Strings.isNullOrEmpty(name)) {
            return;
        }
        String i18nKey = TPMI18Utils.getPlatActivityI18nKey(tenantId, id);
        editPlateI8Text(tenantId, name, lang, i18nKey);
    }

    public static void editPlatBudgetI18Text(String tenantId, String id, String name, String lang) {
        if (Strings.isNullOrEmpty(id) || Strings.isNullOrEmpty(name)) {
            return;
        }
        String i18nKey = TPMI18Utils.getPlatBudgetI18nKey(tenantId, id);
        editPlateI8Text(tenantId, name, lang, i18nKey);
    }

    private static void editPlateI8Text(String tenantId, String name, String lang, String i18nKey) {
        Localization localization = I18nClient.getInstance().get(i18nKey, Integer.parseInt(tenantId));
        if (Objects.nonNull(localization)) {
            localization.set(lang, name);
            I18nClient.getInstance().save(Long.parseLong(tenantId), Lists.newArrayList(localization), true);
        }
    }

}
