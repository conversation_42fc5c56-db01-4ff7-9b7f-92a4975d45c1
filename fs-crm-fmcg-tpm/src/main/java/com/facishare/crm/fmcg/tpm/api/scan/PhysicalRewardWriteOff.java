package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public interface PhysicalRewardWriteOff {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "bottle_scan")
        @JsonProperty(value = "bottle_scan")
        @SerializedName("bottle_scan")
        private String bottleScan;

    }


    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class Result implements Serializable {

        @JSONField(name = "rebate_name")
        @JsonProperty(value = "rebate_name")
        @SerializedName("rebate_name")
        private String rebateName;

        @JSONField(name = "product_name")
        @JsonProperty(value = "product_name")
        @SerializedName("product_name")
        private String productName;

        @JSONField(name = "product_img_path")
        @JsonProperty(value = "product_img_path")
        @SerializedName("product_img_path")
        private String productImgPath;

        @JSONField(name = "red_package_amount")
        @JsonProperty(value = "red_package_amount")
        @SerializedName("red_package_amount")
        private BigDecimal redPackageAmount;
    }
}
