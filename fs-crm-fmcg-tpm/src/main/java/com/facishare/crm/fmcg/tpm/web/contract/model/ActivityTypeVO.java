package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeDraftBoxPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.StatusType;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 16:24
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
public class ActivityTypeVO extends CommonVO implements Serializable, IActivityType {

    private String name;

    @JSONField(name = "template_id")
    @JsonProperty(value = "template_id")
    @SerializedName("template_id")
    private String templateId;

    @JSONField(name = "api_name")
    @JsonProperty(value = "api_name")
    @SerializedName("api_name")
    private String apiName;

    @JSONField(name = "employee_ids")
    @JsonProperty(value = "employee_ids")
    @SerializedName("employee_ids")
    private List<Integer> employeeIds;

    @JSONField(name = "department_ids")
    @JsonProperty(value = "department_ids")
    @SerializedName("department_ids")
    private List<Integer> departmentIds;

    @JSONField(name = "role_ids")
    @JsonProperty(value = "role_ids")
    @SerializedName("role_ids")
    private List<String> roleIds;

    @JSONField(name = "all_employee_ids")
    @JsonProperty(value = "all_employee_ids")
    @SerializedName("all_employee_ids")
    private List<Integer> allEmployeeIds;

    @JSONField(name = "scope_description")
    @JsonProperty(value = "scope_description")
    @SerializedName("scope_description")
    private String scopeDescription = "--";

    private String description;

    private String status;

    @JSONField(name = "exception_status")
    @JsonProperty(value = "exception_status")
    @SerializedName("exception_status")
    private String exceptionStatus;

    private long version;

    @JSONField(name = "package")
    @JsonProperty(value = "package")
    @SerializedName("package")
    private String packageType;

    @JSONField(name = "activity_node_list")
    @JsonProperty(value = "activity_node_list")
    @SerializedName("activity_node_list")
    private List<ActivityNodeVO> activityNodeList = Lists.newArrayList();

    @JSONField(name = "forbid_relate_customer")
    @JsonProperty(value = "forbid_relate_customer")
    @SerializedName("forbid_relate_customer")
    private Boolean forbidRelateCustomer;

    @JSONField(name = "template_name")
    @JsonProperty(value = "template_name")
    @SerializedName("template_name")
    private String templateName;

    public static ActivityTypeVO fromPOToListDatumVO(ActivityTypePO po) {
        if (po == null) {
            return null;
        }
        ActivityTypeVO vo = new ActivityTypeVO();
        vo.setId(po.getId().toString());
        vo.setTemplateId(Strings.isNullOrEmpty(po.getTemplateId()) ? "custom" : po.getTemplateId());
        vo.setName(po.getName());
        vo.setApiName(po.getApiName());
        vo.setPackageType(po.getPackageType());
        vo.setDescription(po.getDescription());
        vo.setScopeDescription(po.getScopeDescription());
        vo.setVersion(po.getVersion());
        vo.setTenantId(po.getTenantId());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(po.getCreateTime());
        vo.setLastUpdater(po.getLastUpdater());
        vo.setLastUpdateTime(po.getLastUpdateTime());
        vo.setDeleted(po.isDeleted());
        vo.setDeleteBy(po.getDeleteBy());
        vo.setDeleteTime(po.getDeleteTime());
        vo.setStatus(po.getStatus());
        vo.setForbidRelateCustomer(po.getForbidRelateCustomer());
        vo.setTemplateName(po.getTemplateName());
        vo.setExceptionStatus(po.getExceptionStatus() == null ? StatusType.NORMAL.value() : po.getExceptionStatus());
        vo.setActivityNodeList(po.getActivityNodes().stream().map(ActivityNodeVO::fromPO).collect(Collectors.toList()));
        return vo;
    }

    public static ActivityTypeVO fromPO(ActivityTypePO po) {
        if (po == null) {
            return null;
        }
        ActivityTypeVO vo = new ActivityTypeVO();
        vo.setId(po.getId().toString());
        vo.setName(po.getName());
        vo.setTemplateId(Strings.isNullOrEmpty(po.getTemplateId()) ? "custom" : po.getTemplateId());
        vo.setApiName(po.getApiName());
        vo.setDescription(po.getDescription());
        vo.setVersion(po.getVersion());
        vo.setPackageType(po.getPackageType());
        vo.setTenantId(po.getTenantId());
        vo.setEmployeeIds(po.getEmployeeIds() == null ? Lists.newArrayList() : po.getEmployeeIds());
        vo.setDepartmentIds(po.getDepartmentIds() == null ? Lists.newArrayList() : po.getDepartmentIds());
        vo.setRoleIds(po.getRoleIds() == null ? Lists.newArrayList() : po.getRoleIds());
        vo.setScopeDescription(po.getScopeDescription());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(po.getCreateTime());
        vo.setLastUpdater(po.getLastUpdater());
        vo.setLastUpdateTime(po.getLastUpdateTime());
        vo.setDeleted(po.isDeleted());
        vo.setDeleteBy(po.getDeleteBy());
        vo.setDeleteTime(po.getDeleteTime());
        vo.setStatus(po.getStatus());
        vo.setForbidRelateCustomer(po.getForbidRelateCustomer());
        vo.setTemplateName(po.getTemplateName());
        vo.setExceptionStatus(po.getExceptionStatus() == null ? StatusType.NORMAL.value() : po.getExceptionStatus());
        vo.setActivityNodeList(po.getActivityNodes().stream().map(ActivityNodeVO::fromPO).collect(Collectors.toList()));
        return vo;
    }

    public static ActivityTypeVO fromPO(ActivityTypePO po,
                                        Map<String, String> objectNameMap,
                                        Map<String, Map<String, String>> recordTypeNameMap) {
        if (po == null) {
            return null;
        }
        ActivityTypeVO vo = new ActivityTypeVO();
        vo.setId(po.getId().toString());
        vo.setName(po.getName());
        vo.setTemplateId(Strings.isNullOrEmpty(po.getTemplateId()) ? "custom" : po.getTemplateId());
        vo.setApiName(po.getApiName());
        vo.setDescription(po.getDescription());
        vo.setVersion(po.getVersion());
        vo.setPackageType(po.getPackageType());
        vo.setTenantId(po.getTenantId());
        vo.setEmployeeIds(po.getEmployeeIds() == null ? Lists.newArrayList() : po.getEmployeeIds());
        vo.setDepartmentIds(po.getDepartmentIds() == null ? Lists.newArrayList() : po.getDepartmentIds());
        vo.setRoleIds(po.getRoleIds() == null ? Lists.newArrayList() : po.getRoleIds());
        vo.setScopeDescription(po.getScopeDescription());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(po.getCreateTime());
        vo.setLastUpdater(po.getLastUpdater());
        vo.setLastUpdateTime(po.getLastUpdateTime());
        vo.setDeleted(po.isDeleted());
        vo.setDeleteBy(po.getDeleteBy());
        vo.setDeleteTime(po.getDeleteTime());
        vo.setStatus(po.getStatus());
        vo.setTemplateName(po.getTemplateName());
        vo.setActivityNodeList(po.getActivityNodes().stream().map(node -> ActivityNodeVO.fromPO(node, objectNameMap, recordTypeNameMap)).collect(Collectors.toList()));
        return vo;
    }

    public static ActivityTypeVO fromDraftBox(ActivityTypeDraftBoxPO po) {
        if (po == null) {
            return null;
        }
        ActivityTypeVO vo = new ActivityTypeVO();
        vo.setId(po.getId().toString());
        vo.setName(po.getName());
        vo.setTemplateId(Strings.isNullOrEmpty(po.getTemplateId()) ? "custom" : po.getTemplateId());
        vo.setApiName(po.getApiName());
        vo.setDescription(po.getDescription());
        vo.setVersion(po.getVersion());
        vo.setPackageType(po.getPackageType());
        vo.setTenantId(po.getTenantId());
        vo.setEmployeeIds(po.getEmployeeIds() == null ? Lists.newArrayList() : po.getEmployeeIds());
        vo.setDepartmentIds(po.getDepartmentIds() == null ? Lists.newArrayList() : po.getDepartmentIds());
        vo.setRoleIds(po.getRoleIds() == null ? Lists.newArrayList() : po.getRoleIds());
        vo.setScopeDescription(po.getScopeDescription());
        vo.setCreator(po.getCreator());
        vo.setCreateTime(po.getCreateTime());
        vo.setLastUpdater(po.getLastUpdater());
        vo.setLastUpdateTime(po.getLastUpdateTime());
        vo.setDeleted(po.isDeleted());
        vo.setDeleteBy(po.getDeleteBy());
        vo.setDeleteTime(po.getDeleteTime());
        vo.setStatus(po.getStatus());
        vo.setTemplateName(po.getTemplateName());
        vo.setExceptionStatus(po.getExceptionStatus() == null ? StatusType.NORMAL.value() : po.getExceptionStatus());
        vo.setActivityNodeList(po.getActivityNodes().stream().map(ActivityNodeVO::fromPO).collect(Collectors.toList()));
        return vo;
    }

    @Override
    public String getDefaultName() {
        return name;
    }
}
