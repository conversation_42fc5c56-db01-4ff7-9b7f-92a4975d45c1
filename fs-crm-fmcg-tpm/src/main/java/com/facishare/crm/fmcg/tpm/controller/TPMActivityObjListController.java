package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.OrderGoodsPromotionPolicyService;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.common.constant.OrderGoodsContents;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 4:39 PM
 */
public class TPMActivityObjListController extends StandardListController {

    protected static final ButtonDocument CLOSE_ACTIVITY_BUTTON = new ButtonDocument();
    private TPM2Service tpm2Service = SpringUtil.getContext().getBean(TPM2Service.class);
    private static final OrderGoodsPromotionPolicyService orderGoodsPromotionPolicyService = SpringUtil.getContext().getBean(OrderGoodsPromotionPolicyService.class);


    private static final List<String> ALLOW_CLOSE_LIST = Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, TPMActivityFields.ACTIVITY_STATUS__SCHEDULE, TPMActivityFields.ACTIVITY_STATUS__END);

    private boolean isWeb = false;

    static {
        CLOSE_ACTIVITY_BUTTON.put("_id", "CloseActivity_button_default_60501de809e1dc00017c2024");
        CLOSE_ACTIVITY_BUTTON.put("api_name", "CloseActivity");
        CLOSE_ACTIVITY_BUTTON.put("action", "CloseActivity");
        CLOSE_ACTIVITY_BUTTON.put("label", "结案");//ignorei18n
        CLOSE_ACTIVITY_BUTTON.put("action_type", "system");
        CLOSE_ACTIVITY_BUTTON.put("actions", Lists.newArrayList());
        CLOSE_ACTIVITY_BUTTON.put("button_type", "common");
        CLOSE_ACTIVITY_BUTTON.put("describe_api_name", "TPMActivityObj");
        CLOSE_ACTIVITY_BUTTON.put("is_active", true);
        CLOSE_ACTIVITY_BUTTON.put("is_deleted", false);
        CLOSE_ACTIVITY_BUTTON.put("use_pages", Lists.newArrayList("list", "detail"));
        CLOSE_ACTIVITY_BUTTON.put("where", Lists.newArrayList());
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        String clientInfo = controllerContext.getClientInfo();
        if (Objects.nonNull(clientInfo) && clientInfo.startsWith("WEB")) {
            isWeb = true;
        }
        super.beforeQueryData(query);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (tpm2Service.isTPM2Tenant(Integer.valueOf(controllerContext.getTenantId()))) {
            realButton(arg, result);
            fillActivityDate(result);
        } else {
            fakeButton(arg, result);
        }
        return super.after(arg, result);
    }

    private void fillActivityDate(Result result) {
        for (ObjectDataDocument obj : result.getDataList()) {

            Long beginDate = (Long) obj.get(TPMActivityFields.BEGIN_DATE);
            Long endDate = (Long) obj.get(TPMActivityFields.END_DATE);

            if (Objects.nonNull(beginDate) && beginDate <= TimeUtils.MIN_DATE) {
                obj.put(TPMActivityFields.BEGIN_DATE, null);
            }
            if (Objects.nonNull(endDate) && endDate >= TimeUtils.MAX_DATE) {
                obj.put(TPMActivityFields.END_DATE, null);
            }
        }
    }

    private void fakeButton(Arg arg, Result result) {
        String functionCode = TPMGrayUtils.isSupportCloseActivityPrivileges(controllerContext.getTenantId()) ? ObjectAction.CLOSE_TPM_ACTIVITY.getActionCode() : "Edit";
        if (arg.isIncludeButtonInfo() && serviceFacade.funPrivilegeCheck(controllerContext.getUser(), ApiNames.TPM_ACTIVITY_OBJ, functionCode)) {
            if (result.getButtonInfo().getButtons() == null) {
                result.getButtonInfo().setButtons(Lists.newArrayList(CLOSE_ACTIVITY_BUTTON));
            } else {
                result.getButtonInfo().getButtons().add(CLOSE_ACTIVITY_BUTTON);
            }
            List<String> buttonArr = Lists.newArrayList("CloseActivity");
            if (result.getButtonInfo().getButtonMap() == null) {
                result.getButtonInfo().setButtonMap(new HashMap<>());
                for (ObjectDataDocument obj : result.getDataList()) {
                    String activityStatus = (String) obj.get(TPMActivityFields.ACTIVITY_STATUS);
                    String closedStatus = (String) obj.get(TPMActivityFields.CLOSED_STATUS);
                    String lifeStatus = (String) obj.get(CommonFields.LIFE_STATUS);
                    boolean statusJudge = TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) ? "normal".equals(lifeStatus) : TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus);
                    if (statusJudge && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
                        result.getButtonInfo().getButtonMap().put(obj.getId(), buttonArr);
                    }
                }
            } else {
                for (ObjectDataDocument obj : result.getDataList()) {
                    String activityStatus = (String) obj.get(TPMActivityFields.ACTIVITY_STATUS);
                    String closedStatus = (String) obj.get(TPMActivityFields.CLOSED_STATUS);
                    String lifeStatus = (String) obj.get(CommonFields.LIFE_STATUS);
                    boolean statusJudge = TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) ? "normal".equals(lifeStatus) : TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus);
                    if (statusJudge && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
                        if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                            result.getButtonInfo().getButtonMap().get(obj.getId()).add("CloseActivity");
                        } else {
                            result.getButtonInfo().getButtonMap().put(obj.getId(), buttonArr);
                        }
                    }

                    if (orderGoodsPromotionPolicyService.judgedIsOrderGoodsPromotionData(obj.toObjectData()) && isWeb) {
                        result.getButtonInfo().getButtonMap().get(obj.getId()).remove(OrderGoodsContents.BUTTON_PLACE_ORDER__C);
                        result.getButtonInfo().getButtonMap().get(obj.getId()).remove(OrderGoodsContents.BUTTON_GO_TO_RECHARGE__C);
                    }
                }
            }
        }
    }

    private void realButton(Arg arg, Result result) {
        if (arg.isIncludeButtonInfo()) {

            if (result.getButtonInfo().getButtonMap() != null) {
                for (ObjectDataDocument obj : result.getDataList()) {
                    String activityStatus = (String) obj.get(TPMActivityFields.ACTIVITY_STATUS);
                    String closedStatus = (String) obj.get(TPMActivityFields.CLOSED_STATUS);
                    if (TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) && !ALLOW_CLOSE_LIST.contains(activityStatus) ||
                            !TPMGrayUtils.isAllowProcessActivityClose(controllerContext.getTenantId()) && !TPMActivityFields.ACTIVITY_STATUS__END.equals(activityStatus) || TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
                        if (result.getButtonInfo().getButtonMap().containsKey(obj.getId())) {
                            result.getButtonInfo().getButtonMap().get(obj.getId()).remove(ObjectAction.CLOSE_TPM_ACTIVITY.getButtonApiName());
                        }
                    }
                    if (orderGoodsPromotionPolicyService.judgedIsOrderGoodsPromotionData(obj.toObjectData()) && isWeb) {
                        result.getButtonInfo().getButtonMap().get(obj.getId()).remove(OrderGoodsContents.BUTTON_PLACE_ORDER__C);
                        result.getButtonInfo().getButtonMap().get(obj.getId()).remove(OrderGoodsContents.BUTTON_GO_TO_RECHARGE__C);
                    }
                }
            }
        }
    }
}
