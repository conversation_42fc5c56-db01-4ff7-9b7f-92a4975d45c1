package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import groovy.lang.Tuple2;

import java.util.List;
import java.util.Set;

public interface IBudgetDisassemblyService {

    Tuple2<String, Long> getPeriodApiNameAndPeriod(String tenantId, String timeDimension, IObjectData masterData);

    IObjectData buildDataForCreateBudgetAccount(String tenantId, IObjectData newDetail, IObjectData masterData, BudgetTypeNodeEntity targetNode);

    void validateMasterData(String tenantId, IObjectData masterData);

    IObjectData findData(String tenantId, String id, String apiName);
    IObjectData findDataWithFields(String tenantId,List<String> fields, String id, String apiName);

    void correctNewDetailsValue(List<ObjectDataDocument> newDetailsDocuments, BudgetTypeNodeEntity targetNode);

    void correctExsitsDetailsValue(ObjectDataDocument existDetail, BudgetTypeNodeEntity targetNode, IObjectData targetAccount);

    void existsDetailDuplicateValidate(List<IObjectData> existsDetails);

    void newDetailDuplicateValidateV2(String tenantId, List<IObjectData> newDetails,BudgetTypeNodeEntity targetNode);


    boolean isOnlyEditCustomFields(Set<String> updateFieldsSet);

    IBudgetOperator initSourceAccountOperator(User user, String dataId);

    void initFailedRetryButton(String tenantId);


}
