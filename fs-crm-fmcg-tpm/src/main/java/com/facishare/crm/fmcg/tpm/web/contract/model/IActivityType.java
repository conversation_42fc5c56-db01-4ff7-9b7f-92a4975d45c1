package com.facishare.crm.fmcg.tpm.web.contract.model;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/30 15:48
 */
public interface IActivityType extends Serializable {
    String getName();

    void setName(String name);

    String getApiName();

    void setApiName(String apiName);

    List<Integer> getEmployeeIds();

    void setEmployeeIds(List<Integer> employeeIds);

    List<Integer> getDepartmentIds();

    void setDepartmentIds(List<Integer> departmentIds);

    List<String> getRoleIds();

    void setRoleIds(List<String> roleIds);

    String getScopeDescription();

    void setScopeDescription(String scopeDescription);

    String getDescription();

    void setDescription(String description);

    String getStatus();

    void setStatus(String status);

    long getVersion();

    void setVersion(long version);

    Boolean getForbidRelateCustomer();

    void setForbidRelateCustomer(Boolean forbidRelateCustomer);

    String getDefaultName();

}
