package com.facishare.crm.fmcg.tpm.web.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.DisassemblyActionCode;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetAccrualRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.abstraction.ITransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.service.abstraction.TPMEnterpriseService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.StoreWriteButtonInit;
import com.facishare.crm.fmcg.tpm.web.contract.UpdateActivityType;
import com.facishare.crm.fmcg.tpm.web.contract.UpdateRedPacketExpire;
import com.facishare.crm.fmcg.tpm.web.contract.kk.ActivityImport;
import com.facishare.crm.fmcg.tpm.web.designer.ActivityCostAssignNodeHandler;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IModuleInitializationService;
import com.facishare.organization.api.model.employee.arg.GetEmployeesDtoByNameArg;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.checker.CheckerResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.warehouse.api.model.arg.CreateAFileFromNFile;
import com.facishare.warehouse.api.service.FileUnityService;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.PaasLayoutProxy;
import com.fmcg.framework.http.RecordTypeProxy;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeCreate;
import com.fmcg.framework.http.contract.paas.describe.UpdateDescribeExtra;
import com.fmcg.framework.http.contract.paas.layout.PaasAssignRecord;
import com.fmcg.framework.http.contract.paas.layout.PaasCreateLayout;
import com.fmcg.framework.http.contract.recordtype.RecordType;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.ApiException;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/6/5 21:38
 */
//IgnoreI18nFile
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class ModuleInitializationService implements IModuleInitializationService {

    public static final String STORE_CONFIRM_BUTTON_API_NAME = "AgreementStoreConfirm_button_default";
    public static final String DETAIL_LAYOUT_TYPE = "detail";
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private SpecialTableMapper specialTableMapper;
    @Resource
    private ActivityCostAssignNodeHandler activityCostAssignNodeHandler;
    @Resource
    private IObjectDescribeService objectDescribeService;
    @Resource
    private BudgetTypeDAO budgetTypeDAO;
    @Resource
    private ITransactionProxy transactionProxy;
    @Resource
    private IBudgetDisassemblyService budgetDisassemblyService;
    @Resource
    private IBudgetAccountService budgetAccountService;
    @Resource
    private IBudgetCalculateService budgetCalculateService;
    @Resource
    private IBudgetAccountDetailService budgetAccountDetailService;
    @Resource
    private IBudgetStatisticTableService budgetStatisticTableService;
    @Resource
    private FileUnityService fileUnityService;
    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;
    @Resource
    private EmployeeProviderService employeeProviderService;
    @Resource
    private TPMEnterpriseService tpmEnterpriseService;
    @Resource
    private BudgetAccrualRuleDAO budgetAccrualRuleDAO;
    @Resource
    private ActivityTypeManager activityTypeManager;
    @Resource
    private IAsyncBudgetDisassemblyService asyncBudgetDisassemblyService;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;
    @Resource
    private PaasLayoutProxy paasLayoutProxy;
    @Resource
    private RecordTypeProxy recordTypeProxy;
    @Resource
    private RecordTypeLogicServiceImpl recordTypeLogicService;


    private static final List<String> ALL_ROLE_CODES = com.beust.jcommander.internal.Lists.newArrayList("personnelrole", "00000000000000000000000000000006", "00000000000000000000000000000019", "00000000000000000000000000000009",
            "00000000000000000000000000000026", "00000000000000000000000000000015", "00000000000000000000000000000004", "00000000000000000000000000000016", "00000000000000000000000000000005",
            "00000000000000000000000000000017", "00000000000000000000000000000029", "00000000000000000000000000000018", "00000000000000000000000000000024", "00000000000000000000000000000002",
            "00000000000000000000000000000014", "00000000000000000000000000000003", "00000000000000000000000000000030", "00000000000000000000000000000031", "00000000000000000000000000000020",
            "00000000000000000000000000000032", "00000000000000000000000000000010", "goalManagerRole", "budgetFinance", "inspector", "marketingActivitiesManager", "ad_account_marketing_role",
            "casesManager", "EnterprisePayAccountManager", "qywx_account_marketing_role", "00000000000000000000000000000033", "pmmAppAdminRole", "fmcgPMMPromotionalRole", "consultAdminManager",
            "sfaVisitUserLimitRole");

    @Override
    public void addActivityTypeFields(String tenantId) {
        addFields(tenantId, ApiNames.TPM_ACTIVITY_PROOF_OBJ, "activity_type");
        addFields(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "activity_type");
        addFields(tenantId, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, "activity_type");
        addFields(tenantId, ApiNames.TPM_DEALER_ACTIVITY_COST, "activity_type");
    }

    /**
     * init store confirm module for 810
     *
     * @param tenantId tenant id
     */
    @Override
    public void initAgreementStoreConfirmModule(String tenantId) {
        addFields(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.SIGNING_MODE, TPMActivityAgreementFields.STORE_CONFIRM_STATUS);
        initStoreConfirmButton(tenantId);
    }

    @Override
    public void disableAgreementStoreConfirmModule(String tenantId) {
        User superUser = User.systemUser(tenantId);

        deleteFields(superUser, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.SIGNING_MODE, TPMActivityAgreementFields.STORE_CONFIRM_STATUS);

        try {
            String sql = "delete from mt_udef_button where tenant_id = '%s' and api_name ='AgreementStoreConfirm_button_default' and describe_api_name = 'TPMActivityAgreementObj'";
            specialTableMapper.setTenantId(tenantId).deleteBySql(String.format(sql, tenantId));
        } catch (Exception ex) {
            log.error("delete 'AgreementStoreConfirm_button_default' button cause exception : ", ex);
        }
    }

    @Override
    public void upgradeFor810(String tenantId) {
        IObjectDescribe unitDescribe = null;
        try {
            unitDescribe = serviceFacade.findObject(tenantId, "UnitInfoObj");
        } catch (Exception ex) {
            log.info("find UnitInfoObj describe cause error : ", ex);
        }
        if (Objects.isNull(unitDescribe)) {
            addFields(tenantId, ApiNames.TPM_ACTIVITY_ITEM_OBJ, TPMActivityItemFields.STANDARD_DISPLAY_IMAGES);
            addFields(
                    tenantId,
                    ApiNames.TPM_ACTIVITY_DETAIL_OBJ,
                    TPMActivityDetailFields.PAYMENT_MODE,
                    TPMActivityDetailFields.PAYMENT_PRODUCT,
                    TPMActivityDetailFields.PAYMENT_PRODUCT_AMOUNT,
                    TPMActivityDetailFields.STANDARD_DISPLAY_IMAGES
            );
            addFields(
                    tenantId,
                    ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ,
                    TPMActivityAgreementDetailFields.PAYMENT_MODE,
                    TPMActivityAgreementDetailFields.PAYMENT_PRODUCT,
                    TPMActivityAgreementDetailFields.PAYMENT_PRODUCT_AMOUNT,
                    TPMActivityAgreementDetailFields.STANDARD_DISPLAY_IMAGES
            );
        } else {
            addFields(tenantId, ApiNames.TPM_ACTIVITY_ITEM_OBJ, TPMActivityItemFields.STANDARD_DISPLAY_IMAGES);
            addFields(
                    tenantId,
                    ApiNames.TPM_ACTIVITY_DETAIL_OBJ,
                    TPMActivityDetailFields.PAYMENT_MODE,
                    TPMActivityDetailFields.PAYMENT_PRODUCT,
                    TPMActivityDetailFields.PAYMENT_PRODUCT_AMOUNT,
                    TPMActivityDetailFields.PAYMENT_PRODUCT_UNIT,
                    TPMActivityDetailFields.STANDARD_DISPLAY_IMAGES
            );
            addFields(
                    tenantId,
                    ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ,
                    TPMActivityAgreementDetailFields.PAYMENT_MODE,
                    TPMActivityAgreementDetailFields.PAYMENT_PRODUCT,
                    TPMActivityAgreementDetailFields.PAYMENT_PRODUCT_AMOUNT,
                    TPMActivityAgreementDetailFields.PAYMENT_PRODUCT_UNIT,
                    TPMActivityAgreementDetailFields.STANDARD_DISPLAY_IMAGES
            );
        }
    }

    @Override
    public void rollbackFor810(String tenantId) {
        User superUser = User.systemUser(tenantId);

        deleteFields(
                superUser,
                ApiNames.TPM_ACTIVITY_DETAIL_OBJ,
                TPMActivityDetailFields.PAYMENT_MODE,
                TPMActivityDetailFields.PAYMENT_PRODUCT,
                TPMActivityDetailFields.PAYMENT_PRODUCT_AMOUNT,
                TPMActivityDetailFields.PAYMENT_PRODUCT_UNIT,
                TPMActivityDetailFields.STANDARD_DISPLAY_IMAGES
        );
    }

    @Override
    public void disableSalesOrderConfirmModule(String tenantId) {
        User superUser = User.systemUser(tenantId);

        deleteFields(
                superUser,
                ApiNames.SALES_ORDER_OBJ,
                SalesOrderObjFields.ACTIVITY_ID,
                SalesOrderObjFields.ACTIVITY_AGREEMENT_ID,
                SalesOrderObjFields.DEALER_ACTIVITY_COST_ID,
                SalesOrderObjFields.COST_SIGN_STATUS,
                SalesOrderObjFields.ACTIVITY_CLOSED_STATUS,
                SalesOrderObjFields.STORE_WRITE_OFF_ID
        );

        try {
            String sql = "delete from mt_udef_button where tenant_id = '%s' and api_name ='SalesOrderCostAssign_button_default' and describe_api_name = 'SalesOrderObj'";
            specialTableMapper.setTenantId(tenantId).deleteBySql(String.format(sql, tenantId));
        } catch (Exception ex) {
            log.error("delete 'SalesOrderCostAssign_button_default' button cause exception : ", ex);
        }
    }

    @Override
    public void initSalesOrderSalesModeFields(String tenantId) {
        addFields(tenantId, ApiNames.SALES_ORDER_OBJ, SalesOrderObjFields.SALES_MODE);
    }

    @Override
    public void initSalesOrderProductSalesTypeFields(String tenantId) {
        addFields(tenantId, ApiNames.SALES_ORDER_PRODUCT_OBJ, SalesOrderProductFields.SALES_TYPE, SalesOrderProductFields.BILLING_LABEL);
    }

    @Override
    public void initSalesOrderTPMFields(String tenantId) {
        activityCostAssignNodeHandler.initSalesOrderTPMFields(tenantId, "activity_id", "activity_agreement_id", "dealer_activity_cost_id", "store_write_off_id", "activity_closed_status");
    }

    @Override
    public void disableSalesOrderTPM810Fields(String tenantId) {
        log.info("start disableSalesOrderTPM810Fields tenant_id={}", tenantId);
        User superUser = User.systemUser(tenantId);

        deleteFields(
                superUser,
                ApiNames.SALES_ORDER_OBJ,
                SalesOrderObjFields.ACTIVITY_ID,
                SalesOrderObjFields.ACTIVITY_AGREEMENT_ID,
                SalesOrderObjFields.DEALER_ACTIVITY_COST_ID,
                SalesOrderObjFields.COST_SIGN_STATUS
        );

        try {
            String sql = "delete from mt_udef_button where tenant_id = '%s' and api_name ='SalesOrderCostAssign_button_default' and describe_api_name = 'SalesOrderObj'";
            specialTableMapper.setTenantId(tenantId).deleteBySql(String.format(sql, tenantId));
        } catch (Exception ex) {
            log.error("delete 'SalesOrderCostAssign_button_default' button cause exception : ", ex);
        }

        log.info("end disableSalesOrderTPM810Fields tenant_id={}", tenantId);
    }

    @Override
    public void initSalesOrderTPM815Fields(String tenantId) {
        activityCostAssignNodeHandler.initSalesOrderTPMFields(tenantId, "store_write_off_id", "activity_closed_status");
    }

    @Override
    public void disableBudgetObjectDescribe(String tenantId) {
        deleteDescribes(tenantId, ApiNames.TPM_BUDGET_TRANSFER_DETAIL);
    }

    @Override
    public void initAllTenantSalesOrderTPMFields() {
        log.info("init initAllTenantSalesOrderTPMFields");

        String TPM2TenantId = ConfigFactory.getConfig("variables_fmcg_gray").get("ei_list_fmcg_tpm_gray");
        log.info("TPM2TenantId = {} ", TPM2TenantId);
        String[] tenantIds = TPM2TenantId.split(",");
        for (int i = 0; i < tenantIds.length; i++) {
            String tenantId = tenantIds[i];
            if (tenantId.equals("1")) {
                continue;
            }
            log.info("执行 tenantId={}", tenantId);
            IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.SALES_ORDER_OBJ);
            if (describe != null && Objects.isNull(describe.getFieldDescribe(SalesOrderObjFields.ACTIVITY_ID))) {
                log.info("初始化字段");
                activityCostAssignNodeHandler.initSalesOrderTPMFields(tenantId, "activity_id", "activity_agreement_id", "dealer_activity_cost_id");
            }
            log.info("执行 tenantId={} 结束", tenantId);
        }
        log.info("end initAllTenantSalesOrderTPMFields");
    }

    @Override
    public void initAllTenantSalesOrderTPM815Fields() {
        log.info("init initAllTenantSalesOrderTPM815Fields");

        String TPM2TenantId = ConfigFactory.getConfig("variables_fmcg_gray").get("ei_list_fmcg_tpm_gray");
        log.info("TPM2TenantId = {} ", TPM2TenantId);
        String[] tenantIds = TPM2TenantId.split(",");
        for (int i = 0; i < tenantIds.length; i++) {
            String tenantId = tenantIds[i];
            if (tenantId.equals("1")) {
                continue;
            }
            log.info("执行 tenantId={}", tenantId);
            try {
                IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.SALES_ORDER_OBJ);
                if (describe != null && Objects.isNull(describe.getFieldDescribe(SalesOrderObjFields.STORE_WRITE_OFF_ID))) {
                    log.info("初始化字段");
                    activityCostAssignNodeHandler.initSalesOrderTPMFields(tenantId, "store_write_off_id", "activity_closed_status");
                }
            } catch (Exception e) {
                log.info("执行异常 tenantId={}  ", tenantId, e);
                continue;
            }

            log.info("执行 tenantId={} 结束", tenantId);
        }
        log.info("end initAllTenantSalesOrderTPM815Fields");
    }

    @Override
    public void initAllTenantSalesOrderTPM815DeleteFields(String tenantId) {

        List<String> deleteIds = Lists.newArrayList("747520", "749082", "684088", "749619", "753489",
                "750694", "750718", "748191", "748192", "709541",
                "748219", "751026", "748999", "752849", "750817",
                "748262");
        if (Objects.isNull(tenantId)) {
            deleteIds = Lists.newArrayList(tenantId);
        }
        log.info("start initAllTenantSalesOrderTPM815DeleteFields deleteIds ={} ", JSON.toJSONString(deleteIds));

        for (String deleteTenantId : deleteIds) {

            log.info("initAllTenantSalesOrderTPM815DeleteFields deleteTenantId={}", deleteTenantId);
            User superUser = User.systemUser(deleteTenantId);
            try {
                deleteFields(superUser, ApiNames.SALES_ORDER_OBJ, "store_write_off_id", "activity_closed_status");
            } catch (Exception e) {
                log.error("initAllTenantSalesOrderTPM815DeleteFields  error deleteTenantId={}", deleteTenantId, e);
            }
        }

        log.info("end initAllTenantSalesOrderTPM815DeleteFields");
    }

    @Override
    @MetadataTransactional
    public void doDisassembly(String tenantId, String objectDataIds, String flag) {
        if (StringUtils.isEmpty(objectDataIds)) {
            return;
        }
        List<String> ids = JSON.parseArray(objectDataIds, String.class);
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        for (String objectDataId : ids) {
            IObjectData masterData = serviceFacade.findObjectData(User.systemUser(tenantId), objectDataId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
            List<IObjectData> details = budgetAccountDetailService.queryDetailsByRelatedData(tenantId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, objectDataId);

            String budgetId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            List<IObjectData> collect = details.stream().filter(obj -> Objects.equals(obj.get("budget_account_id", String.class), budgetId) &&
                            Objects.equals(obj.get(TPMBudgetAccountDetailFields.BUSINESS_TYPE), BizType.TAKE_APART_OUT.value()) &&
                            Objects.equals(obj.get(TPMBudgetAccountDetailFields.MAIN_TYPE), MainType.FREEZE.value()))
                    .collect(Collectors.toList());
            log.info("doDisassembly collect:{}", JSON.toJSONString(collect));
            if (CollectionUtils.isEmpty(collect)) {
                continue;
            }

            String approvalTraceId = collect.get(0).get("approval_trace_id", String.class);
            String businessTraceId = collect.get(0).get("biz_trace_id", String.class);

            log.info("approvalTraceId:{}，businessTraceId:{}", approvalTraceId, businessTraceId);

            String sourceAccountId = masterData.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
            IObjectData sourceAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);

            String typeId = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
            BudgetTypePO budgetType = budgetTypeDAO.get(tenantId, typeId);
            if (Objects.isNull(budgetType)) {
                log.info("类型未找到，id:{}", objectDataId);
                continue;

            }
            String targetNodeId = masterData.get(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, String.class);
            BudgetTypeNodeEntity targetNode = budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(targetNodeId)).findFirst().orElse(null);
            if (Objects.isNull(targetNode)) {
                log.info("节点未找到，id:{}", objectDataId);
                continue;
            }

            IBudgetOperator sourceDisassemblyOperator = BudgetOperatorFactory.initOperator(BizType.TAKE_APART_OUT, User.systemUser(tenantId), sourceAccount.getId(),
                    businessTraceId, approvalTraceId, masterData);
            if (!sourceDisassemblyOperator.tryLock()) {
                log.info("转出预算表被他人占用，请稍后再试...id:{}", objectDataId);
                continue;
            }

            if (Objects.equals("unfreeze", flag)) {
                sourceDisassemblyOperator.unfreeze(BizType.APPROVAL_BACK);
                sourceDisassemblyOperator.recalculate();
                sourceDisassemblyOperator.unlock();
                log.info("unfreeze:APPROVAL_BACK...");
                continue;
            }
            log.info("doDisassembly for budget...");
            // flag:1 newDetail
            //flag:2 existsDetail
            List<IObjectData> newDetails = new ArrayList<>();
            List<IObjectData> existsDetails = new ArrayList<>();
            Map<String, IBudgetOperator> targetDisassemblyOperatorsMap = new HashMap<>();
            Map<String, BigDecimal> groupAmountById = new HashMap<>();

            if (Objects.equals("new", flag)) {
                newDetails = queryNewDetails(tenantId, objectDataId);
            } else if (Objects.equals("exists", flag)) {
                existsDetails = queryExistsDetails(tenantId, objectDataId);

                log.info("existsDetails:{}", JSON.toJSONString(existsDetails));
                boolean continueFlag = false;

                for (IObjectData existDetail : existsDetails) {
                    String accountId = existDetail.get(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID, String.class);
                    IBudgetOperator targetOperator = BudgetOperatorFactory.initOperator(BizType.TAKE_APART_IN, User.systemUser(tenantId),
                            accountId, businessTraceId, approvalTraceId, masterData);

                    if (!targetOperator.tryLock()) {
                        log.info("转入预算表被他人占用，请稍后再试...id:{}", objectDataId);
                        continueFlag = true;
                        break;

                    }
                    groupAmountById.put(accountId, new BigDecimal(existDetail.get(TPMBudgetDisassemblyExistsDetailsFields.AMOUNT, String.class)));
                    targetDisassemblyOperatorsMap.put(accountId, targetOperator);
                }

                if (continueFlag) {
                    log.info("转入预算表尝试被锁失败，请稍后再试...id:{}", objectDataId);
                    continue;
                }
            } else {
                throw new ValidateException(I18N.text(I18NKeys.MODULE_INITIALIZATION_SERVICE_0));
            }


            List<IObjectData> finalNewDetails = newDetails;
            List<IObjectData> accounts = Lists.newArrayList();

            transactionProxy.run(() -> {

                //paas
                //1、拆解预算表扣减预算
                BigDecimal unfreeze = sourceDisassemblyOperator.unfreeze(BizType.RELEASE);
                sourceDisassemblyOperator.expenditure(unfreeze);
                sourceDisassemblyOperator.recalculate();
                sourceDisassemblyOperator.unlock();
                log.info("unfreeze:{}", sourceDisassemblyOperator);

                //2、已有预算表增加预算
                if (Objects.equals("exists", flag)) {
                    for (IBudgetOperator targetDisassemblyOperator : targetDisassemblyOperatorsMap.values()) {
                        BigDecimal amountForExistDetail = groupAmountById.get(targetDisassemblyOperator.getAccount().getId());
                        targetDisassemblyOperator.income(amountForExistDetail);
                        targetDisassemblyOperator.recalculate();
                        log.info("targetDisassemblyOperator:{}", targetDisassemblyOperator);
                        log.info("amountForExistDetail:{}", amountForExistDetail);
                    }
                    for (IBudgetOperator operator : targetDisassemblyOperatorsMap.values()) {
                        operator.unlock();
                    }
                }


                if (Objects.equals("new", flag) && !CollectionUtils.isEmpty(finalNewDetails)) {
                    //3、新建预算表
                    List<IObjectData> dataForCreateBudgetAccountTable = new ArrayList<>();
                    for (IObjectData newDetail : finalNewDetails) {
                        dataForCreateBudgetAccountTable.add(budgetDisassemblyService.buildDataForCreateBudgetAccount(tenantId, newDetail, sourceAccount, targetNode));
                    }
                    log.info("dataForCreateBudgetAccountTable:{}", JSON.toJSONString(dataForCreateBudgetAccountTable));
                    if (!CollectionUtils.isEmpty(dataForCreateBudgetAccountTable)) {

                        for (IObjectData data : dataForCreateBudgetAccountTable) {
                            String totalAmount = data.get(TPMBudgetAccountFields.TOTAL_AMOUNT, String.class);
                            IObjectData account = budgetAccountService.createBudgetAccount(User.systemUser(tenantId), data, false, false, true, true);
                            accounts.add(account);
                            IBudgetOperator operator = BudgetOperatorFactory.initOperator(
                                    BizType.TAKE_APART_IN,
                                    User.systemUser(tenantId),
                                    data.getId(),
                                    businessTraceId,
                                    approvalTraceId,
                                    masterData);
                            operator.income(new BigDecimal(totalAmount));
                            budgetCalculateService.recalculateBudgetAmount(User.systemUser(tenantId), data.getId());
                        }
                    }
                }
            });

            budgetStatisticTableService.asyncDoStatistic(tenantId, accounts);
        }
    }

    @Override
    public void initCashingFieldFor840(String tenantIdsStr) {
        List<String> tenantIds = Lists.newArrayList();
        try {
            tenantIds.addAll(JSON.parseArray(tenantIdsStr, String.class));
        } catch (Exception e) {
            log.error("initCashingFieldFor840 parseArray error", e);
            return;
        }

        List<String> exTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                IObjectDescribe activityObjDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ);
                IObjectDescribe activityAgreementObjDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
                IObjectDescribe storeWriteOffObjDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);

                if (activityObjDescribe != null) {
                    activityCostAssignNodeHandler.initCashingFieldFor840(tenantId, ApiNames.TPM_ACTIVITY_OBJ, "dealer_cashing_type", false);
                    activityCostAssignNodeHandler.initCashingFieldFor840(tenantId, ApiNames.TPM_ACTIVITY_OBJ, "store_cashing_type", false);
                }

                if (activityAgreementObjDescribe != null) {
                    activityCostAssignNodeHandler.initCashingFieldFor840(tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "agreement_cashing_type", false);
                }

                if (storeWriteOffObjDescribe != null) {
                    activityCostAssignNodeHandler.initCashingFieldFor840(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ, "cashing_product_total_price", true);
                }

            } catch (Exception e) {
                log.info("执行异常 tenantId={}  ", tenantId, e);
                exTenantIds.add(tenantId);
            }
        }
        if (!CollectionUtils.isEmpty(exTenantIds)) {
            log.error("initCashingFieldFor840 errTenantIds:{}", JSON.toJSONString(exTenantIds));
        }
    }

    @Override
    public String addStoreWriteOffButton(StoreWriteButtonInit.Arg arg) {

        String source = arg.getSource();
        String tenantIds;
        if (source.equals("config")) {
            tenantIds = ConfigFactory.getConfig("variables_fmcg_gray").get("ei_list_fmcg_tpm_gray");
        } else {
            tenantIds = arg.getTenantIds();
        }
        if (StringUtils.isEmpty(tenantIds)) {
            return "id empty";
        }
        Set<String> tenantList = new HashSet<>();
        Arrays.stream(tenantIds.split(",")).forEach(tenantId -> tenantList.add(tenantId.trim()));
        for (String tenantId : tenantList) {

            if (tenantId.equals("1")) {
                continue;
            }
            String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"list_batch\", \"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";

            List<Map> list = getStoreButtonList(tenantId);
            Set<String> buttonApiNameSet = new HashSet<>();
            list.forEach(v -> buttonApiNameSet.add((String) v.get("api_name")));
            if (!buttonApiNameSet.contains("CostWriteOff_button_default")) {
                (specialTableMapper.setTenantId(tenantId)).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, "CostWriteOff_button_default", "门店费用核销", "TPMStoreWriteOffObj"));
            }
        }
        return "success";
    }

    @Override
    public void addAllTenantDealerCostDealerWheres(String tenantId) {
        List<String> tenantIds = getTenantIds(tenantId);

        if (CollectionUtils.isEmpty(tenantIds)) {
            return;
        }
        Map<String, Boolean> isActiveEnterprise = tpmEnterpriseService.batchIsActiveEnterprise(tenantIds.stream().map(Integer::parseInt).collect(Collectors.toList()));

        for (String id : tenantIds) {
            if (id.equals("1")) {
                continue;
            }
            if (!isActiveEnterprise.containsKey(id) || Boolean.FALSE.equals(isActiveEnterprise.get(id))) {
                continue;
            }
            try {
                IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_DEALER_ACTIVITY_COST);
                IFieldDescribe dealerDescribe = describe.getFieldDescribe(TPMDealerActivityCostFields.DEALER_ID);
                if (Objects.nonNull(dealerDescribe) && dealerDescribe instanceof ObjectReferenceFieldDescribe) {

                    ObjectReferenceFieldDescribe objectReferenceField = (ObjectReferenceFieldDescribe) dealerDescribe;
                    if (CollectionUtils.isEmpty(objectReferenceField.getWheres())) {
                        LinkedHashMap where = JSON.parseObject("{\n" +
                                "          \"connector\": \"OR\",\n" +
                                "          \"filters\": [\n" +
                                "            {\n" +
                                "              \"value_type\": 0,\n" +
                                "              \"operator\": \"EQ\",\n" +
                                "              \"field_name\": \"record_type\",\n" +
                                "              \"field_values\": [\n" +
                                "                \"dealer__c\"\n" +
                                "              ]\n" +
                                "            }\n" +
                                "          ]\n" +
                                "        }", LinkedHashMap.class);
                        objectReferenceField.setWheres(Lists.newArrayList(where));
                    }
                    serviceFacade.updateFieldDescribe(describe, Lists.newArrayList(dealerDescribe));
                }
            } catch (Exception e) {
                log.error("set dealer_id wheres err, tenantId={}", id, e);
            }
        }
    }

    @NotNull
    private List<String> getTenantIds(String tenantId) {
        /*
        * 710938,707988,748862
          ei_list_fmcg_tpm_yqsl=735454
        * */
        List<String> tenantIds = Lists.newArrayList(tenantId);
        if (StringUtils.isEmpty(tenantId)) {
            String TPM2TenantId = ConfigFactory.getConfig("variables_fmcg_gray").get("ei_list_fmcg_tpm_gray");
            log.info("TPM2TenantId = {} ", TPM2TenantId);
            tenantIds = Lists.newArrayList(TPM2TenantId.split(","));
        }
        return tenantIds;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void imgNPathToAPath(String tenantAccount, String nPaths) {
        if (!StringUtils.isEmpty(nPaths)) {
            String[] split = nPaths.split(",");
            if (split.length >= 1) {
                for (String nPath : split) {
                    CreateAFileFromNFile.NDownloadFileArg var1 = new CreateAFileFromNFile.NDownloadFileArg();
                    var1.setBusiness("Feed.WQ");
                    var1.setnPath(nPath);
                    var1.setUser(new com.facishare.warehouse.api.model.arg.User(-10000, tenantAccount));
                    var1.setEa(tenantAccount);

                    CreateAFileFromNFile.AUploadFileArg var2 = new CreateAFileFromNFile.AUploadFileArg();
                    var2.setBusiness("Feed.WQ");
                    var2.setFileExt("jpg");
                    var2.setUser(new com.facishare.warehouse.api.model.arg.User(-10000, tenantAccount));
                    var2.setPublic(true);


                    CreateAFileFromNFile.AUploadFileResult ret = fileUnityService.CreateAFileFromNFile(var1, var2);
                    String finalAPath = ret.getFinalAPath();
                    log.info("nPath:{},aPath:{}", nPath, finalAPath);
                }
            }
            return;
        }
        String pocProofImg = ConfigFactory.getConfig("IntegralService").get("poc_proof_img");
        if (StringUtils.isEmpty(pocProofImg)) {
            pocProofImg = "{\"DISPLAY_1\":[\"N_202303_03_8bab16d6febe4fc8acab0c7870510d03\",\"N_202303_03_e87d0238068e4423b365baecc5edb704\",\"N_202303_03_bea4637f5e0c46df884c1bc149b59d58\",\"N_202303_03_80dbd12810ad491a818e38b354bacd9a\",\"N_202303_03_61adbab2c76349dda55962865ab4a6d9\",\"N_202303_03_3692d83980e5421ca0a66b11b661d9f8\",\"N_202303_03_1f0eeb69f8c84942bb064d42693864d6\",\"N_202303_03_b4b7b73234504937ae255d7a77c8d8be\",\"N_202303_03_72e741c06366476d83ac3e9429cf6cfb\",\"N_202303_03_5ae71b822fa4460d9003e57ad6a7ea53\",\"N_202303_03_5f31f3cfbf58447b87018fbd4df61dde\",\"N_202303_03_f2777e2852ab49459bc84e6b5bb57058\",\"N_202303_03_198addb989374936b1a23ffac3fc8631\",\"N_202303_03_46083b8fded1436fa6937cf77cb96bd7\",\"N_202303_03_5950d335f4174980bc8188a1cb0dd89e\"],\"DISPLAY_2\":[\"N_202303_03_5953cf48c49a455ea10f4ea01cddaee2\",\"N_202303_03_e41bf2dd98f24d85b5e655e830300e5b\",\"N_202303_03_c73366aea52f423faa70fcce99f9ff01\",\"N_202303_03_7d086e74892a434688771078fb632a7b\",\"N_202303_03_154ce32afe8d46a493e5957737ab6971\"],\"LIVE_1\":[\"N_202303_03_8bab16d6febe4fc8acab0c7870510d03\",\"N_202303_03_e87d0238068e4423b365baecc5edb704\",\"N_202303_03_bea4637f5e0c46df884c1bc149b59d58\",\"N_202303_03_80dbd12810ad491a818e38b354bacd9a\",\"N_202303_03_61adbab2c76349dda55962865ab4a6d9\",\"N_202303_03_3692d83980e5421ca0a66b11b661d9f8\",\"N_202303_03_1f0eeb69f8c84942bb064d42693864d6\",\"N_202303_03_b4b7b73234504937ae255d7a77c8d8be\",\"N_202303_03_72e741c06366476d83ac3e9429cf6cfb\",\"N_202303_03_5ae71b822fa4460d9003e57ad6a7ea53\",\"N_202303_03_5f31f3cfbf58447b87018fbd4df61dde\",\"N_202303_03_f2777e2852ab49459bc84e6b5bb57058\",\"N_202303_03_198addb989374936b1a23ffac3fc8631\",\"N_202303_03_46083b8fded1436fa6937cf77cb96bd7\",\"N_202303_03_5950d335f4174980bc8188a1cb0dd89e\"],\"LIVE_2\":[\"N_202303_03_5953cf48c49a455ea10f4ea01cddaee2\",\"N_202303_03_e41bf2dd98f24d85b5e655e830300e5b\",\"N_202303_03_c73366aea52f423faa70fcce99f9ff01\",\"N_202303_03_7d086e74892a434688771078fb632a7b\",\"N_202303_03_154ce32afe8d46a493e5957737ab6971\"],\"JUDGE_1\":[\"N_202303_03_5aba97d17b234744902406196fd1c4df\",\"N_202303_03_1327395aa3d64ec7909ac5292582c195\",\"N_202303_03_ad92bfd6f6b243ffbb913b53e6483124\",\"N_202303_03_06a8bbe569f943e1b1cc42bbed1ff020\",\"N_202303_03_ddb1d2ac162e4e909fd940e52c2fc48a\",\"N_202303_03_6212645bd32f403a8bfc05899d939553\",\"N_202303_03_740d7d4e4971409084844eea474f0aaa\",\"N_202303_03_67d64c3ddbe5473ab4e35273fec6852a\",\"N_202303_03_39181cdfe9dc485cb046638c05c87511\",\"N_202303_03_58d8275eab4843ad84c19060fcfae336\",\"N_202303_03_b6508ee3923b4004b0108c46d22b75a7\",\"N_202303_03_abf8cf28e7174308997a895e9a361a80\",\"N_202303_03_530b5d3e95664725a04a8ae0f3832e2e\",\"N_202303_03_ecfd2b436bb54d6d8b2b8f3c17fcbd49\",\"N_202303_03_aebfe33e57f64a8fbeb0fabd5e2e4971\"],\"JUDGE_2\":[\"N_202303_03_27ce05e482014e1d9bdc7187f85f9aa9\",\"N_202303_03_47fe1b2e80c14b51807da2fac8b57a21\",\"N_202303_03_7cb361c9931d45928e650a06f0d63d09\",\"N_202303_03_f6096fcd04ed416bb6531f713bc2286e\"],\"SAMPLE_1\":[\"N_202303_03_5e9be1a6716c43f58577808fcee63357\",\"N_202303_03_821b0822c5de42c4abc6481b179dfaa5\",\"N_202303_03_430e27a2852d4269ae7b902fbbdbef69\",\"N_202303_03_b8a120590dbb4ea7ad5a18fb0c932f68\",\"N_202303_03_1e1412ddc6074f6f8c24c4982c69cf52\",\"N_202303_03_ef74999a6e39417086081b48db7c1d41\",\"N_202303_03_0f542eff5a204ebd8224868a84d2da40\",\"N_202303_03_309cd5eaff854d8da2e8ceff46e4ec9c\",\"N_202303_03_9271c56e65dc492ba37f1ea37543893f\",\"N_202303_03_f4418b92ee0b4aeaa634c58b287121e0\",\"N_202303_03_83c0264db8c04f078a84519695e96d5b\",\"N_202303_03_a6cb5395a11048af9090dec73133af4a\",\"N_202303_03_c3b548d1364d45e38e924253d23c9182\",\"N_202303_03_0fd7197119ce44029e657567e6ecd594\",\"N_202303_03_3180b8a8aa1e48eb9d1dfa23eec78a0e\"],\"SAMPLE_2\":[\"N_202303_03_12e8d70c5d884228bade3878c378e740\",\"N_202303_03_d4f57b2d66d6438f93513f3c8e17ad3b\",\"N_202303_03_f7697a722c3e436d82fdfc1867ff765e\",\"N_202303_03_17bd5d1d4f5541729460314b4e802b1a\",\"N_202303_03_b54210533f844e538b5b93cbc81bf0a9\"],\"WHOLE_1\":[\"N_202303_03_91dec46ce3d34ae1a4aa135b15da7131\",\"N_202303_03_68a446ade9134696ab1aacc27c70fa4b\",\"N_202303_03_c7b5a26852b14c2cbccfc388cd506556\",\"N_202303_03_7ce6db7044b140d2b7fad519c83bab64\",\"N_202303_03_e4e47be7bade40f0bf64855be189bd6d\",\"N_202303_03_d24d151862094cc28c798bd0508bc7e4\",\"N_202303_03_4c4dde9c9ce3402f982114e3a2b72f36\",\"N_202303_03_46633abc6bc54121acf01664aac769a2\",\"N_202303_03_330c143614d84156b4623f5cf6ff3fb9\",\"N_202303_03_8e5ff291a7d14205a1d4b65638837e7a\",\"N_202303_03_bdd9ee5e9f384c4d8e2ea94e50d257a6\",\"N_202303_03_944ca020115a4fd29a27cb87f29d97a9\",\"N_202303_03_46182aadf3164ce2b563ca6d50693ab4\",\"N_202303_03_ddbb01e1453d4ffcbbdc96f515ef77db\",\"N_202303_03_f2e102b5d8aa4b88baddf407f51835a2\",\"N_202303_03_4e795987ca9a47d5a1cd1d2ff4b66508\",\"N_202303_03_e542e506084a471bac398f82aee70184\",\"N_202303_03_df4541f131b1471f95e6ebe3531f9a34\",\"N_202303_03_35f3a1bed3aa40c7bb7cc6bb872f72fa\",\"N_202303_03_15a2f9ff7c774119a533585feb4107f4\",\"N_202303_03_014cfefe5d1743e6b1a3572c80f20246\",\"N_202303_03_dbd20713b50740e684c6c77f49cb2082\"],\"WHOLE_2\":[\"N_202303_03_555c2af9083e46129aa6623fc2697c73\",\"N_202303_03_b872a9fbc6644da9aaa871cb371031ed\",\"N_202303_03_b46dba4da2644a4c8275a47872b12530\",\"N_202303_03_76084aa6234544d68597748fb021efea\",\"N_202303_03_60916b4a1c0c45f9bba28e6e188c2986\"]}";
        }
        Map<String, List<String>> map = (Map<String, List<String>>) JSON.parseObject(pocProofImg, Map.class);
        Map<String, List<String>> result = Maps.newHashMap();
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            for (String nPath : entry.getValue()) {

                CreateAFileFromNFile.NDownloadFileArg var1 = new CreateAFileFromNFile.NDownloadFileArg();
                var1.setBusiness("Feed.WQ");
                var1.setnPath(nPath);
                var1.setUser(new com.facishare.warehouse.api.model.arg.User(-10000, tenantAccount));
                var1.setEa(tenantAccount);

                CreateAFileFromNFile.AUploadFileArg var2 = new CreateAFileFromNFile.AUploadFileArg();
                var2.setBusiness("Feed.WQ");
                var2.setFileExt("jpg");
                var2.setUser(new com.facishare.warehouse.api.model.arg.User(-10000, tenantAccount));
                var2.setPublic(true);


                CreateAFileFromNFile.AUploadFileResult ret = fileUnityService.CreateAFileFromNFile(var1, var2);
                String finalAPath = ret.getFinalAPath();

                if (result.get(entry.getKey()) == null) {
                    result.put(entry.getKey(), Lists.newArrayList(finalAPath));
                } else {
                    result.get(entry.getKey()).add(finalAPath);
                }
            }


        }
        log.info("imgNPathToAPath result:{}", JSON.toJSONString(result));
        log.info("imgNPathToAPath result:{}", JSON.toJSONString(result.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (old, newD) -> newD, LinkedHashMap::new))));

    }

    private Map<String, IFieldDescribe> describeFieldMap(String tenantId, String apiName) throws MetadataServiceException {

        IObjectDescribe activityDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName);
        Map<String, IFieldDescribe> fieldsMap = Maps.newHashMap();
        List<IFieldDescribe> fieldDescribes = activityDescribe.getFieldDescribes();
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            fieldsMap.put(fieldDescribe.get("label", String.class), fieldDescribe);
        }
        return fieldsMap;
    }

    private List<IObjectData> findByNameWithFields(String tenantId, String apiName, String name) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(200);
        query.setOffset(0);

        query.setNeedReturnCountNum(false);

        IFilter filter = new Filter();
        filter.setFieldName(CommonFields.NAME);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(name));

        query.setFilters(Lists.newArrayList(filter));

        return QueryDataUtil.find(serviceFacade, tenantId, apiName, query, Lists.newArrayList("_id", "name"));

    }

    private String convertReferenceValue(String tenantId, String targetApiName, String label, String value, String relatedFlag) {

        List<IObjectData> targetData = findByNameWithFields(tenantId, targetApiName, value);
        if (targetData.size() > 1) {
            log.info(String.format("【%s】有重名，请选择主属性导入。主属性标志：%s", label, relatedFlag));
            return "";
        }
        if (targetData.size() == 0) {
            log.info(String.format("使用【%s】没查到数据，请检查主属性是否有误。主属性标志：%s", label, relatedFlag));
            return "";
        }
        return targetData.get(0).getId();
    }

    private List<String> convertReferenceManyValue(String tenantId, String targetApiName, String label, String value, String relatedFlag) {
        Set<String> objectIds = new HashSet<>();
        for (String datum : value.split(",")) {
            if (StringUtils.isEmpty(datum)) {
                continue;
            }
            List<IObjectData> targetData = findByNameWithFields(tenantId, targetApiName, datum.trim());
            if (targetData.size() > 1) {
                log.info(String.format("查找关联多选：【%s】有重名，请选择主属性导入。主属性标志：%s", label, relatedFlag));
                return Lists.newArrayList();
            }
            if (targetData.size() == 0) {
                log.info(String.format("查找关联多选：使用【%s】没查到数据，请检查主属性是否有误。主属性标志：%s", label, relatedFlag));
                return Lists.newArrayList();
            }
            objectIds.add(targetData.get(0).getId());
        }

        return Lists.newArrayList(objectIds);
    }

    private List<Map> getStoreButtonList(String tenantId) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name = 'CostWriteOff_button_default' and describe_api_name = 'TPMStoreWriteOffObj'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    @SuppressWarnings("unchecked")
    private String convertSelectOne(IFieldDescribe fieldDescribe, String value) {
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        return options.stream().filter(option -> Objects.equals(value, option.get("label"))).map(option -> option.get("value")).findFirst().orElse("");
    }

    @SuppressWarnings("unchecked")
    private String convertRecordType(IFieldDescribe fieldDescribe, String value) {
        List<Map<String, String>> options = (List<Map<String, String>>) fieldDescribe.get("options");
        return options.stream().filter(option -> Objects.equals(value, option.get("label"))).map(option -> option.get("api_name")).findFirst().orElse("");
    }

    private List<String> convertDepartment(String tenantId, String value) {
        List<String> departmentNames = Arrays.stream(value.split(",")).collect(Collectors.toList());
        return organizationService.getDepartmentIdsByNames(Integer.parseInt(tenantId), departmentNames);
    }

    private Map<String, List<ObjectDataDocument>> convertData(String data) {

        List<ObjectDataDocument> maps = JSON.parseArray(data, ObjectDataDocument.class);
        return maps.stream().collect(Collectors.groupingBy(objectData -> String.valueOf(objectData.get("关联标识（必填）"))));
    }

    private Map<String, List<Map<String, Object>>> handlerData(String tenantId, String data, Map<String, IFieldDescribe> fieldsMap) throws ParseException {

        Map<String, List<ObjectDataDocument>> dataAfterConvert = convertData(data);

        Map<String, List<Map<String, Object>>> result = Maps.newHashMap();
        for (Map.Entry<String, List<ObjectDataDocument>> entry : dataAfterConvert.entrySet()) {
            String relatedFlag = entry.getKey();
            List<ObjectDataDocument> importObjectData = entry.getValue();
            Map<String, Object> objectMap;

            try {
                master:
                for (ObjectDataDocument importObjectDatum : importObjectData) {
                    objectMap = Maps.newHashMap();

                    for (Map.Entry<String, Object> objectEntry : importObjectDatum.entrySet()) {

                        String label = objectEntry.getKey().replace("（必填）", "").trim();
                        String value = String.valueOf(objectEntry.getValue()).trim();
                        IFieldDescribe fieldDescribe = fieldsMap.get(label);
                        if (fieldDescribe == null) {
                            if (!Objects.equals("关联标识", label) && !Objects.equals("主对象ID(请勿编辑此列)", label)) {
                                log.info("fieldDescribe is null,label:{}", label);
                            }
                            continue;
                        }
                        String type = fieldDescribe.getType();
                        String apiName = fieldDescribe.getApiName();
                        if (StringUtils.isEmpty(value)) {
                            continue;
                        }
                        switch (type) {
                            case "object_reference": {
                                String targetApiName = fieldDescribe.get("target_api_name", String.class);
                                String id = convertReferenceValue(tenantId, targetApiName, label, value, entry.getKey());
                                if (StringUtils.isEmpty(id)) {
                                    break master;
                                }
                                objectMap.put(apiName, id);
                                break;
                            }
                            case "object_reference_many": {
                                String targetApiName = fieldDescribe.get("target_api_name", String.class);
                                List<String> ids = convertReferenceManyValue(tenantId, targetApiName, label, value, entry.getKey());
                                if (CollectionUtils.isEmpty(ids)) {
                                    break master;
                                }
                                objectMap.put(apiName, ids);
                                break;
                            }
                            case "select_one": {
                                String id = convertSelectOne(fieldDescribe, value);
                                if (StringUtils.isEmpty(id)) {
                                    log.info(String.format("使用【%s】没查到数据，请检查单选值是否有误。主属性标志：【%s】", value, entry.getKey()));
                                    break master;
                                }
                                objectMap.put(apiName, id);
                                break;
                            }
                            case "department":
                            case "department_many": {
                                if (Objects.equals("999999", value)) {
                                    objectMap.put(apiName, Lists.newArrayList("999999"));
                                } else {
                                    List<String> departmentIds = convertDepartment(tenantId, value);
                                    if (CollectionUtils.isEmpty(departmentIds)) {
                                        log.info(String.format("必填字段：【%s】未填写。主属性标志：【%s】", label, entry.getKey()));
                                        break master;
                                    }
                                    objectMap.put(apiName, departmentIds);
                                }

                                break;
                            }
                            case "date_time": {
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                                Date parse = simpleDateFormat.parse(value);
                                objectMap.put(apiName, parse.getTime());
                                break;
                            }
                            case "date": {
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
                                Date parse = simpleDateFormat.parse(value);
                                objectMap.put(apiName, parse.getTime());
                                break;
                            }
                            case "employee": {
                                GetEmployeesDtoByNameArg arg = new GetEmployeesDtoByNameArg();
                                arg.setName(value);
                                arg.setEnterpriseId(Integer.parseInt(tenantId));
                                GetEmployeesDtoByNameResult employeesByName = employeeProviderService.getEmployeesByName(arg);
                                if (employeesByName.getEmployeeDto() == null) {
                                    log.info("查无此人:{},related flag:{}", value, relatedFlag);
                                    break master;
                                }
                                objectMap.put(apiName, Lists.newArrayList(String.valueOf(employeesByName.getEmployeeDto().getEmployeeId())));
                                break;
                            }
                            case "employee_many": {
                                Set<String> employeeIds = new HashSet<>();
                                for (String datum : value.split(",")) {
                                    if (StringUtils.isEmpty(datum)) {
                                        continue;
                                    }
                                    GetEmployeesDtoByNameArg arg = new GetEmployeesDtoByNameArg();
                                    arg.setName(datum);
                                    arg.setEnterpriseId(Integer.parseInt(tenantId));
                                    GetEmployeesDtoByNameResult employeesByName = employeeProviderService.getEmployeesByName(arg);
                                    if (employeesByName.getEmployeeDto() == null) {
                                        log.info("查无此人:{},related flag:{}", datum, relatedFlag);
                                        break master;
                                    }

                                    employeeIds.add(String.valueOf(employeesByName.getEmployeeDto().getEmployeeId()));
                                }
                                objectMap.put(apiName, Lists.newArrayList(employeeIds));
                                break;
                            }
                            case "record_type": {
                                String id = convertRecordType(fieldDescribe, value);
                                if (StringUtils.isEmpty(id)) {
                                    log.info(String.format("使用【%s】没查到数据，请检查业务类型是否有误。主属性标志：【%s】", value, entry.getKey()));
                                    break master;
                                }
                                objectMap.put(apiName, id);
                                break;
                            }
                            default: {
                                objectMap.put(apiName, value);
                                break;
                            }
                        }

                    }
                    if (result.get(relatedFlag) != null) {
                        result.get(relatedFlag).add(objectMap);
                    } else {
                        result.put(relatedFlag, Lists.newArrayList(objectMap));
                    }
                }
            } catch (Exception ex) {
                log.info("handler data single data error,related flag:{}", relatedFlag, ex);
            }


        }

        return result.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (old, newD) -> newD, LinkedHashMap::new));
    }

    @Override
    public void kdhActivityImport(String tenantId, ActivityImport.Arg arg) {


        String masterPath = "/tpm/kk/kdh_activity_import.json";
        String detailPath = "/tpm/kk/kdh_account.json";
        try {
            Map<String, IFieldDescribe> activityFieldsMap = describeFieldMap(tenantId, ApiNames.TPM_ACTIVITY_OBJ);
            Map<String, IFieldDescribe> detailFieldsMap = describeFieldMap(tenantId, "object_2aHZm__c");
            String masterData;
            String detailData;
            if (arg != null && !StringUtils.isEmpty(arg.getMasterData()) && !StringUtils.isEmpty(arg.getDetailData())) {
                masterData = arg.getMasterData();
                detailData = arg.getDetailData();
            } else {
                masterData = ConfigFactory.getConfig("fmcg-custom-config").get("masterData");
                if (StringUtils.isEmpty(masterData)) {
                    masterData = read(masterPath);
                }

                detailData = ConfigFactory.getConfig("fmcg-custom-config").get("detailData");
                if (StringUtils.isEmpty(detailData)) {
                    detailData = read(detailPath);
                }
            }


            Map<String, List<Map<String, Object>>> afterHandlerMasterData = handlerData(tenantId, masterData, activityFieldsMap);
            Map<String, List<Map<String, Object>>> afterHandlerDetailData = handlerData(tenantId, detailData, detailFieldsMap);
            int begin = ConfigFactory.getConfig("fmcg-custom-config").getInt("importBegin", 0);
            int end = ConfigFactory.getConfig("fmcg-custom-config").getInt("importEnd", 0);
            int flag = 0;
            for (Map.Entry<String, List<Map<String, Object>>> entry : afterHandlerMasterData.entrySet()) {
                ++flag;
                if (flag < begin || flag > end) {
                    continue;
                }
                try {
                    Map<String, Object> master = entry.getValue().get(0);
                    List<Map<String, Object>> details = afterHandlerDetailData.get(entry.getKey());
                    ObjectDataDocument masterObject = new ObjectDataDocument();
                    masterObject.putAll(master);

                    List<ObjectDataDocument> detailsObject = details.stream().map(data -> {
                        ObjectDataDocument objectDataDocument = new ObjectDataDocument();
                        objectDataDocument.putAll(data);
                        return objectDataDocument;
                    }).collect(Collectors.toList());

                    BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
                    ActionContext addActionContext = new ActionContext(RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build(),
                            ApiNames.TPM_ACTIVITY_OBJ, "Add");
                    addActionContext.setAttribute("triggerWorkflow", false);
                    addActionContext.setAttribute("triggerFlow", false);

                    saveArg.setObjectData(masterObject);
                    Map<String, List<ObjectDataDocument>> detailsMap = Maps.newHashMap();
                    detailsMap.put("object_2aHZm__c", detailsObject);
                    saveArg.setDetails(detailsMap);
                    BaseObjectSaveAction.Result result = serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
                } catch (Exception ex) {
                    log.info("trigger action error,related flag:{}", entry.getKey(), ex);
                }
            }

        } catch (Exception ex) {
            log.info("kdhActivityImport ex", ex);
        }


    }

    @Override
    public void fmCashingType(String tenantId, String limitStr) {
        int i = 0;
        SearchTemplateQuery query = new SearchTemplateQuery();
        int limit = 1;
        if (!StringUtils.isEmpty(limitStr)) {
            try {
                limit = Integer.parseInt(limitStr);
            } catch (Exception e) {
                log.error("error parseInt", e);
            }

        }
        int offset = 0;
        query.setOffset(offset);
        query.setLimit(limit);


        OrderBy order = new OrderBy();
        order.setFieldName("create_time");
        order.setIsAsc(false);
        order.setIsNullLast(false);

        query.setOrders(Lists.newArrayList(order));

        Filter filter = new Filter();
        filter.setFieldName(TPMActivityFields.DEALER_CASHING_TYPE);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(TPMActivityCashingProductFields.GOODS));

        Filter filter2 = new Filter();
        filter2.setFieldName(TPMActivityFields.STORE_CASHING_TYPE);
        filter2.setOperator(Operator.EQ);
        filter2.setFieldValues(Lists.newArrayList(TPMActivityCashingProductFields.GOODS));


        Wheres wheres = new Wheres();
        wheres.setConnector(SearchQuery.Connector.OR.name());
        wheres.setFilters(Lists.newArrayList(filter));

        Wheres wheres2 = new Wheres();
        wheres2.setConnector(SearchQuery.Connector.OR.name());
        wheres2.setFilters(Lists.newArrayList(filter2));


        query.setWheres(Lists.newArrayList(wheres, wheres2));

        List<IObjectData> dataList;
        while (!(dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), "TPMActivityObj", query).getData()).isEmpty()) {
            i++;
            if (i > limit) {
                break;
            }
            List<String> updateField = Lists.newArrayList(TPMActivityFields.DEALER_CASHING_TYPE, TPMActivityFields.STORE_CASHING_TYPE);
            for (IObjectData data : dataList) {
                data.set(TPMActivityFields.DEALER_CASHING_TYPE, TPMActivityCashingProductFields.CASH);
                data.set(TPMActivityFields.STORE_CASHING_TYPE, TPMActivityCashingProductFields.CASH);
            }
            try {
                serviceFacade.batchUpdateByFields(User.systemUser(tenantId), dataList, updateField);
            } catch (Exception e) {
                log.info("batchUpdateByFields error.", e);
                break;
            }

        }


    }

    @Override
    public void changeAccrualRuleData(String tenantId, Integer limit) {
        limit = limit == null ? 50 : limit;
        List<BudgetAccrualRulePO> accrualRulePOS = budgetAccrualRuleDAO.list(tenantId, null, null, limit, 0);
        for (BudgetAccrualRulePO accrualRulePO : accrualRulePOS) {
            if (accrualRulePO.getBudgetType() != null) {
                accrualRulePO.getAccrualRuleNodes().forEach(vo -> {
                    vo.setBudgetType(accrualRulePO.getBudgetType());
                });
                budgetAccrualRuleDAO.edit(accrualRulePO.getTenantId(), accrualRulePO.getCreator(), accrualRulePO.getUniqueId(), accrualRulePO);
            }
        }
    }

    @Override
    public void fixDisassemblyStatus(String tenantId, List<String> dataIds, String status) {
        List<String> blackStatus = Lists.newArrayList(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED,
                TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN_FAILED, TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED,
                TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FROZEN, TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN,
                TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SUCCESS, TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__SCHEDULE);
        List<IObjectData> masterData = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, dataIds, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);

        for (IObjectData masterDatum : masterData) {
            String statusFromDB = masterDatum.get(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, String.class);
            if (!TPMGrayUtils.allowFixAnyDataDisassemblyStatus(tenantId) && blackStatus.contains(statusFromDB)) {
                log.info("black status,dataId:{}", masterDatum.getId());
                continue;
            }
            Map<String, Object> updateMap = Maps.newHashMap();
            updateMap.put(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS, status);
            serviceFacade.updateWithMap(User.systemUser(tenantId), masterDatum, updateMap);
        }
    }

    @Override
    public void fixActivityStatus(String tenantId, List<String> dataIds, String status) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ);

        List<IObjectData> activityObjs = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, dataIds, ApiNames.TPM_ACTIVITY_OBJ);
        for (IObjectData activityObj : activityObjs) {
            String statusForDB = activityObj.get(TPMActivityFields.ACTIVITY_STATUS, String.class);
            if (!Strings.isNullOrEmpty(statusForDB)) {
                IObjectData objectData = ObjectDataExt.of(activityObj).copy();
                Map<String, Object> updateMap = Maps.newHashMap();
                updateMap.put(TPMActivityFields.ACTIVITY_STATUS, status);
                serviceFacade.updateWithMap(User.systemUser(tenantId), activityObj, updateMap);
                // 修改记录
                serviceFacade.log(User.systemUser(tenantId), EventType.MODIFY, ActionType.Modify, describe, activityObj, updateMap, objectData);
            }
        }
    }

    @Override
    public void fixHistoryData(String tenantId,
                               List<String> dataIds,
                               Map<String, Object> updateFieldMap,
                               String apiName) {
        if (MapUtils.isNullOrEmpty(updateFieldMap)) {
            return;
        }
        List<IObjectData> activityObjs = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, dataIds, apiName);
        for (IObjectData activityObj : activityObjs) {
            Map<String, Object> updateMap = Maps.newHashMap();
            updateMap.putAll(updateFieldMap);
            serviceFacade.updateWithMap(User.systemUser(tenantId), activityObj, updateMap);
        }
    }

    @Override
    public String updateActivityType(UpdateActivityType.Arg arg) {
        String tenantId = arg.getTenantId();
        Integer employeeId = arg.getEmployeeId();
        List<String> dataIds = arg.getDataIds();
        String param = arg.getParam();
        for (String dataId : dataIds) {
            ActivityTypeExt activityTypeExt = activityTypeManager.find(tenantId, dataId);
            ActivityPlanConfigEntity activityPlanConfigEntity = activityTypeExt.activityPlanConfig();
            if (activityPlanConfigEntity == null) {
                continue;
            }
            if ("cycle".equals(arg.getAction())) {
                activityPlanConfigEntity.setEnableActivityCycleControl("1".equals(param));
            } else if ("proNode".equals(arg.getAction())) {
                activityPlanConfigEntity.setEnableRelationPreNodeRequired("1".equals(param));
            }
            activityTypeManager.update(tenantId, employeeId, dataId, activityTypeExt.get());
        }
        return "success";
    }

    @Override
    public void unFrozenForDisassemblyStatusFailed(String tenantId, List<String> dataIds) {

        for (String dataId : dataIds) {
            fixDisassemblyStatus(tenantId, Lists.newArrayList(dataId), TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED);
            asyncBudgetDisassemblyService.unFrozen(User.systemUser(tenantId), dataId);
        }
    }

    @Override
    public void doDisassemblyV2(String tenantId, List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return;
        }
        for (String dataId : dataIds) {
            asyncBudgetDisassemblyService.doDisassembly(User.systemUser(tenantId), dataId, DisassemblyActionCode.DISASSEMBLY_RETRY.value());
        }
    }

    @Override
    public void updateRedPacketExpire(UpdateRedPacketExpire.Arg arg) {
        String tenantId = arg.getTenantId();
        Long expireTime = arg.getExpireTime();
        String status = arg.getStatus();
        log.info("updateRedPacketExpire,tenantId:{},expireTime:{},status:{}", tenantId, expireTime, status);
        User user = User.systemUser(tenantId);
        SearchTemplateQuery searchTemplateQuery = QueryDataUtil.minimumQuery();

        Filter redStatusFilter = new Filter();
        redStatusFilter.setFieldName(RedPacketRecordObjFields.RED_PACKET_STATUS);
        redStatusFilter.setOperator(Operator.EQ);
        redStatusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(RedPacketRecordObjFields.WITHDRAWAL_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.WithdrawalStatus.AWAIT));

        searchTemplateQuery.setFilters(Lists.newArrayList(redStatusFilter, statusFilter));

        // 添加过期时间过滤条件
        if (Boolean.TRUE.equals(arg.getFilterByExpirationTime())) {
            Filter expirationTimeFilter = new Filter();
            expirationTimeFilter.setFieldName(RedPacketRecordObjFields.EXPIRATION_TIME);
            expirationTimeFilter.setOperator(Operator.IS);
            expirationTimeFilter.setFieldValues(Lists.newArrayList());
            searchTemplateQuery.getFilters().add(expirationTimeFilter);
        }

        // 如果有数据id，则只更新指定的数据，不然，就更新所属活动的数据
        if (CollectionUtils.isEmpty(arg.getDataIds())) {
            Filter activityFilter = new Filter();
            activityFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
            activityFilter.setOperator(Operator.EQ);
            activityFilter.setFieldValues(Lists.newArrayList(arg.getActivityId()));

            searchTemplateQuery.getFilters().add(activityFilter);
        } else {
            Filter idFilter = new Filter();
            idFilter.setFieldName(CommonFields.ID);
            idFilter.setOperator(Operator.IN);
            idFilter.setFieldValues(Lists.newArrayList(arg.getDataIds()));

            searchTemplateQuery.getFilters().add(idFilter);
        }
        List<String> fields = Lists.newArrayList(CommonFields.ID, CommonFields.TENANT_ID,
                CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME, RedPacketRecordObjFields.EXPIRATION_TIME,
                RedPacketRecordObjFields.ACTIVITY_ID, RedPacketRecordObjFields.RED_PACKET_STATUS,
                RedPacketRecordObjFields.WITHDRAWAL_STATUS);
        QueryDataUtil.findAndConsume(serviceFacade, user, ApiNames.RED_PACKET_RECORD_OBJ, searchTemplateQuery, fields, dataList -> {
            List<List<IObjectData>> partition = Lists.partition(dataList, 200);
            for (List<IObjectData> objectData : partition) {
                batchUpdateRedPacketStatus(user, objectData, expireTime, status);
            }
        });
    }

    @Override
    public void authUrl(String tenantId, String userId) {
        IChangeableConfig config = ConfigFactory.getConfig("fs-fmcg-framework-config");
        String authConfig = config.get("tongfu_auth_config");
        if (!Strings.isNullOrEmpty(authConfig)) {
            try {
                JSONObject authConfigJson = JSONObject.parseObject(authConfig);
                log.info("load tongfu auth config : {}", authConfigJson);
            } catch (Exception ex) {
                log.error("load tongfu auth config error : ", ex);
            }
        }

        List<IObjectData> areas = queryArea(tenantId, userId);
        log.info("queryArea size: {}", areas.size());

    }

    @Override
    public void initDmsObjectFor950(String tenantIds) {

        String[] split = tenantIds.split(",");
        if (split == null) {
            return;
        }
        for (String tenantId : split) {
            try {
                addFields(tenantId, false, ApiNames.PAYMENT_OBJ, PaymentFields.RETURNED_GOODS_INVOICE_ID);
            } catch (Exception ex) {
                log.error("新增回款单.退货单字段失败", ex);
            }

            try {
                addFields(tenantId, true, ApiNames.ENTERPRISE_FUND_ACCOUNT_OBJ, EnterpriseFundAccountObjFields.TOTAL_EXPENSE_AMOUNT);
            } catch (Exception ex) {
                log.error("新增回款单.调帐单累计支出字段失败", ex);
            }
            try {
                addFields(tenantId, true, ApiNames.ENTERPRISE_FUND_ACCOUNT_OBJ, EnterpriseFundAccountObjFields.TOTAL_REVENUE_AMOUNT);
            } catch (Exception ex) {
                log.error("新增回款单.调帐单累计收入字段失败", ex);
            }

            updateFields(tenantId);


            try {
                PaasDescribeCreate.Arg arg = buildDescribeArg(ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ);
                PaasDescribeCreate.Result result = paasDescribeProxy.create(Integer.parseInt(tenantId), -10000, arg);
                if (result.getErrCode() != 0 && result.getErrCode() != *********) {
                    log.error("init obj error,apiName:{},error msg:{}", ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, result.getErrMessage());
                }
                // 创建icon_slot
                updateIconSlot(Integer.parseInt(tenantId), ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ);
            } catch (Exception ex) {
                log.error("create describe has exception", ex);
            }

            addLayout(tenantId);
            addRecordType(tenantId);
            updateRecordType(tenantId);
            assignLayout(tenantId);
            assignRecordType(tenantId);

        }
    }

    @Override
    public void initDmsObjectFor955(String tenantIds) {

        for (String tenantId : tenantIds.split(",")) {
            IObjectDescribe returnGoodsInvoice = null;
            try {
                IObjectDescribe payment = serviceFacade.findObject(tenantId, ApiNames.PAYMENT_OBJ);
                for (IFieldDescribe fieldDescribe : payment.getFieldDescribes()) {
                    if (PaymentFields.RETURNED_GOODS_INVOICE_ID.equals(fieldDescribe.getApiName())) {

                        returnGoodsInvoice = serviceFacade.findObject(tenantId, ApiNames.RETURNED_GOODS_INVOICE_OBJ);

                        addFields(tenantId, false, ApiNames.RETURNED_GOODS_INVOICE_OBJ, ReturnedGoodsInvoiceFields.EXCHANGE_SETTLEMENT_AMOUNT);

                    }
                }

            } catch (Exception ex) {
                log.error("新增退货单单.换货结算金额失败", ex);
            }
            try {
                if (returnGoodsInvoice != null) {

                    for (IFieldDescribe fieldDescribe : returnGoodsInvoice.getFieldDescribes()) {
                        if (ReturnedGoodsInvoiceFields.PENDING_REFUND_AMOUNT.equals(fieldDescribe.getApiName())) {

                            fieldDescribe.set("expression", "(IF($returned_goods_inv_amount$<0, -$returned_goods_inv_amount$, $returned_goods_inv_amount$)-MAX(MAX(IF($exchange_settlement_amount$<0, -$exchange_settlement_amount$, $exchange_settlement_amount$),IF($refund_amount$<0, -$refund_amount$, $refund_amount$)),IF($total_settled_amount$<0, -$total_settled_amount$, $total_settled_amount$)))*IF($returned_goods_inv_amount$<0, 1, -1)");

                            serviceFacade.updateFieldDescribe(returnGoodsInvoice, Lists.newArrayList(fieldDescribe));
                        }
                    }
                }

            } catch (Exception ex) {
                log.error("updateFields RETURNED_GOODS_INVOICE_OBJ error,", ex);
            }
        }
    }


    @Override
    public void updateRedPacket(String tenantId, String flag, String fromConfig) {
        if ("invalid".equals(flag)) {
            invalidData(tenantId, fromConfig);
        } else {
            List<String> ids = getIds(fromConfig);
            log.info("updateRedPacket ids size: {}", ids);
            List<List<String>> partition = Lists.partition(ids, 200);
            for (List<String> idsItem : partition) {
                List<IObjectData> data = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, idsItem, ApiNames.RED_PACKET_RECORD_OBJ);
                User systemUser = User.systemUser(tenantId);
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.RedPacketStatus.INVALID);
                serviceFacade.batchUpdateWithMap(systemUser, data, updateMap);
            }


        }
    }

    private void invalidData(String tenantId, String fromConfig) {

        List<String> ids = getIds(fromConfig);


        int i = 0;
        SearchTemplateQuery query = new SearchTemplateQuery();

        int offset = 0;
        query.setOffset(offset);
        query.setLimit(200);


        OrderBy order = new OrderBy();
        order.setFieldName("create_time");
        order.setIsAsc(false);
        order.setIsNullLast(false);

        Filter filter = new Filter();
        filter.setFieldName(RedPacketRecordObjFields.RED_PACKET_STATUS);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.RedPacketStatus.INVALID));


        query.setFilters(Lists.newArrayList(filter));


        query.setOrders(Lists.newArrayList(order));

        while (true) {
            i++;
            if (i > 80) {
                break;
            }
            List<IObjectData> objData = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query).getData();

            if (CollectionUtils.isEmpty(objData)) {
                break;
            }

            try {
                List<IObjectData> invalidData = objData.stream().filter(id -> ids.contains(id.getId())).collect(Collectors.toList());
                serviceFacade.bulkInvalid(invalidData, User.systemUser(tenantId));
            } catch (Exception e) {
                log.info("update is_report_item_quantity  error", e);
                break;
            }

        }
    }

    private List<String> getIds(String fromConfig) {

        List<String> idsResult = Lists.newArrayList();
        if ("true".equals(fromConfig)) {
            String idsStrFromConfig = ConfigFactory.getConfig("fs-fmcg-tpm-config").get("fix_red_packet_ids");
            String[] split = idsStrFromConfig.split(",");
            for (String s : split) {
                if (org.springframework.util.StringUtils.isEmpty(s)) {
                    continue;
                }
                idsResult.add(s.trim());
            }
        } else {
            String idsStr;
            try {
                File mainJsonFile = ResourceUtils.getFile("classpath:tpm/mn/red_packet_ids.txt");
                idsStr = new String(Files.readAllBytes(mainJsonFile.toPath()));
            } catch (Exception ex) {
                throw new RuntimeException("read red_packet_ids.txt error", ex);
            }

            String[] split = idsStr.split(",");
            for (String s : split) {
                if (org.springframework.util.StringUtils.isEmpty(s)) {
                    continue;
                }
                idsResult.add(s.trim());
            }
        }
        return idsResult;
    }

    private void addLayout(String tenantId) {
        try {

            PaasCreateLayout.Arg expenditureArg = new PaasCreateLayout.Arg();
            expenditureArg.setLayoutData(JSON.parseObject(layout("EnterpriseFundAccountAdjustmentObjExpenditure")));
            PaasCreateLayout.Result expenditureResult = paasLayoutProxy.createLayout(Integer.parseInt(tenantId), -10000, expenditureArg);
            if (expenditureResult.getCode() != 0) {
                log.error("expenditureResult error:{}", expenditureResult.getMessage());
            }

            PaasCreateLayout.Arg transferArg = new PaasCreateLayout.Arg();
            transferArg.setLayoutData(JSON.parseObject(layout("EnterpriseFundAccountAdjustmentObjTransfer")));
            PaasCreateLayout.Result transferResult = paasLayoutProxy.createLayout(Integer.parseInt(tenantId), -10000, transferArg);
            if (transferResult.getCode() != 0) {
                log.error("transferResult error:{}", transferResult.getMessage());
            }
        } catch (Exception ex) {
            log.error("init layout error", ex);
        }

    }

    private void addRecordType(String tenantId) {
        try {


            RecordType.Arg expenditureArg = buildRecordTypeArg(ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.EXPENDITURE_NAME, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.EXPENDITURE);
            RecordType.Result expenditureResult = recordTypeProxy.create(Integer.parseInt(tenantId), -10000, expenditureArg);
            if (expenditureResult.getCode() != 0) {
                log.error("expenditureArg error:{}", expenditureResult.getMessage());
            }

            RecordType.Arg transferArg = buildRecordTypeArg(ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.TRANSFER_NAME, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.TRANSFER);
            RecordType.Result transferResult = recordTypeProxy.create(Integer.parseInt(tenantId), -10000, transferArg);
            if (transferResult.getCode() != 0) {
                log.error("transferResult error:{}", transferResult.getMessage());
            }
        } catch (Exception ex) {
            log.error("addRecordType error", ex);
        }

    }

    private void updateRecordType(String tenantId) {
        try {


            RecordType.Arg revenueArg = buildRecordTypeArg(ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.REVENUE_NAME, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.REVENUE);
            RecordType.Result revenueResult = recordTypeProxy.update(Integer.parseInt(tenantId), -10000, revenueArg);
            if (revenueResult.getCode() != 0) {
                log.error("revenueArg error:{}", revenueResult.getMessage());
            }

        } catch (Exception ex) {
            log.error("addRecordType error", ex);
        }

    }

    private void assignLayout(String tenantId) {
        try {
            JSONArray defaultRolesArg = buildAssignLayoutRole(EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.REVENUE, EnterpriseFundAccountAdjustmentFields.LayoutApiName.REVENUE);
            recordTypeLogicService.saveLayoutAssign("detail", ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, defaultRolesArg.toJSONString(), User.systemUser(tenantId), "");
        } catch (Exception ex) {
            log.error("assignLayout error:{};{}", tenantId, EnterpriseFundAccountAdjustmentFields.LayoutApiName.REVENUE);
        }
        try {
            JSONArray expenditureRolesArg = buildAssignLayoutRole(EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.EXPENDITURE, EnterpriseFundAccountAdjustmentFields.LayoutApiName.EXPENDITURE);
            recordTypeLogicService.saveLayoutAssign("detail", ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, expenditureRolesArg.toJSONString(), User.systemUser(tenantId), "");

        } catch (Exception ex) {
            log.error("assignLayout error:{};{}", tenantId, EnterpriseFundAccountAdjustmentFields.LayoutApiName.EXPENDITURE);
        }

        try {
            JSONArray transferRolesArg = buildAssignLayoutRole(EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.TRANSFER, EnterpriseFundAccountAdjustmentFields.LayoutApiName.TRANSFER);
            recordTypeLogicService.saveLayoutAssign("detail", ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, transferRolesArg.toJSONString(), User.systemUser(tenantId), "");
        } catch (Exception ex) {
            log.error("assignLayout error:{};{}", tenantId, EnterpriseFundAccountAdjustmentFields.LayoutApiName.TRANSFER);
        }


    }

    private void assignRecordType(String tenantId) {
        try {
            assignRecordType(tenantId, ApiNames.ENTERPRISE_FUND_ACCOUNT_ADJUSTMENT_OBJ, "00000000000000000000000000000006", EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.REVENUE,
                    Lists.newArrayList(EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.TRANSFER, EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.EXPENDITURE,
                            EnterpriseFundAccountAdjustmentFields.RecordTypeApiName.REVENUE));
        } catch (Exception ex) {
            log.error("assignRecordType err:{}", tenantId, ex);
        }

    }

    private void assignRecordType(String tenantId, String apiName, String roleCode, String defaultRecordType, List<String> recordList) {
        PaasAssignRecord.Arg arg = new PaasAssignRecord.Arg();
        arg.setDescribeApiName(apiName);
        PaasAssignRecord.Role role = new PaasAssignRecord.Role();
        role.setRecords(recordList);
        role.setRoleCode(roleCode);
        role.setDefaultRecord(defaultRecordType);
        arg.setRoleList(JSON.toJSONString(com.google.common.collect.Lists.newArrayList(role)));
        PaasAssignRecord.Result result = paasLayoutProxy.assignRecord(Integer.parseInt(tenantId), -10000, arg);
        if (result.getErrCode() != 0) {
            log.info("assign err. arg:{},result:{}", arg, result);
        }
    }

    private JSONArray buildAssignLayoutRole(String recordApiName, String layoutApiName) {
        JSONArray roles = new JSONArray();
        for (String roleCode : ALL_ROLE_CODES) {
            JSONObject role = new JSONObject();
            role.put("roleCode", roleCode);
            JSONObject recordLayout = new JSONObject();
            recordLayout.put("record_api_name", recordApiName);
            recordLayout.put("layout_api_name", layoutApiName);
            JSONArray recordLayouts = new JSONArray();
            recordLayouts.add(recordLayout);
            role.put("record_layout", recordLayouts);
            roles.add(role);
        }

        return roles;
    }

    private RecordType.Arg buildRecordTypeArg(String apiName, String label, String recordTypeApiName) {
        RecordType.Arg recordTypeArg = new RecordType.Arg();
        recordTypeArg.setDescribeApiName(apiName);
        Map<String, Object> typeMap = Maps.newHashMap();
        typeMap.put("label", label);
        typeMap.put("api_name", recordTypeApiName);
        typeMap.put("is_active", true);
        typeMap.put("description", Objects.equals("default__c", recordTypeApiName) ? label : "");
        recordTypeArg.setRecordType(JSON.toJSONString(typeMap));
        return recordTypeArg;
    }

    private void updateFields(String tenantId) {
        Map<String, IObjectDescribe> describes = serviceFacade.findObjects(String.valueOf(tenantId), Lists.newArrayList(ApiNames.ENTERPRISE_FUND_ACCOUNT_OBJ, ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));
        try {
            IObjectDescribe enterpriseFundAccountObjDescribe = describes.get(ApiNames.ENTERPRISE_FUND_ACCOUNT_OBJ);
            for (IFieldDescribe fieldDescribe : enterpriseFundAccountObjDescribe.getFieldDescribes()) {
                if (EnterpriseFundAccountObjFields.BALANCE.equals(fieldDescribe.getApiName())) {

                    fieldDescribe.set("expression", "$base_amount$+$payment_total_amount$+$total_revenue_amount$-$pay_total_amount$-$total_expense_amount$");

                    serviceFacade.updateFieldDescribe(enterpriseFundAccountObjDescribe, com.beust.jcommander.internal.Lists.newArrayList(fieldDescribe));
                }
            }
        } catch (Exception ex) {
            log.error("updateFields ENTERPRISE_FUND_ACCOUNT_OBJ error,", ex);
        }
        try {

            IObjectDescribe accountsReceivableNoteObjDescribe = describes.get(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
            for (IFieldDescribe fieldDescribe : accountsReceivableNoteObjDescribe.getFieldDescribes()) {
                if (AccountsReceivableNoteFields.OBJECT_RECEIVABLE.equals(fieldDescribe.getApiName())) {

                    fieldDescribe.set("options", addObjectReceivableOptions((List<Map<String, String>>) fieldDescribe.get("options")));

                    serviceFacade.updateFieldDescribe(accountsReceivableNoteObjDescribe, com.beust.jcommander.internal.Lists.newArrayList(fieldDescribe));
                }
            }
        } catch (Exception ex) {
            log.error("updateFields ACCOUNTS_RECEIVABLE_NOTE_OBJ error,", ex);
        }
    }

    private List<Map<String, String>> addObjectReceivableOptions(List<Map<String, String>> options) {
        List<Map<String, String>> newOptions = com.beust.jcommander.internal.Lists.newArrayList(options);
        newOptions.add(buildOption("出库单", "OutboundDeliveryNoteObj"));//ignorei18n
        return newOptions;
    }

    private Map<String, String> buildOption(String label, String value) {
        Map<String, String> option = Maps.newHashMap();
        option.put("label", label);
        option.put("value", value);
        return option;
    }

    protected PaasDescribeCreate.Arg buildDescribeArg(String apiName) {
        PaasDescribeCreate.Arg arg = new PaasDescribeCreate.Arg();
        arg.setActive(true);
        arg.setIncludeLayout(true);

        try {
            arg.setJsonData(describe(apiName));
            arg.setJsonLayout(layout(apiName));
            arg.setJsonListLayout(listLayout(apiName));
        } catch (IOException ex) {
            throw new MetaDataBusinessException("init describe error,", ex);
        }

        arg.setLayoutType("detail");
        return arg;
    }

    private String describe(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%s.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    protected String layout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sDetailLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }


    private String listLayout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sMobileLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    public void updateIconSlot(int tenantId, String apiName) {
        // 创建新版图标
        String objApiNameAndIconSlotStr = ConfigFactory.getConfig("checkin-custom-config").get("preObjectIconSlotStr");
        Map<String, Integer> objApiNameAndIconSlotMap = JSONObject.parseObject(objApiNameAndIconSlotStr, new TypeReference<Map<String, Integer>>() {
        });
        if (org.apache.commons.collections.MapUtils.isNotEmpty(objApiNameAndIconSlotMap) && objApiNameAndIconSlotMap.containsKey(apiName)) {
            JSONObject extArg = new JSONObject();
            extArg.put("icon_slot", objApiNameAndIconSlotMap.get(apiName));
            UpdateDescribeExtra.Arg arg = new UpdateDescribeExtra.Arg();
            arg.setDescribeApiName(apiName);
            arg.setDescribeExtra(extArg);
            paasDescribeProxy.updateDescribeExtra(tenantId, -10000, arg);
        }
    }

    private List<IObjectData> queryArea(String tenantId, String userId) {
        // 查角色
        List<String> roleList = queryRole(tenantId, userId);
        IFilter userIdFilter = new Filter();
        if (roleList.contains("65deecea8f6eb50001ee8897")) {
            // 主任
            userIdFilter.setFieldName("field_department_head__c");
            userIdFilter.setOperator(Operator.EQ);
            userIdFilter.setFieldValues(Lists.newArrayList(userId));
        } else if (roleList.contains("65dee7468f6eb50001ee8764")) {
            // 战区总
            userIdFilter.setFieldName("field_warzone_total__c");
            userIdFilter.setOperator(Operator.EQ);
            userIdFilter.setFieldValues(Lists.newArrayList(userId));
        } else {
            userIdFilter.setFieldName("user_id__c");
            userIdFilter.setOperator(Operator.EQ);
            userIdFilter.setFieldValues(Lists.newArrayList(userId));
        }

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(userIdFilter);
        stq.setNeedReturnQuote(true);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                "user_work_area__c",
                stq,
                Lists.newArrayList(
                        "province__c",
                        "city__c",
                        "district__c",
                        "province_code__c",
                        "city_code__c",
                        "district_code__c"
                ));

    }

    private List<String> queryRole(String tenantId, String userId) {
        List<String> userRole = new ArrayList<>();
        try {
            userRole = serviceFacade.getUserRole(User.builder().tenantId(tenantId).userId(userId).build());
        } catch (Exception exception) {
            log.error("getUserRole error:", exception);
        }
        return userRole;
    }

    private void batchUpdateRedPacketStatus(User user, List<IObjectData> redPacketDataList, Long expireTime, String status) {
        for (IObjectData redPacketData : redPacketDataList) {
            redPacketData.set(RedPacketRecordObjFields.RED_PACKET_STATUS, status);
            redPacketData.set(RedPacketRecordObjFields.EXPIRATION_TIME, expireTime);
        }
        List<String> updateFields = Lists.newArrayList(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.EXPIRATION_TIME);
        serviceFacade.batchUpdateByFields(user, redPacketDataList, updateFields);
    }

    @Override
    public void fixBudgetAccountDetailTraceId(String tenantId, String id) {

        List<IObjectData> accountDetails = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(id), ApiNames.TPM_BUDGET_ACCOUNT_DETAIL);
        if (CollectionUtils.isEmpty(accountDetails)) {
            return;
        }
        IObjectData accountDetail = accountDetails.get(0);
        log.info("bizTraceId:{},approvalTraceId:{},accountDetail:{}", accountDetail.get(TPMBudgetAccountDetailFields.BIZ_TRACE_ID, String.class),
                accountDetail.get(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID, String.class),
                JSON.toJSONString(accountDetail));
        String relatedObjDataId = accountDetail.get(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID, String.class);
        String relatedObjApiName = accountDetail.get(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME, String.class);

        if (!Objects.equals(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, relatedObjApiName)) {
            return;
        }
        String traceId = relatedObjDataId.toUpperCase(Locale.ROOT);

        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(TPMBudgetAccountDetailFields.BIZ_TRACE_ID, traceId);
        updateMap.put(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID, traceId);
        serviceFacade.updateWithMap(User.systemUser(tenantId), accountDetail, updateMap);

    }

    @Override
    public void addObjFields(String tenantId, String objectApiName, String... fieldApiNames) {
        addFields(tenantId, objectApiName, fieldApiNames);
    }

    @Override
    public void disableDeleteObjFields(String tenantId, String objectApiName, String... fieldApiNames) {
        deleteFields(User.systemUser(tenantId), objectApiName, fieldApiNames);
    }

    private String read(String path) throws IOException {
        BufferedInputStream inputStream = new BufferedInputStream(this.getClass().getResourceAsStream(path));
        byte[] bytes = new byte[inputStream.available()];
        inputStream.read(bytes);
        inputStream.close();
        return new String(bytes, Charsets.UTF_8);
    }

    private List<IObjectData> queryNewDetails(String tenantId, String objDataId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(200);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setFindExplicitTotalNum(false);

        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(objDataId));

        stq.setFilters(Lists.newArrayList(masterIdFilter));

        return CommonUtils.queryData(
                this.serviceFacade,
                User.systemUser(tenantId),
                ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ,
                stq);
    }

    private List<IObjectData> queryExistsDetails(String tenantId, String objDataId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(200);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setFindExplicitTotalNum(false);

        Filter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(TPMBudgetDisassemblyNewDetailsFields.BUDGET_DISASSEMBLY_ID);
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(com.beust.jcommander.internal.Lists.newArrayList(objDataId));

        stq.setFilters(com.beust.jcommander.internal.Lists.newArrayList(masterIdFilter));

        return CommonUtils.queryData(
                this.serviceFacade,
                User.systemUser(tenantId),
                ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ,
                stq);
    }

    private void deleteFields(User superUser, String objectApiName, String... fieldApiNames) {
        for (String fieldApiName : fieldApiNames) {
            deleteField(superUser, objectApiName, fieldApiName);
        }
        clearDescribeCache(superUser.getTenantId(), objectApiName);
    }

    private void deleteField(User superUser, String objectApiName, String fieldApiName) {
        try {
            serviceFacade.disableField(superUser, objectApiName, fieldApiName);
            serviceFacade.deleteCustomField(superUser, objectApiName, fieldApiName);
        } catch (Exception ex) {
            log.error(String.format("delete field '%s' cause unknown exception : ", fieldApiName), ex);
        }
    }


    private void deleteDescribes(String tenantId, String... objectApiNames) {
        for (String objectApiName : objectApiNames) {
            deleteDescribe(tenantId, objectApiName);
            clearDescribeCache(tenantId, objectApiName);
        }
    }

    private void deleteDescribe(String tenantId, String objectApiName) {
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, objectApiName);
            CheckerResult disResult = objectDescribeService.disableDescribe(describe);
            CheckerResult result = objectDescribeService.delete(describe);
        } catch (Exception ex) {
            log.error(String.format("delete describe '%s' cause unknown exception : ", objectApiName), ex);
        }
    }

    private void addFields(String tenantId, String objectApiName, String... fieldApiNames) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectApiName);
        for (String fieldApiName : fieldApiNames) {
            addField(tenantId, describe, fieldApiName, true, "tpm/module_fields");
        }
        clearDescribeCache(tenantId, describe.getApiName());
    }

    private void addFields(String tenantId, Boolean show, String objectApiName, String... fieldApiNames) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectApiName);
        for (String fieldApiName : fieldApiNames) {
            addField(tenantId, describe, fieldApiName, show, "dms/field");
        }
        clearDescribeCache(tenantId, describe.getApiName());
    }

    private void addField(String tenantId, IObjectDescribe describe, String fieldApiName, Boolean show, String pathPrefix) {
        if (!describe.containsField(fieldApiName)) {
            addFieldFromFileResource(tenantId, describe, fieldApiName, show, pathPrefix);
        }
    }

    private void clearDescribeCache(String tenantId, String apiName) {
        NotifierClient.send("describe-extra-clear-room", String.format("%s_%s", tenantId, apiName));
    }

    private void initStoreConfirmButton(String tenantId) {
        String sqlTemplate = "insert into mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\",\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\",\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")values('%s','%s','%s','%s','[\"detail\"]','%s','','%s','[]',null,'common','',null,null,'-10000','-10000','t','f',1,'system',null,null,null,null,null);";
        List<String> buttons = queryAgreementButtons(tenantId);
        if (!buttons.contains(STORE_CONFIRM_BUTTON_API_NAME)) {
            specialTableMapper.setTenantId(tenantId).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, STORE_CONFIRM_BUTTON_API_NAME, "确认协议", "TPMActivityAgreementObj", "[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"connector\":\"AND\",\"isIndex\":false,\"fieldNum\":0,\"operator\":\"EQ\",\"isObjectReference\":false,\"field_name\":\"signing_mode\",\"field_values\":[\"agent_signing\"]},{\"value_type\":0,\"connector\":\"AND\",\"isIndex\":false,\"fieldNum\":0,\"operator\":\"EQ\",\"isObjectReference\":false,\"field_name\":\"store_confirm_status\",\"field_values\":[\"unconfirmed\"]},{\"value_type\":0,\"connector\":\"AND\",\"isIndex\":false,\"fieldNum\":0,\"operator\":\"EQ\",\"isObjectReference\":false,\"field_name\":\"life_status\",\"field_values\":[\"normal\"]}]}]"));
        }
    }

    private void addFieldFromFileResource(String tenantId, IObjectDescribe describe, String fieldApiName, Boolean show, String pathPrefix) {
        String fieldDescribeJson = loadFieldDescribeJsonFromResource(describe.getApiName(), fieldApiName, pathPrefix);
        doAddField(tenantId, describe, fieldDescribeJson, show);
    }

    private List<String> queryAgreementButtons(String tenantId) {
        String sql = "select api_name from mt_udef_button where tenant_id = '%s' and api_name ='AgreementStoreConfirm_button_default' and describe_api_name = 'TPMActivityAgreementObj'";
        List<Map> list = specialTableMapper.setTenantId(tenantId).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list.stream().map(m -> (String) m.get("api_name")).collect(Collectors.toList());
    }

    private String loadFieldDescribeJsonFromResource(String objectApiName, String fieldApiName, String pathPrefix) {
        if (StringUtils.isEmpty(pathPrefix)) {
            pathPrefix = "tpm/module_fields";
        }
        try {
            File file = ResourceUtils.getFile(String.format("classpath:" + pathPrefix + "/%s.%s.json", objectApiName, fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataBusinessException("read field describe from file cause io exception.");
        }
    }

    private void doAddField(String tenantId, IObjectDescribe describe, String fieldDescribe, Boolean show) {
        User superUser = User.systemUser(tenantId);

        JSONObject field = JSON.parseObject(fieldDescribe);

        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(superUser, DETAIL_LAYOUT_TYPE, describe.getApiName());

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
        fieldLayout.setRequired(false);
        fieldLayout.setShow(Objects.isNull(show) ? true : show);
        fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);

        serviceFacade.addDescribeCustomField(superUser,
                describe.getApiName(),
                fieldDescribe,
                Lists.newArrayList(fieldLayout),
                Lists.newArrayList());
    }

    @Override
    public void initOneMoreOrderModule(String tenantId) {
        // 查询是否已添加了"再来一单"按钮配置
        String querySql = "SELECT api_name FROM mt_udef_button WHERE tenant_id = '%s' AND api_name = 'OneMoreOrder_button_default' AND describe_api_name = '%s' LIMIT 1";
        List<Map> list = specialTableMapper.setTenantId(tenantId).findBySql(String.format(querySql, SqlEscaper.pg_escape(tenantId), "SalesOrderObj"));
        if (!CollectionUtils.isEmpty(list)) {
            throw new ValidateException("[one more order] button is already exist.");
        }

        // 给指定的企业刷指定的按钮
        String insertSql = "INSERT INTO mt_udef_button(\"id\",\"tenant_id\",\"api_name\",\"label\",\"use_pages\",\"describe_api_name\",\"description\",\"wheres\"," +
                "\"param_form\",\"actions\",\"button_type\",\"jump_url\",\"create_time\",\"last_modified_time\",\"created_by\",\"last_modified_by\",\"is_active\"," +
                "\"is_deleted\",\"version\",\"define_type\",\"is_batch\",\"url\",\"display\",\"redirect_type\",\"lock_data_show_button\")" +
                "VALUES('%s','%s','OneMoreOrder_button_default','再来一单','[\"detail\",\"list\"]','%s','',null,'[]',null,'common','',null,null,'-10000','-10000'," +
                "'t','f',1,'system',null,null,null,null,null);";
        specialTableMapper.setTenantId(tenantId).insertBySql(String.format(insertSql, IdGenerator.get(), tenantId, "SalesOrderObj"));
    }

}
