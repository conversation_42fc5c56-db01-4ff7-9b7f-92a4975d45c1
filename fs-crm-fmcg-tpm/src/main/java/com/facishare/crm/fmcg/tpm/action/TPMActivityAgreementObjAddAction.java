package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.checkins.api.model.SendDisplayConfirmRemindMessage;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityAgreementDTO;
import com.facishare.crm.fmcg.tpm.api.visit.ActivityAgreementImageDTO;
import com.facishare.crm.fmcg.tpm.api.visit.VisitAgreementActionDataDTO;
import com.facishare.crm.fmcg.tpm.business.CrmAuditLogService;
import com.facishare.crm.fmcg.tpm.business.DescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityAgreementConfigEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityAgreementTimeSettingEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.CheckinService;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.facishare.crm.fmcg.tpm.controller.TPMActivityAgreementObjRelatedListController.YINLU_FILTER_TIME;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 3:46 PM
 */

@SuppressWarnings("Duplicates,unused")
@Slf4j
public class TPMActivityAgreementObjAddAction extends StandardAddAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {

    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final ShopMMService shopMMService = SpringUtil.getContext().getBean(ShopMMService.class);
    private final CheckinService checkinService = SpringUtil.getContext().getBean(CheckinService.class);
    private final IActivityService activityService = SpringUtil.getContext().getBean(IActivityService.class);
    private final CrmAuditLogService crmAuditLogService = SpringUtil.getContext().getBean(CrmAuditLogService.class);
    private final IRangeFieldBusiness rangeFieldBusiness = SpringUtil.getContext().getBean(IRangeFieldBusiness.class);
    private final IActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(IActivityTypeManager.class);
    private final DescribeCacheService describeCacheService = SpringUtil.getContext().getBean(DescribeCacheService.class);
    private final ITPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(ITPMDisplayReportService.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);


    private final SimpleDateFormat validationMessageDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private final SimpleDateFormat notifyMessageDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private String activityId = null;
    private String storeId = null;

    private IObjectData store = null;
    private ActivityTypeExt activityType = null;
    private IObjectData activity = null;
    private String storeConfirmOuterTenantId = null;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        stopWatch.lap("super.before");

        this.sendActivityObjAuditLog(arg);
        stopWatch.lap("sendActivityObjAuditLog");

        this.validateCashingProduct();
        stopWatch.lap("validateCashingProduct");

        activityService.ifAllowCreateDataDueToOnceWriteOff(actionContext.getTenantId(), activity);
        stopWatch.lap("ifAllowCreateDataDueToOnceWriteOff");

        this.validateDealer();
        stopWatch.lap("validateDealer");

        this.yinluValidate();

        this.validateDisplayReport(arg);
        stopWatch.lap("validateDisplayReport");
    }

    private void validateDisplayReport(Arg arg) {
        try {
            tpmDisplayReportService.validateDisplayReport(arg);
        } catch (ValidateException ex) {
            throw ex;
        } catch (Exception e) {
            throw new ValidateException(e.getMessage() != null ? e.getMessage() : "System Validate Error.");
        }
    }

    private void yinluValidate() {
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            //创建时间大于 5.28号的才可以被创建协议
            Long activityCreateTime = activity.get(CommonFields.CREATE_TIME, Long.class);
            Long effectiveTime = Long.parseLong(YINLU_FILTER_TIME);
            if (activityCreateTime <= effectiveTime) {
                throw new ValidateException("agreement not allow");
            }
        }
    }

    private void validateDealer() {
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            return;
        }
        IObjectData data = arg.getObjectData().toObjectData();
        String dealerId = storeBusiness.findDealerId(actionContext.getTenantId(), store);
        String dataStoreId = data.get(TPMActivityAgreementFields.DEALER_ID, String.class);
        if ((Strings.isNullOrEmpty(dataStoreId) && !Strings.isNullOrEmpty(dealerId)) || (!Strings.isNullOrEmpty(dataStoreId) && Strings.isNullOrEmpty(dealerId))
                || !Strings.isNullOrEmpty(dataStoreId) && !dataStoreId.equals(dealerId)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ADD_ACTION_0));
        }
    }

    private void sendActivityObjAuditLog(Arg arg) {
        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(actionContext.getTenantId())
                .userId(String.valueOf(User.systemUser(actionContext.getTenantId())))
                .action("Add")
                .objectApiNames(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)
                .message("新建活动协议")//ignorei18n
                .parameters(JSON.toJSONString(arg))
                .build());
    }

    @Override
    protected void customProcessArg(Arg arg) {
        super.customProcessArg(arg);
        stopWatch.lap("super.customProcessArg");

        if (!TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(this.activityId)) {
            this.activityId = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID);
            this.activity = this.serviceFacade.findObjectDataIgnoreAll(User.systemUser(actionContext.getTenantId()), this.activityId, ApiNames.TPM_ACTIVITY_OBJ);
            String activityTypeId = this.activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
            this.activityType = activityTypeManager.find(actionContext.getTenantId(), activityTypeId);
        }

        this.storeId = (String) arg.getObjectData().get(TPMActivityAgreementFields.STORE_ID);
        if (storeId != null) {
            this.store = serviceFacade.findObjectData(actionContext.getUser(), storeId, ApiNames.ACCOUNT_OBJ);
        }
        stopWatch.lap("customInit");

        // 设置协议结束时间为结束日期当天的末尾
        this.processEndDateArg();
        stopWatch.lap("processEndDateArg");

        // 确认并设置协议状态字段
        this.processAgreementStatusArg();
        stopWatch.lap("processAgreementStatusArg");

        // 确认并设置协议确认状态字段
        this.processStoreConfirmArg();
        stopWatch.lap("processStoreConfirmArg");
    }

    private void validateCashingProduct() {
        String actualTotalAmountStr = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT);
        if (Strings.isNullOrEmpty(actualTotalAmountStr)) {
            actualTotalAmountStr = "0";
        }
        BigDecimal actualTotalAmount = new BigDecimal(actualTotalAmountStr);
        List<ObjectDataDocument> cashingProductDetails = arg.getDetails().get(ApiNames.TPM_ACTIVITY_AGREEMENT_CASHING_PRODUCT_OBJ);
        String agreementCashingType = (String) arg.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_CASHING_TYPE);
        if (Objects.equals(agreementCashingType, TPMActivityCashingProductFields.GOODS)) {
            // 860 协议兑付产品非必填
            if (CollectionUtils.isEmpty(cashingProductDetails)) {
                return;
            }
            BigDecimal totalPrice = new BigDecimal("0");
            for (ObjectDataDocument cashingProductDetail : cashingProductDetails) {
                BigDecimal price = new BigDecimal((String) cashingProductDetail.get(TPMActivityAgreementCashingProductFields.PRICE));
                BigDecimal quantity = new BigDecimal((String) cashingProductDetail.get(TPMActivityAgreementCashingProductFields.QUANTITY));
                totalPrice = totalPrice.add(price.multiply(quantity));
            }
            if (actualTotalAmount.compareTo(totalPrice) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ADD_ACTION_1));
            }
        }
    }

    /**
     * 设置协议结束时间为结束日期当天的末尾
     */
    private void processEndDateArg() {
        if (!TPMGrayUtils.notResetEndDate(actionContext.getTenantId())) {
            long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) this.arg.getObjectData().get(TPMActivityAgreementFields.END_DATE));
            this.arg.getObjectData().put(TPMActivityAgreementFields.END_DATE, end);
        }
    }

    /**
     * 确认并设置协议状态字段
     */
    private void processAgreementStatusArg() {
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            return;
        }
        long begin = (long) this.arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) this.arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            // 未生效
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
        } else if (now < end) {
            // 进行中
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
        } else {
            // 已结束
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
        }
        this.arg.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);
    }

    /**
     * 确认并设置协议确认状态字段
     * SIGNING_MODE__NORMAL         : 业代签订-无需门店确认
     * SIGNING_MODE__AGENT_SIGNING  : 业代签订-需门店确认
     */
    private void processStoreConfirmArg() {
        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(this.activityId)) {
            this.arg.getObjectData().put(TPMActivityAgreementFields.SIGNING_MODE, null);
            this.arg.getObjectData().put(TPMActivityAgreementFields.STORE_CONFIRM_STATUS, null);
            return;
        }
        String signingMode = Objects.isNull(this.activityType.agreementConfig()) ? TPMActivityAgreementFields.SIGNING_MODE__NORMAL : this.activityType.agreementConfig().getSigningMode();
        this.arg.getObjectData().put(TPMActivityAgreementFields.SIGNING_MODE, signingMode);
        if (TPMActivityAgreementFields.SIGNING_MODE__AGENT_SIGNING.equals(signingMode)) {
            this.arg.getObjectData().put(TPMActivityAgreementFields.STORE_CONFIRM_STATUS, TPMActivityAgreementFields.STORE_CONFIRM_STATUS__UNCONFIRMED);
        } else {
            this.arg.getObjectData().put(TPMActivityAgreementFields.STORE_CONFIRM_STATUS, null);
        }
    }

    @Override
    protected void validate() {

        super.validate();
        stopWatch.lap("frameworkValidate");

        // 校验协议开始以及结束时间是否合规
        this.validateAgreementDateSpan();
        stopWatch.lap("validateAgreementDateSpan");

        // 校验当前活动方案是否可以对当前门店签订协议
        this.validateActivity();
        // 元气的门店校验
        this.validateActivityForYuanQi();
        stopWatch.lap("validateActivity");

        // 校验是否有时间冲突的协议信息
        this.validateDuplicateAgreement();
        stopWatch.lap("validateDuplicateAgreement");
    }


    /**
     * 校验协议开始以及结束时间是否合规
     */
    private void validateAgreementDateSpan() {
        long begin = (long) this.arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) this.arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);
        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_AGREEMENT_DATE_ERROR));
        }
    }

    /**
     * 校验当前活动方案是否可以对当前门店签订协议
     */
    private void validateActivity() {
        // 校验当前门店是否在指定活动方案的范围内
        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(this.activityId)) {
            return;
        }

        this.validateActivityRange(activity);

        // 校验申请对象 是否存在开始结束时间
        if (validateActivityDateExists(activity)) {
            return;
        }

        // 校验协议时段和活动方案时段是否合规
        if (!GrayRelease.isAllow("fmcg", "YINLU_TPM", actionContext.getTenantId())) {
            validateActivityDateSpan(activity);
        } else {
            validateActivityDateSpanForYinLu(activity);
        }
    }

    private void validateActivityForYuanQi() {
        // 元气企业 默认业务类型 协议不校验。跳过
        String recordType = (String) this.arg.getObjectData().get(CommonFields.RECORD_TYPE);
        log.info("validateActivity agreement data recordType is {}", recordType);

        // 只有元气企业且非默认业务类型需要校验门店是否在活动范围内
        if (!TPMGrayUtils.isYuanQi(actionContext.getTenantId()) ||
            CommonFields.RECORD_TYPE__DEFAULT.equals(recordType)) {
            return;
        }

        String activityId = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID);
        if (Strings.isNullOrEmpty(activityId)) {
            return;
        }

        IObjectData activity = this.serviceFacade.findObjectDataIgnoreAll(
            User.systemUser(actionContext.getTenantId()),
            activityId,
            ApiNames.TPM_ACTIVITY_OBJ
        );
        this.validateActivityRangeForYuanQi(activity);
    }

    private void validateActivityRangeForYuanQi(IObjectData activity) {
        if (Objects.isNull(activity)){
            return;
        }

        String lifeStatus = activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ADD_ACTION_2));
        }

        String closedStatus = activity.get(TPMActivityFields.CLOSED_STATUS, String.class);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT));
        }

        String activityStatus = activity.get(TPMActivityFields.ACTIVITY_STATUS, String.class);
        if (TPMActivityFields.ACTIVITY_STATUS__CLOSED.equals(activityStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ADD_ACTION_3));
        }

        String dealerId = storeBusiness.findDealerId(actionContext.getTenantId(), store);
        if (dealerId == null && storeBusiness.findDealerRecordType(actionContext.getTenantId()).contains(store.getRecordType())) {
            dealerId = storeId;
        }
        Map<String, Boolean> validateResultMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(actionContext.getTenantId(), this.storeId, dealerId, Lists.newArrayList(activity), false, true);

        if (Boolean.FALSE.equals(validateResultMap.getOrDefault(activity.getId(), false))) {
            throw new ValidateException(I18N.text(I18NKeys.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT));
        }

    }

    private boolean validateActivityDateExists(IObjectData activity) {
        return activity.get(TPMActivityFields.BEGIN_DATE) == null && activity.get(TPMActivityFields.END_DATE) == null;
    }

    /**
     * 校验是否有时间冲突的协议信息
     */
    private void validateDuplicateAgreement() {
        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(this.activityId)) {
            return;
        }
        long agreementBegin = (long) this.arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long agreementEnd = (long) this.arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        SearchTemplateQuery query = new SearchTemplateQuery();

        // 同一个方案，同一个门店，限制拉最多100个协议取做校验，出问题了再说
        query.setLimit(100);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(this.activityId));

        Filter accountFilter = new Filter();
        accountFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(activityFilter, accountFilter));

        if (describeCacheService.isExistField(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.RIO_PROTOCOL_STATUS)) {
            Filter invalidFilter = new Filter();
            invalidFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
            invalidFilter.setOperator(Operator.NEQ);
            invalidFilter.setFieldValues(Lists.newArrayList("void"));
            query.getFilters().add(invalidFilter);

            Filter invalidEmptyFilter = new Filter();
            invalidEmptyFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
            invalidEmptyFilter.setOperator(Operator.IS);
            invalidEmptyFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(invalidEmptyFilter);
            query.setPattern("1 and 2 and (3 or 4)");
        }

        // 查询当前活动方案下的所有协议，只查询 _id, begin_date, end_date 字段
        List<IObjectData> agreements = this.serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(actionContext.getTenantId()), actionContext.getRequestContext()).getContext(),
                ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ,
                query,
                Lists.newArrayList(CommonFields.ID, TPMActivityAgreementFields.BEGIN_DATE, TPMActivityAgreementFields.END_DATE)
        ).getData();

        if (!CollectionUtils.isEmpty(agreements)) {
            agreements.forEach(agreement -> {
                long begin = (long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE);
                long end = (long) agreement.get(TPMActivityAgreementFields.END_DATE);
                if (TimeUtils.isIntervalOverlap(begin, end, agreementBegin, agreementEnd)) {
                    throw new ValidateException(I18N.text(I18NKeys.TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR));
                }
            });
        }
    }

    /**
     * 校验当前门店是否在指定活动方案的范围内
     *
     * @param activity 活动方案
     */
    private void validateActivityRange(IObjectData activity) {
        if (Objects.isNull(activity)){
            return;
        }

        String lifeStatus = this.activity.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ADD_ACTION_2));
        }

        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT));
        }

        String activityStatus = (String) activity.get(TPMActivityFields.ACTIVITY_STATUS);
        if (TPMActivityFields.ACTIVITY_STATUS__CLOSED.equals(activityStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_ADD_ACTION_3));
        }

        if (Objects.isNull(this.activityType.agreementNode())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_IS_NOT_AGREEMENT_ACTIVITY_CAN_NOT_CREATE_AGREEMENT));
        }

        String dealerId = storeBusiness.findDealerId(actionContext.getTenantId(), store);
        if (dealerId == null && storeBusiness.findDealerRecordType(actionContext.getTenantId()).contains(store.getRecordType())) {
            dealerId = storeId;
        }
        Map<String, Boolean> validateResultMap = rangeFieldBusiness.judgeStoreInActivitiesStoreRange(actionContext.getTenantId(), this.storeId, dealerId, Lists.newArrayList(activity), false, true);

        if (Boolean.FALSE.equals(validateResultMap.getOrDefault(activity.getId(), false))) {
            throw new ValidateException(I18N.text(I18NKeys.THIS_STORE_WHICH_IS_NOT_IN_THE_RANGE_CAN_NOT_CREATE_AGREEMENT));
        }
    }

    /**
     * 校验协议时段和活动方案时段是否合规
     *
     * @param activity 活动方案
     */
    private void validateActivityDateSpan(IObjectData activity) {
        long activityBegin = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
        long activityEnd = activity.get(TPMActivityFields.END_DATE, Long.class);
        long agreementBegin = this.arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
        long agreementEnd = this.arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.END_DATE, Long.class);

        // 银鹭企业不走通用校验，有特殊的校验逻辑
        log.info("activity - begin time : {}, end time : {}", activityBegin, activityEnd);


        ActivityAgreementConfigEntity config = this.activityType.agreementConfig();

        // 基于协议结点设置，获取可签订协议的时间范围
        long agreementBeginRange = calculateBeginTimeByTimeSetting(config, activityBegin, activityEnd, activityBegin);
        long agreementEndRange = calculateEndTimeByTimeSetting(config, activityBegin, activityEnd, activityEnd);

        long now = System.currentTimeMillis();

        // 校验当前时间是否在可签订协议的时间范围内
        if (now > agreementEndRange || now < agreementBeginRange) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.CURRENT_TIME_IS_NOT_FIT_THE_AGREEMENT_TIME_RANGE),
                    validationMessageDateFormat.format(new Date(agreementBeginRange)),
                    validationMessageDateFormat.format(new Date(agreementEndRange))));
        }

        // 校验协议的时段范围是否在活动方案的时段范围内
        if (agreementBegin < activityBegin || agreementEnd > activityEnd) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_TIME_OUT_OF_RANGE_ERROR));
        }
    }

    /**
     * 校验协议时段和活动方案时段是否合规
     *
     * @param activity 活动方案
     */
    private void validateActivityDateSpanForYinLu(IObjectData activity) {
        long activityBegin = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
        long activityEnd = activity.get(TPMActivityFields.END_DATE, Long.class);
        long agreementBegin = this.arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
        long agreementEnd = this.arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.END_DATE, Long.class);

        long now = System.currentTimeMillis();

        String activityTimeSpanPlanId = activity.get("activity_time_span_plan_id__c", String.class);
        if (Strings.isNullOrEmpty(activityTimeSpanPlanId)) {
            return;
        }

        IObjectData activityTimeSpanPlan = this.serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityTimeSpanPlanId, "tpm_activity_time_span_plan__c");

        long activityAgreementBegin = (long) activityTimeSpanPlan.get(TPMActivityFields.YINLU_AGREEMENT_BEGIN_DATE);
        long activityAgreementEnd = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) activityTimeSpanPlan.get(TPMActivityFields.YINLU_AGREEMENT_END_DATE));

        if (now > activityAgreementEnd || now < activityAgreementBegin) {
            throw new ValidateException(I18N.text(I18NKeys.CURRENT_TIME_IS_NOT_FIT_THE_AGREEMENT_TIME));
        }
        long begin = Math.min(activityAgreementBegin, activityBegin);
        long end = Math.max(activityAgreementEnd, activityEnd);

        if (agreementBegin < begin || agreementEnd > end) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_TIME_OUT_OF_RANGE_ERROR));
        }
    }

    private long calculateBeginTimeByTimeSetting(ActivityAgreementConfigEntity config, long begin, long end, long defaultValue) {
        if (config == null) {
            return defaultValue;
        }
        return calculateByTimeSetting(config.getBeginTimeSetting(), begin, end, defaultValue);
    }

    private long calculateEndTimeByTimeSetting(ActivityAgreementConfigEntity config, long begin, long end, long defaultValue) {
        if (config == null) {
            return defaultValue;
        }
        return calculateByTimeSetting(config.getEndTimeSetting(), begin, end, defaultValue);
    }

    private long calculateByTimeSetting(ActivityAgreementTimeSettingEntity setting, long begin, long end, long defaultValue) {
        if (Objects.isNull(setting) || Strings.isNullOrEmpty(setting.getType())) {
            return defaultValue;
        }
        switch (setting.getType()) {
            case "before_begin_date":
                return begin - setting.getValue();
            case "after_begin_date":
                return begin + setting.getValue();
            case "before_end_date":
                return end - setting.getValue();
            case "after_end_date":
                return end + setting.getValue();
            default:
                return defaultValue;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {

        super.after(arg, result);
        stopWatch.lap("frameworkAfter");

        // 如果是需要门店确认的协议，需要将门店对应的外部企业加到协议的相关团队内
        addStoreTeamMember(result);
        stopWatch.lap("addStoreTeamMember");

        // 如果协议上挂有审批流，且协议是进行中的状态，需要拨正为未生效状态
        setAgreementStatus(result);
        stopWatch.lap("setAgreementStatus");

        // 异步更新门店标签 - 巡店：李海生
        asyncUpdateStoreLabel(result);
        stopWatch.lap("asyncUpdateStoreLabel");

        // 如果是需要门店确认的协议，需要通过外勤服务向微信公众号发送通知
        asyncSendStoreAgreementConfirmNotify(result);
        stopWatch.lap("asyncSendStoreAgreementConfirmNotify");

        // 更新外勤动作快照 - 巡店
        asyncSaveVisitAction(result);
        stopWatch.lap("asyncSaveVisitAction");

        try {
            asyncAddTpmLog(arg);
        } catch (Exception e) {
            log.warn("活动协议埋点异常 tenantId:{}", actionContext.getTenantId(), e);
        }

        return result;
    }

    private void asyncAddTpmLog(Arg arg) {
        String agreementCashingType = (String) arg.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_CASHING_TYPE);
        if (Objects.equals(agreementCashingType, TPMActivityCashingProductFields.GOODS)) {
            String subModule = BuryModule.TPM.TPM_ACTIVITY_AGREEMENT + "_" + BuryModule.CASH_TYPE.CASH;
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), subModule, BuryOperation.CREATE, true);
        } else {
            BuryService.asyncTpmLog(Integer.valueOf(actionContext.getTenantId()), Integer.valueOf(actionContext.getUser().getUserIdOrOutUserIdIfOutUser()), BuryModule.TPM.TPM_ACTIVITY_AGREEMENT, BuryOperation.CREATE, true);
        }

        BigDecimal actualTotalAmount = arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
        BuryService.asyncTpmAmountLog(Integer.valueOf(actionContext.getTenantId()), actionContext.getUser().getUserIdInt(), BuryModule.TPM_AMOUNT.ACTUAL_TOTAL_AMOUNT, BuryOperation.CREATE, actualTotalAmount.doubleValue());
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        this.stopWatch.logSlow(500);
    }

    /**
     * 如果是需要门店确认的协议，需要将门店对应的外部企业加到协议的相关团队内
     *
     * @param result result
     */
    private void addStoreTeamMember(Result result) {
        String signingMode = (String) result.getObjectData().get(TPMActivityAgreementFields.SIGNING_MODE);
        if (!Strings.isNullOrEmpty(signingMode) && signingMode.equals(TPMActivityAgreementFields.SIGNING_MODE__AGENT_SIGNING) && !Strings.isNullOrEmpty(storeId)) {
            String outerTenantId = findOuterTenantIdByAccountId(this.storeId);
            if (!Strings.isNullOrEmpty(outerTenantId)) {
                storeConfirmOuterTenantId = outerTenantId;
                ObjectDataExt agreementExt = ObjectDataExt.of(result.getObjectData());
                TeamMember outerTeamMember = new TeamMember(outerTenantId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY, outerTenantId, TeamMember.MemberType.OUT_TENANT, null);
                agreementExt.addTeamMembers(Lists.newArrayList(outerTeamMember));
                this.serviceFacade.batchUpdateRelevantTeam(User.systemUser(actionContext.getTenantId()), Lists.newArrayList(agreementExt.getObjectData()), true);
            }
        }
    }

    private String findOuterTenantIdByAccountId(String accountId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);

        Filter accountIdFilter = new Filter();
        accountIdFilter.setFieldName("mapper_account_id");
        accountIdFilter.setOperator(Operator.EQ);
        accountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        query.setFilters(Lists.newArrayList(accountIdFilter));

        List<IObjectData> relations = serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(actionContext.getTenantId()), actionContext.getRequestContext()).getContext(),
                "EnterpriseRelationObj",
                query,
                Lists.newArrayList(CommonFields.ID)).getData();

        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }

        return relations.get(0).getId();
    }

    /**
     * 如果协议上挂有审批流，且协议是进行中的状态，需要拨正为未生效状态
     *
     * @param result result
     */
    private void setAgreementStatus(Result result) {
        String agreementStatus = (String) result.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_STATUS);

        if (isApprovalFlowStartSuccess(result.getObjectData().getId()) && TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {
            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
            this.serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updater);
            result.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
        }
    }

    /**
     * 异步更新门店标签 - 巡店：李海生
     *
     * @param result result
     */
    private void asyncUpdateStoreLabel(Result result) {
        String agreementStatus = (String) result.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_STATUS);
        ParallelUtils.createParallelTask().submit(() -> updateStoreLabel(actionContext.getTenantId(), this.storeId, agreementStatus)).run();
    }

    /**
     * 如果是需要门店确认的协议，需要通过外勤服务向微信公众号发送通知
     *
     * @param result result
     */
    private void asyncSendStoreAgreementConfirmNotify(Result result) {
        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(this.activityId)) {
            return;
        }
        String signingMode = (String) result.getObjectData().get(TPMActivityAgreementFields.SIGNING_MODE);

        // only store confirm mode need to send confirm notify
        if (Objects.nonNull(storeConfirmOuterTenantId) && !Strings.isNullOrEmpty(signingMode) && signingMode.equals(TPMActivityAgreementFields.SIGNING_MODE__AGENT_SIGNING)) {
            ParallelUtils.createParallelTask().submit(() -> sendStoreAgreementConfirmNotify(result.getObjectData(), this.activity, storeConfirmOuterTenantId)).run();
        }
    }

    /**
     * 更新外勤动作快照 - 巡店
     *
     * @param result result
     */
    private void asyncSaveVisitAction(Result result) {
        String visitId = (String) result.getObjectData().get(TPMActivityProofFields.VISIT_ID);
        String actionId = (String) result.getObjectData().get(TPMActivityProofFields.ACTION_ID);

        if (!Strings.isNullOrEmpty(visitId) && !Strings.isNullOrEmpty(actionId)) {
            ParallelUtils.createParallelTask().submit(() -> saveVisitAction(result)).run();
        }
    }

    private void updateStoreLabel(String tenantId, String storeId, String agreementStatus) {
        if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(agreementStatus)) {
            storeBusiness.updateStoreLabel(tenantId, Lists.newArrayList(storeId), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 0);
        } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {
            storeBusiness.updateStoreLabel(tenantId, Lists.newArrayList(storeId), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 1);
        }
    }

    private void sendStoreAgreementConfirmNotify(ObjectDataDocument agreement, IObjectData activity, String outerTenantId) {
        try {
            SendDisplayConfirmRemindMessage.Args notifyArg = new SendDisplayConfirmRemindMessage.Args();

            long beginDate = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
            long endDate = activity.get(TPMActivityFields.END_DATE, Long.class);
            long agreementBeginDate = agreement.toObjectData().get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            long agreementEndDate = agreement.toObjectData().get(TPMActivityAgreementFields.END_DATE, Long.class);

            long createTime = System.currentTimeMillis();

            notifyArg.setTenantId(actionContext.getTenantId());
            notifyArg.setDataId(agreement.getId());
            notifyArg.setActivityAgreementNo((String) agreement.get(TPMActivityAgreementFields.CODE));
            notifyArg.setActivityAgreementAmount((String) agreement.get(TPMActivityAgreementFields.TOTAL));
            notifyArg.setCreateTime(notifyMessageDateFormat.format(new Date(createTime)));
            notifyArg.setAgreementStartDate(validationMessageDateFormat.format(new Date(agreementBeginDate)));
            notifyArg.setAgreementEndDate(validationMessageDateFormat.format(new Date(agreementEndDate)));

            notifyArg.setActivityName(activity.getName());
            notifyArg.setActivityStartDate(validationMessageDateFormat.format(new Date(beginDate)));
            notifyArg.setActivityEndDate(validationMessageDateFormat.format(new Date(endDate)));

            notifyArg.setOutTenantId(Long.parseLong(outerTenantId));

            log.info("notify arg : {}", notifyArg);

            SendDisplayConfirmRemindMessage.Result notifyResult = shopMMService.sendDisplayConfirmRemindMessage(notifyArg);

            log.info("notify result : {}", notifyResult);
        } catch (Exception ex) {
            log.error("send store agreement confirm notify error : ", ex);
        }
    }

    private void saveVisitAction(Result result) {
        try {
            String visitId = (String) result.getObjectData().get(TPMActivityProofFields.VISIT_ID);
            String actionId = (String) result.getObjectData().get(TPMActivityProofFields.ACTION_ID);

            VisitAgreementActionDataDTO data = new VisitAgreementActionDataDTO();
            List<IObjectData> agreements = queryAgreementByVisitAction(visitId, actionId);
            data.setActivityAgreementDTOList(Lists.newArrayList());
            for (IObjectData agreement : agreements) {
                ActivityAgreementDTO dto = new ActivityAgreementDTO();
                dto.setAgreementId(agreement.getId());
                dto.setRemark((String) agreement.get(TPMActivityAgreementFields.DESCRIPTION));
                dto.setImages(JSON.parseArray(JSON.toJSONString(agreement.get(TPMActivityAgreementFields.AGREEMENT_IMAGES)), ActivityAgreementImageDTO.class));
                dto.setImagesTotalCount(CollectionUtils.isEmpty(dto.getImages()) ? 0 : dto.getImages().size());
                dto.setDetails(Lists.newArrayList());
                data.getActivityAgreementDTOList().add(dto);
            }
            data.setActivityAgreementListSize(data.getActivityAgreementDTOList().size());
            String updateActionResult = checkinService.updateAgreementAction(actionContext.getUser(), visitId, actionId, data);
            result.getObjectData().put("__update_action_result", updateActionResult);
        } catch (Exception ex) {
            log.error("save agreement visit action error : ", ex);
        }
    }

    private List<IObjectData> queryAgreementByVisitAction(String visitId, String actionId) {
        SearchTemplateQuery query = QueryDataUtil.minimumQuery();

        // 调研了下，动作外露超过6条基本就没啥可读性了，索性直接查 TOP 6
        query.setLimit(6);

        Filter visitFilter = new Filter();
        visitFilter.setFieldName(TPMActivityAgreementFields.VISIT_ID);
        visitFilter.setOperator(Operator.EQ);
        visitFilter.setFieldValues(Lists.newArrayList(visitId));

        Filter actionFilter = new Filter();
        actionFilter.setFieldName(TPMActivityAgreementFields.ACTION_ID);
        actionFilter.setOperator(Operator.EQ);
        actionFilter.setFieldValues(Lists.newArrayList(actionId));

        query.setFilters(Lists.newArrayList(visitFilter, actionFilter));

        return serviceFacade.findBySearchTemplateQueryWithFields(
                ActionContextExt.of(User.systemUser(actionContext.getTenantId()), actionContext.getRequestContext()).getContext(),
                ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID,
                        TPMActivityAgreementFields.ACTION_ID,
                        TPMActivityAgreementFields.VISIT_ID,
                        TPMActivityAgreementFields.AGREEMENT_IMAGES,
                        TPMActivityAgreementFields.DESCRIPTION
                )).getData();
    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        addProofPeriodTime(result);
        return result;
    }

    private void addProofPeriodTime(Result result) {
        tpmDisplayReportService.addProofPeriodTime(actionContext.getUser().getUpstreamOwnerIdOrUserId(), result);
    }
}
