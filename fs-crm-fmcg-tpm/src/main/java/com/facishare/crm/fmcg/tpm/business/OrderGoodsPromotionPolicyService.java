package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.business.abstraction.IActivityService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IOrderGoodsPromotionPolicyService;
import com.facishare.crm.fmcg.tpm.business.dto.RuleExecutionResult;
import com.facishare.crm.fmcg.tpm.business.dto.RuleWhere;
import com.facishare.crm.fmcg.tpm.business.enums.UseRangeEnum;
import com.facishare.crm.fmcg.tpm.common.constant.OrderGoodsContents;
import com.facishare.crm.fmcg.tpm.dao.mongo.OrderGoodsDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.OrderGoodsPromotionPolicyRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActiveStatusEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.OrderGoodsPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.OrderGoodsPromotionPolicyRulePO;
import com.facishare.crm.fmcg.tpm.service.TPMRoleService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.utils.lock.RedisDistributedLock;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.OrderGoodsPromotionPolicyRuleVO;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IPromotionPolicyService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.dto.auth.RoleViewForWebPojo;
import com.facishare.paas.auth.model.RolePojo;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PricebookConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/22 20:29
 */
@Slf4j
@Component
public class OrderGoodsPromotionPolicyService extends BaseService implements IOrderGoodsPromotionPolicyService, OrderGoodsBaseService {

    @Resource
    private IDescribeCacheService describeCacheService;
    @Resource
    private ButtonActionService buttonActionService;
    @Resource
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    @Resource
    private TPMRoleService tPMRoleService;
    @Resource
    private IPromotionPolicyService promotionPolicyService;
    @Resource
    private OrderGoodsDAO orderGoodsDAO;
    @Resource
    private OrderGoodsPromotionPolicyRuleDAO orderGoodsPromotionPolicyRuleDAO;
    @Resource
    private TPM2Service tpm2Service;
    @Resource
    private TPMTriggerActionService triggerActionService;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private IActivityService activityService;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    private static final List<String> INIT_FIELDS = Lists.newArrayList(
            "order_goods_meeting_flag",
            "promotion_type",
            "order_amount",
            "recharge_amount",
            "activity_rebate_amount",
            "pay_type",
            "receiving_into_account",
            "order_policy_dynamic_amount"
    );
    private static final String ORDER_GOODS_LOCK_KEY = "ORDER_GOODS_LOCK_%s_%s_%s";

    @Override
    public Boolean isOpenPromotionPolicy(String tenantId) {
        return promotionPolicyService.isOpenPromotionPolicy(tenantId);
    }

    @Override
    public Boolean isOpenRebateByObj(int tenantId, String objApiName) {
        PricebookConfig.Arg arg = new PricebookConfig.Arg();
        arg.setKeys(Lists.newArrayList("rebate_policy_source"));
        arg.setIsAllConfig(false);
        PricebookConfig.Result commonConfigs = paasDataProxy.getCommonConfigs(tenantId, -10000, arg);
        Integer code = commonConfigs.getCode();
        if (code != 0) {
            return false;
        }
        PricebookConfig.DataDTO data = commonConfigs.getData();
        if (Objects.isNull(data)) {
            return false;
        }

        Optional<JSONArray> first = data.getValues().stream().filter(v -> v.getString("key").equals("rebate_policy_source")).findFirst().map(v -> JSON.parseArray(v.getString("value")));
        Map<String, Boolean> apiUsedMap;
        if (first.isPresent()) {
            apiUsedMap = first.get().stream().map(k -> JSON.parseObject(k.toString())).collect(Collectors.toMap(k -> k.getString("api_name"), v -> v.getBoolean("used")));
            return apiUsedMap.getOrDefault(objApiName, false);
        }

        return false;
    }

    @Override
    public OrderGoodsInit.Result init(OrderGoodsInit.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();
        if (!Boolean.TRUE.equals(isOpenPromotionPolicy(tenantId))) {
            return OrderGoodsInit.Result.builder().msg("promotion policy not open").code("50011").build();
        }
        if (!isOpenRebateByObj(Integer.parseInt(tenantId), ApiNames.PAYMENT_OBJ)) {
            return OrderGoodsInit.Result.builder().msg("PaymentObj not open rebate policy").code("50011").build();
        }
        if (!tpm2Service.existSfaCarPreSaleLicenseCode(Integer.valueOf(tenantId), null)) {
            return OrderGoodsInit.Result.builder().msg("user_license_sfa_car_pre_sale_user_limit and dealer_edition license not buy").code("50011").build();
        }

        describeCacheService.createDefaultDescribe(tenantId, Lists.newArrayList(ApiNames.TPM_ACTIVITY_OBJ, ApiNames.TPM_ACTIVITY_STORE_OBJ));
        describeCacheService.initFields(tenantId, ApiNames.TPM_ACTIVITY_OBJ, INIT_FIELDS);
        describeCacheService.initFields(tenantId, ApiNames.PRICE_POLICY_OBJ, Lists.newArrayList("activity_id"));
        describeCacheService.initFields(tenantId, ApiNames.SALES_ORDER_OBJ, Lists.newArrayList("activity_id"));
        describeCacheService.initLayout(tenantId, ApiNames.TPM_ACTIVITY_OBJ, OrderGoodsContents.LAYOUT_NAME);
        describeCacheService.initLayout(tenantId, ApiNames.TPM_ACTIVITY_OBJ, OrderGoodsContents.EDIT_LAYOUT_NAME);
        initButton(tenantId);
        initRecordType(tenantId, ApiNames.TPM_ACTIVITY_OBJ, OrderGoodsContents.ORDER_GOODS_RECORD_TYPE, "订货会", OrderGoodsContents.LAYOUT_NAME);//ignorei18n
        describeCacheService.assignLayout(tenantId, "detail", ApiNames.TPM_ACTIVITY_OBJ, OrderGoodsContents.ORDER_GOODS_RECORD_TYPE, OrderGoodsContents.LAYOUT_NAME);
        describeCacheService.assignLayout(tenantId, "edit", ApiNames.TPM_ACTIVITY_OBJ, OrderGoodsContents.ORDER_GOODS_RECORD_TYPE, OrderGoodsContents.EDIT_LAYOUT_NAME);
        addPluginInstance(tenantId);
        return OrderGoodsInit.Result.builder().code("0").msg("success").build();
    }

    private void addPluginInstance(String tenantId) {
        try {
            promotionPolicyService.bindPluginInstance(tenantId, 1000, ApiNames.PRICE_POLICY_OBJ, "tpm_price_policy");
            promotionPolicyService.bindPluginInstance(tenantId, 1000, ApiNames.REBATE_POLICY_OBJ, "tpm_rebate_price_policy");
        } catch (Exception e) {
            log.info("addPluginInstance error", e);
        }
    }

    private void initRecordType(String tenantId, String objectApiName, String recordTypeApiName, String recordTypeLabel, String... layoutApiNames) {
        List<IRecordTypeOption> recordTypes = recordTypeLogicService.findRecordTypeOptionList(tenantId, objectApiName, false);
        boolean exists = recordTypes.stream().anyMatch(option -> option.getApiName().equals(recordTypeApiName));
        if (exists) {
            return;
        }

        RecordTypeRoleViewPojo recordType = new RecordTypeRoleViewPojo();
        recordType.setApi_name(recordTypeApiName);
        recordType.setIs_active(true);
        recordType.setLabel(recordTypeLabel);
        recordType.setDescription("");
        recordType.setConfig(Maps.newHashMap());
        recordType.setRoles(Lists.newArrayList());

        List<String> roles = Lists.newArrayList("marketingActivitiesManager", "00000000000000000000000000000006", "cityManager");
        List<RolePojo> existRoles = tPMRoleService.queryExistRole(tenantId, Sets.newHashSet(roles));
        if (CollectionUtils.isNotEmpty(existRoles)) {
            roles = roles.stream().filter(role -> existRoles.stream().anyMatch(existRole -> existRole.getRoleCode().equals(role))).collect(Collectors.toList());
        }

        for (String role : roles) {
            for (String layoutApiName : layoutApiNames) {
                RoleViewForWebPojo detailRole = new RoleViewForWebPojo();
                detailRole.setIs_default(false);
                detailRole.setIs_used(true);
                detailRole.setLayout_api_name(layoutApiName);
                detailRole.setRoleCode(role);
                recordType.getRoles().add(detailRole);
            }
        }

        RecordTypeResult createResult = recordTypeLogicService.createRecordType(tenantId, objectApiName, recordType, User.systemUser(tenantId));

        log.info("create record type result: {}", createResult);
    }

    private void initButton(String tenantId) {
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build();
        ServiceContext context = new ServiceContext(requestContext, null, null);

        String placeOrderButtonStr = "{\"describe_api_name\":\"TPMActivityObj\",\"api_name\":\"place_order__c\",\"label\":\"去订货\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"order_goods_meeting_flag\",\"field_values\":[\"true\"],\"operator\":\"EQ\",\"value_type\":0},{\"field_name\":\"promotion_type\",\"field_values\":[\"product_promotion\"],\"operator\":\"HASANYOF\",\"value_type\":0},{\"field_name\":\"life_status\",\"field_values\":[\"normal\"],\"operator\":\"EQ\",\"value_type\":0},{\"field_name\":\"activity_status\",\"field_values\":[\"in_progress\"],\"operator\":\"EQ\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";
        buttonActionService.createButtonByRole(context, placeOrderButtonStr);

        String goToRechargeButtonStr = "{\"describe_api_name\":\"TPMActivityObj\",\"api_name\":\"go_to_recharge__c\",\"label\":\"去充值\",\"define_type\":\"custom\",\"button_type\":\"common\",\"redirect_type\":\"\",\"description\":\"\",\"use_pages\":[\"detail\",\"list\"],\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"field_name\":\"order_goods_meeting_flag\",\"field_values\":[\"true\"],\"operator\":\"EQ\",\"value_type\":0},{\"field_name\":\"promotion_type\",\"field_values\":[\"recharge_promotion\"],\"operator\":\"HASANYOF\",\"value_type\":0},{\"field_name\":\"life_status\",\"field_values\":[\"normal\"],\"operator\":\"EQ\",\"value_type\":0},{\"field_name\":\"activity_status\",\"field_values\":[\"in_progress\"],\"operator\":\"EQ\",\"value_type\":0}]}],\"param_form\":[],\"jump_url\":\"\",\"is_active\":true,\"is_deleted\":false,\"lock_data_show_button\":false}";
        buttonActionService.createButtonByRole(context, goToRechargeButtonStr);
    }

    @Override
    public OrderGoodsPromotionPolicyEdit.Result enableEditOrderGoodsPromotionPolicy(OrderGoodsPromotionPolicyEdit.Arg arg) {
        List<IObjectData> quotes = queryObjByActivityId(arg.getTenantId(), arg.getObjectId(), ApiNames.QUOTE_OBJ);
        List<IObjectData> payments = queryObjByActivityId(arg.getTenantId(), arg.getObjectId(), ApiNames.PAYMENT_OBJ);
        if (CollectionUtils.isNotEmpty(quotes) || CollectionUtils.isNotEmpty(payments)) {
            return OrderGoodsPromotionPolicyEdit.Result.builder().enable(false).msg(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_33)).build();
        }
        return OrderGoodsPromotionPolicyEdit.Result.builder().enable(true).build();
    }

    private boolean getNeedEdit(OrderGoodsPromotionPolicyEdit.Arg arg) {
        if (StringUtils.isBlank(arg.getProductPromotionJson()) && StringUtils.isBlank(arg.getRechargePromotionJson())) {
            return false;
        }
        OrderGoodsPO orderGoodsPO = orderGoodsDAO.find(arg.getTenantId(), arg.getObjectId(), ApiNames.TPM_ACTIVITY_OBJ);
        if (Objects.isNull(orderGoodsPO)) {
            log.info("order goods not exist, objectId: {}", arg.getObjectId());
            return false;
        }

        String productPromotionJson = arg.getProductPromotionJson();
        if (StringUtils.isNotBlank(productPromotionJson)) {
            List<OrderGoodsPromotionPolicyRuleVO> orderGoodsPromotionPolicyRuleVOS = JSONArray.parseArray(productPromotionJson, OrderGoodsPromotionPolicyRuleVO.class);
            boolean existNewRule = orderGoodsPromotionPolicyRuleVOS.stream().anyMatch(rule -> StringUtils.isBlank(rule.getObjectId()));
            if (existNewRule) {
                return true;
            }

            List<OrderGoodsPromotionPolicyRulePO> promotionPolicyRule = orderGoodsPromotionPolicyRuleDAO.queryByParentObjectId(arg.getTenantId(), arg.getObjectId());
            if (CollectionUtils.isEmpty(promotionPolicyRule) || promotionPolicyRule.size() != orderGoodsPromotionPolicyRuleVOS.size()) {
                return true;
            }

            if (promotionPolicyRule.stream().anyMatch(rule -> orderGoodsPromotionPolicyRuleVOS.stream().anyMatch(vo -> !Objects.equals(rule.getProductGiftData(), vo.getProductGiftData())))) {
                return true;
            }
        }

        String rechargePromotionJson = arg.getRechargePromotionJson();
        if (StringUtils.isNotBlank(rechargePromotionJson)) {
            String rechargePromotionJsonPo = orderGoodsPO.getRechargePromotionJson();
            return !Objects.equals(rechargePromotionJsonPo, rechargePromotionJson);
        }
        return false;
    }

    private List<IObjectData> queryObjByActivityId(String tenantId, String activityId, String objApiName) {

        //paas  bug， 字段不存在会返回列表数据
        boolean existField = describeCacheService.isExistField(tenantId, objApiName, TPMActivityFields.ORDER_GOODS_MEETING_FLAG);
        if (!existField) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(200);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(PaymentFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter orderGoodsMeetingFlagFilter = new Filter();
        orderGoodsMeetingFlagFilter.setFieldName(TPMActivityFields.ORDER_GOODS_MEETING_FLAG);
        orderGoodsMeetingFlagFilter.setOperator(Operator.EQ);
        orderGoodsMeetingFlagFilter.setFieldValues(Lists.newArrayList("true"));

        stq.setFilters(Lists.newArrayList(activityFilter, orderGoodsMeetingFlagFilter));

        List<IObjectData> detailDataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                User.systemUser(tenantId),
                objApiName,
                stq,
                Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.TENANT_ID)).getData();

        if (CollectionUtils.isEmpty(detailDataList)) {
            return Lists.newArrayList();
        }
        return detailDataList;
    }

    @Override
    public OrderGoodsPromotionPolicyGet.Result get(OrderGoodsPromotionPolicyGet.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (StringUtils.isBlank(arg.getObjectId())) {
            return OrderGoodsPromotionPolicyGet.Result.builder().build();
        }

        String objectApiName = arg.getObjectApiName();
        if (StringUtils.isNotBlank(objectApiName)) {
            objectApiName = ApiNames.TPM_ACTIVITY_OBJ;
        }

        OrderGoodsPO orderGoodsPO = orderGoodsDAO.find(context.getTenantId(), arg.getObjectId(), objectApiName);
        if (Objects.isNull(orderGoodsPO)) {
            return OrderGoodsPromotionPolicyGet.Result.builder().build();
        }

        String rechargePromotionJson = orderGoodsPO.getRechargePromotionJson();
        List<OrderGoodsPromotionPolicyRulePO> orderGoodsPromotionPolicyRulePOS = orderGoodsPromotionPolicyRuleDAO.queryByParentObjectId(context.getTenantId(), arg.getObjectId());
        List<OrderGoodsPromotionPolicyRuleVO> orderGoodsPromotionPolicyRuleVOS = orderGoodsPromotionPolicyRulePOS.stream().map(OrderGoodsPromotionPolicyRulePO::poToVo).collect(Collectors.toList());
        String productPromotionJson = CollectionUtils.isNotEmpty(orderGoodsPromotionPolicyRuleVOS) ? JSON.toJSONString(orderGoodsPromotionPolicyRuleVOS) : "";
        //审批中获取 _copy 字段值
        IObjectData activity = serviceFacade.findObjectDataByIds(context.getTenantId(), Lists.newArrayList(arg.getObjectId()), objectApiName).get(0);
        if (Objects.nonNull(activity)) {
            String lifeStatus = activity.get(CommonFields.LIFE_STATUS, String.class);
            List<String> showCopyDataLifeStatusList = Lists.newArrayList(CommonFields.LIFE_STATUS__UNDER_REVIEW, CommonFields.LIFE_STATUS__INEFFECTIVE, CommonFields.LIFE_STATUS__IN_CHANGE);
            if (showCopyDataLifeStatusList.contains(lifeStatus)) {
                rechargePromotionJson = orderGoodsPO.getRechargePromotionJsonCopy();
                productPromotionJson = orderGoodsPO.getProductPromotionJsonCopy();
            }
        }
        if (StringUtils.isNotBlank(rechargePromotionJson)) {
            JSONObject rechargePromotion = JSON.parseObject(rechargePromotionJson);
            JSONArray ruleCondition = rechargePromotion.getJSONArray("rule_condition");
            //排序
            JSONArray sortedRuleCondition = getAscSortRuleCondition(ruleCondition);
            rechargePromotion.put("rule_condition", sortedRuleCondition);
            rechargePromotionJson = rechargePromotion.toJSONString();
        }
        return OrderGoodsPromotionPolicyGet.Result.builder()
                .objectApiName(orderGoodsPO.getObjectApiName())
                .objectId(orderGoodsPO.getObjectId())
                .productPromotionJson(StringUtils.isNotBlank(productPromotionJson) ? productPromotionJson : "")
                .rechargePromotionJson(StringUtils.isNotBlank(rechargePromotionJson) ? rechargePromotionJson : "")
                .build();
    }

    @Override
    public void saveOrUpdateOrderGoodsPromotionPolicyForApproval(String tenantId, String userId, IObjectData toObjectData, String productPromotionJson, String rechargePromotionJson) {
        OrderGoodsPO orderGoods = orderGoodsDAO.find(tenantId, toObjectData.getId(), toObjectData.getDescribeApiName());

        if (Objects.isNull(orderGoods)) {
            OrderGoodsPO orderGoodsPO = new OrderGoodsPO();
            orderGoodsPO.setObjectId(toObjectData.getId());
            orderGoodsPO.setObjectApiName(toObjectData.getDescribeApiName());
            orderGoodsPO.setVersion(0);
            orderGoodsPO.setStatus("normal");
            orderGoodsPO.setRechargePromotionJson(rechargePromotionJson);
            orderGoodsPO.setProductPromotionJsonCopy(productPromotionJson);
            orderGoodsPO.setRechargePromotionJsonCopy(rechargePromotionJson);
            orderGoodsDAO.add(tenantId, Integer.parseInt(userId), orderGoodsPO);
        } else {
            orderGoods.setProductPromotionJsonCopy(productPromotionJson);
            orderGoods.setRechargePromotionJsonCopy(rechargePromotionJson);
            orderGoodsDAO.edit(tenantId, Integer.parseInt(userId), orderGoods.getId().toString(), orderGoods);
        }
    }

    @Override
    public void createOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData, String productPromotionJson, String rechargePromotionJson) {
        log.info("createOrderGoodsSFAData. tenantId = {} , userId = {} , objectId = {} , productPromotionJson = {} , rechargePromotionJson = {}", tenantId, userId, objectData.getId(), productPromotionJson, rechargePromotionJson);

        String lockKey = getOrderGoodsLockKey("Add", tenantId, objectData.getId());
        String uuid = UUID.randomUUID().toString();
        if (redisDistributedLock.tryLock(lockKey, uuid)) {
            try {
                List<OrderGoodsPromotionPolicyRuleVO> orderGoodsPromotionPolicyRuleVOS = Lists.newArrayList();
                if (StringUtils.isNotBlank(productPromotionJson)) {
                    log.info("createOrderGoodsSFAData productPromotionJson : {}", productPromotionJson);
                    orderGoodsPromotionPolicyRuleVOS = JSONArray.parseArray(productPromotionJson, OrderGoodsPromotionPolicyRuleVO.class);
                    orderGoodsPromotionPolicyRuleVOS = orderGoodsPromotionPolicyRuleVOS.stream().filter(rule -> StringUtils.isNotBlank(rule.getProductGiftData())).collect(Collectors.toList());
                }

                OrderGoodsPO orderGoods = orderGoodsDAO.find(tenantId, objectData.getId(), objectData.getDescribeApiName());
                if (Objects.isNull(orderGoods)) {
                    log.info("createOrderGoodsSFAData orderGoods is null");
                    return;
                }

                createOrderGoodsSFAPromotionPolicyData(tenantId, userId, objectData, orderGoods.getId().toString(), orderGoodsPromotionPolicyRuleVOS);
                createOrderGoodsSFARebatePolicyData(tenantId, userId, objectData, orderGoods);
            } finally {
                redisDistributedLock.unlock(lockKey, uuid);
            }
        }
    }

    private String getOrderGoodsLockKey(String action, String tenantId, String id) {
        return String.format(ORDER_GOODS_LOCK_KEY, action, tenantId, id);
    }

    public void createOrderGoodsSFARebatePolicyData(String tenantId, String userId, IObjectData objectData, OrderGoodsPO orderGoods) {
        log.info("createOrderGoodsSFARebatePolicyData. tenantId = {} , userId = {} , objectId = {} , orderGoods = {}", tenantId, userId, objectData.getId(), orderGoods);
        String rechargePromotionJson = orderGoods.getRechargePromotionJson();
        if (StringUtils.isBlank(rechargePromotionJson)) {
            return;
        }

        JSONObject rechargePromotion = JSON.parseObject(rechargePromotionJson);
        JSONArray ruleCondition = rechargePromotion.getJSONArray("rule_condition");
        //排序
        JSONArray sortedRuleCondition = getSortRuleCondition(ruleCondition);
        if (CollectionUtils.isNotEmpty(sortedRuleCondition)) {
            String rebatePolicyId = createRebatePolicy(tenantId, userId, objectData, rechargePromotion, sortedRuleCondition, "create");
            updateOrderGoods(tenantId, userId, orderGoods, rechargePromotion, sortedRuleCondition, rebatePolicyId);
        }
    }

    public void updateOrderGoodsSFARebatePolicyData(String tenantId, String userId, String rebatePolicyId, IObjectData objectData, OrderGoodsPO orderGoods) {
        log.info("updateOrderGoodsSFARebatePolicyData. tenantId = {} , userId = {} , rebatePolicyId = {} , objectId = {} , orderGoods = {}", tenantId, userId, rebatePolicyId, objectData.getId(), orderGoods);
        String rechargePromotionJson = orderGoods.getRechargePromotionJson();
        if (StringUtils.isBlank(rechargePromotionJson)) {
            return;
        }

        JSONObject rechargePromotion = JSON.parseObject(rechargePromotionJson);
        JSONArray ruleCondition = rechargePromotion.getJSONArray("rule_condition");
        //排序
        JSONArray sortedRuleCondition = getSortRuleCondition(ruleCondition);
        if (CollectionUtils.isNotEmpty(sortedRuleCondition)) {
            updateRebatePolicy(tenantId, userId, rebatePolicyId, objectData, rechargePromotion, sortedRuleCondition, "Edit");
            updateOrderGoods(tenantId, userId, orderGoods, rechargePromotion, sortedRuleCondition, rebatePolicyId);
        }
    }

    private void updateOrderGoods(String tenantId, String userId, OrderGoodsPO orderGoods, JSONObject rechargePromotion, JSONArray sortedRuleCondition, String rebatePolicyId) {
        JSONArray addRuleCondition = new JSONArray();
        int priority = 1;
        for (int i = 0; i < sortedRuleCondition.size(); i++) {
            JSONObject condition = sortedRuleCondition.getJSONObject(i);
            condition.put("priority", priority);
            addRuleCondition.add(condition);
            priority++;
        }
        rechargePromotion.put("rule_condition", addRuleCondition);
        rechargePromotion.put("rebate_policy_id", rebatePolicyId);
        orderGoods.setRechargePromotionJson(rechargePromotion.toJSONString());
        orderGoodsDAO.edit(tenantId, Integer.parseInt(userId), orderGoods.getId().toString(), orderGoods);
    }

    public String createRebatePolicy(String tenantId, String userId, IObjectData objectData, JSONObject rechargePromotion, JSONArray sortedRuleCondition, String actionCode) {
        // 创建返利产生政策
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(ApiNames.REBATE_POLICY_OBJ)
                .objectData(buildRebatePolicyData(objectData, userId, "", actionCode))
                .detailApiName(ApiNames.REBATE_POLICY_RULE_OBJ)
                .details(buildRebatePolicyRuleData(tenantId, userId, objectData, "", rechargePromotion, sortedRuleCondition))
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
        if (Objects.isNull(result.getObjectData())) {
            return null;
        }
        return result.getObjectData().getId();
    }

    public String updateRebatePolicy(String tenantId, String userId, String rebatePolicyId, IObjectData objectData, JSONObject rechargePromotion, JSONArray sortedRuleCondition, String actionCode) {
        // 创建返利产生政策
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Edit")
                .apiName(ApiNames.REBATE_POLICY_OBJ)
                .objectData(buildRebatePolicyData(objectData, userId, rebatePolicyId, actionCode))
                .detailApiName(ApiNames.REBATE_POLICY_RULE_OBJ)
                .details(buildRebatePolicyRuleData(tenantId, userId, objectData, rebatePolicyId, rechargePromotion, sortedRuleCondition))
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
        if (Objects.isNull(result.getObjectData())) {
            return null;
        }
        return result.getObjectData().getId();
    }

    public List<IObjectData> buildRebatePolicyRuleData(String tenantId, String userId, IObjectData objectData, String rebatePolicyId, JSONObject rechargePromotion, JSONArray sortedRuleCondition) {

        RuleWhere.FiltersBean activityFilter = getActivityFilter(tenantId, objectData.getId());

        List<IObjectData> details = Lists.newArrayList();
        String calculateType = rechargePromotion.getString(RebatePolicyRuleFields.CALCULATE_TYPE);
        int priority = 1;
        for (int i = 0; i < sortedRuleCondition.size(); i++) {
            List<RuleWhere.FiltersBean> filters = Lists.newArrayList(activityFilter);

            JSONObject rule = sortedRuleCondition.getJSONObject(i);
            IObjectData detail = getDefaultRuleDetailObjectData(objectData, rebatePolicyId, priority, rule);

            RuleExecutionResult ruleExecutionResult = new RuleExecutionResult();
            if (RebatePolicyRuleFields.CALCULATE_TYPE_CYCLE.equals(calculateType)) {
                if (sortedRuleCondition.size() > 1) {
                    throw new ValidateException("calculate_type cycle ruleCondition size > 1");
                }
                RuleWhere.FiltersBean paymentAmountFilter = getCyclePaymentAmountFilter(rule);
                filters.add(paymentAmountFilter);
                buildCycleRuleExecutionResult(rule, ruleExecutionResult);
            }

            if (RebatePolicyRuleFields.CALCULATE_TYPE_EXPRESSION.equals(calculateType)) {
                if (sortedRuleCondition.size() < 1) {
                    throw new ValidateException("calculate_type cycle ruleCondition size < 1");
                }
                RuleWhere.FiltersBean paymentAmountFilter = getConstantPaymentAmountFilter(rule);
                filters.add(paymentAmountFilter);
                buildConstantExecutionResult(rule, ruleExecutionResult);
            }

            RuleWhere ruleWhere = new RuleWhere(filters);
            detail.set(RebatePolicyRuleFields.RULE_CONDITION, JSON.toJSONString(Lists.newArrayList(ruleWhere)));
            detail.set(RebatePolicyRuleFields.EXECUTION_RESULT, JSON.toJSONString(ruleExecutionResult));
            details.add(detail);
            priority += 1;
        }

        return details;
    }

    private RuleWhere.FiltersBean getActivityFilter(String tenantId, String id) {
        IObjectDescribe paymentDesc = serviceFacade.findObject(tenantId, ApiNames.PAYMENT_OBJ);
        return RuleWhere.FiltersBean.builder()
                .fieldName(RebatePolicyFields.ACTIVITY_ID)
                .operator(Operator.EQ.name())
                .fieldValues(Lists.newArrayList(id))
                .type(paymentDesc.getFieldDescribeMap().get(PaymentFields.ACTIVITY_ID).getType())
                .objectApiName(ApiNames.PAYMENT_OBJ)
                .fieldNameType("field")
                .fieldValueType("value")
                .build();
    }

    private RuleWhere.FiltersBean getCyclePaymentAmountFilter(JSONObject rule) {
        return RuleWhere.FiltersBean.builder()
                .fieldName("amount")
                .operator(Operator.GTE.name())
                .fieldValues(Lists.newArrayList(rule.getString("amount")))
                .objectApiName(ApiNames.PAYMENT_OBJ)
                .fieldNameType("field")
                .filterType("cycle")
                .build();
    }

    private RuleWhere.FiltersBean getConstantPaymentAmountFilter(JSONObject rule) {
        return RuleWhere.FiltersBean.builder()
                .fieldName("amount")
                .operator(Operator.GTE.name())
                .fieldValues(Lists.newArrayList(rule.getString("amount")))
                .type("currency")
                .objectApiName(ApiNames.PAYMENT_OBJ)
                .fieldNameType("field")
                .fieldValueType("value")
                .build();
    }

    private void buildConstantExecutionResult(JSONObject rule, RuleExecutionResult ruleExecutionResult) {
        RuleExecutionResult.ExpressionsBean expressionsBean = RuleExecutionResult.ExpressionsBean.builder()
                .executeType("CONSTANT")
                .right(rule.getString("used_amount"))
                .rowId(rule.getString("rowId"))
                .build();

        ruleExecutionResult.setCycleInfo(RuleExecutionResult.CycleInfo.builder().cycleData(Lists.newArrayList()).maxAmount("").build());
        ruleExecutionResult.setExpressions(Lists.newArrayList(expressionsBean));
        ruleExecutionResult.setCalculateType("EXPRESSION");
    }

    private void buildCycleRuleExecutionResult(JSONObject rule, RuleExecutionResult ruleExecutionResult) {
        RuleExecutionResult.LeftBean left = RuleExecutionResult.LeftBean.builder()
                .objectApiName(ApiNames.PAYMENT_OBJ)
                .fieldName("amount")
                .fieldNameType("field")
                .build();
        RuleExecutionResult.CycleDataBean cycleData = RuleExecutionResult.CycleDataBean.builder()
                .executeType("SOURCE_OBJECT")
                .left(left)
                .fieldValue(rule.getString("amount"))
                .usedAmount(rule.getString("used_amount"))
                .build();
        ruleExecutionResult.setCycleInfo(RuleExecutionResult.CycleInfo.builder().cycleData(Lists.newArrayList(cycleData)).maxAmount(rule.getString("max_amount")).build());
        ruleExecutionResult.setExpressions(Lists.newArrayList());
        ruleExecutionResult.setCalculateType("CYCLE");
    }

    @NotNull
    private IObjectData getDefaultRuleDetailObjectData(IObjectData objectData, String rebatePolicyId, int priority, JSONObject rule) {
        IObjectData detail = new ObjectData();
        String name = objectData.getName() + "-" + priority;
        detail.setName(name);
        String topicName = objectData.getName();
        if (StringUtils.isNotBlank(topicName) && topicName.length() > 20) {
            topicName = topicName.substring(0, 19);
        }

        detail.set(RebatePolicyRuleFields.TOPIC, topicName);
        detail.set(RebatePolicyRuleFields.USE_TYPE, RebatePolicyRuleFields.USE_TYPE_CASH);
        detail.set(RebatePolicyRuleFields.RULE_TYPE, RebatePolicyRuleFields.RULE_TYPE_MONEY);
        detail.set(RebatePolicyRuleFields.PRIORITY, priority);
        detail.set(RebatePolicyRuleFields.OBJECT_DESCRIBE_API_NAME, ApiNames.REBATE_POLICY_RULE_OBJ);
        detail.set(RebatePolicyRuleFields.REBATE_USED_DATE, "{\"type\":\"UNLIMITED\"}");
        detail.set(RebatePolicyRuleFields.PRODUCT_CONDITION_TYPE, "ALL");
        detail.set(RebatePolicyRuleFields.FUNCTION_INFO, "[]");
        detail.set(RebatePolicyRuleFields.SOURCE_OBJECT_API_NAME, ApiNames.PAYMENT_OBJ);
        detail.set("__isContinue", false);
        detail.set("__notShowDetail", false);
        detail.set("__isComplete", true);

        String id = (String) rule.getOrDefault("_id", "");
        if (StringUtils.isNotBlank(id)) {
            detail.setId(id);
        }
        if (StringUtils.isNotBlank(rebatePolicyId)) {
            detail.set(RebatePolicyRuleFields.REBATE_POLICY_ID, rebatePolicyId);
        }
        return detail;
    }

    private JSONArray getSortRuleCondition(JSONArray ruleCondition) {
        List<JSONObject> ruleConditionList = ruleCondition != null ?
                ruleCondition.toJavaList(JSONObject.class) :
                Collections.emptyList();

        List<JSONObject> sortedRuleConditionList = ruleConditionList.stream()
                .sorted((o1, o2) -> o2.getBigDecimal("amount").compareTo(o1.getBigDecimal("amount")))
                .collect(Collectors.toList());
        return JSONArray.parseArray(JSON.toJSONString(sortedRuleConditionList));
    }

    private JSONArray getAscSortRuleCondition(JSONArray ruleCondition) {
        List<JSONObject> ruleConditionList = ruleCondition != null ?
                ruleCondition.toJavaList(JSONObject.class) :
                Collections.emptyList();

        List<JSONObject> sortedRuleConditionList = ruleConditionList.stream()
                .sorted(Comparator.comparing(o -> o.getBigDecimal("amount")))
                .collect(Collectors.toList());
        return JSONArray.parseArray(JSON.toJSONString(sortedRuleConditionList));
    }

    private IObjectData buildRebatePolicyData(IObjectData toObjectData, String userId, String rebatePolicyId, String actionCode) {
        Long beginDate = toObjectData.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long endDate = toObjectData.get(TPMActivityFields.END_DATE, Long.class);
        List<String> owner = toObjectData.getOwner();

        IObjectData objectData = new ObjectData();
        objectData.setOwner(owner);
        objectData.setId(rebatePolicyId);
        objectData.setDescribeApiName(ApiNames.REBATE_POLICY_OBJ);
        objectData.setCreatedBy(userId);
        objectData.setDataOwnDepartment(toObjectData.getDataOwnDepartment());
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);

        objectData.set(RebatePolicyFields.NAME, toObjectData.get(TPMActivityFields.NAME, String.class));
        objectData.set(RebatePolicyFields.ACTIVITY_ID, toObjectData.getId());
        objectData.set(RebatePolicyFields.ORDER_GOODS_MEETING_FLAG, true);
        objectData.set(RebatePolicyFields.START_DATE, beginDate <= TimeUtils.MIN_DATE ? null : beginDate);
        objectData.set(RebatePolicyFields.END_DATE, endDate >= TimeUtils.MAX_DATE ? null : endDate);
        objectData.set(RebatePolicyFields.PRIORITY, "1");
        objectData.set(RebatePolicyFields.CALCULATE_RANGE, RebatePolicyFields.CALCULATE_RANGE_CURRENT);
        objectData.set(RebatePolicyFields.REBATE_BASIS, RebatePolicyFields.REBATE_BASIS_MASTER);
        objectData.set(RebatePolicyFields.REBATE_DIMENSION, "PaymentObj.account_id");
        objectData.set(RebatePolicyFields.EXECUTE_MODE, RebatePolicyFields.EXECUTE_MODE_EACH);

        // 申请的门店范围
        String storeRange = toObjectData.get(TPMActivityFields.STORE_RANGE, String.class);
        objectData.set(RebatePolicyFields.ACCOUNT_RANGE, buildRebatePolicyAccountRange(storeRange, toObjectData));

        // actionCode
        if ("create".equals(actionCode)) {
            objectData.set(RebatePolicyFields.ACTIVE_STATUS, RebatePolicyFields.ACTIVE_STATUS__ENABLE);
        }
        // 补充字段, 区分sfa和tpm 来源
        objectData.set(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);
        return objectData;
    }

    private void createOrderGoodsSFAPromotionPolicyData(String tenantId, String userId, IObjectData objectData, String orderGoodsId, List<OrderGoodsPromotionPolicyRuleVO> promotionPolicyRuleList) {
        if (CollectionUtils.isEmpty(promotionPolicyRuleList)) {
            log.info("createOrderGoodsSFAPromotionPolicyData: productPromotionJson is empty");
            return;
        }
        log.info("createOrderGoodsSFAPromotionPolicyData: {}", JSON.toJSONString(promotionPolicyRuleList.size()));
        AtomicLong order = gePricePolicyLastNameOrder(tenantId, objectData.getId());
        for (OrderGoodsPromotionPolicyRuleVO promotionPolicyRuleVO : promotionPolicyRuleList) {
            OrderGoodsPromotionPolicyRulePO promotionPolicyRule = OrderGoodsPromotionPolicyRulePO.voToPo(promotionPolicyRuleVO);
            String pricePolicyId = createPricePolicy(tenantId, userId, objectData, promotionPolicyRule.getProductGiftData(), "create", order.get());

            promotionPolicyRule.setObjectId(pricePolicyId);
            promotionPolicyRule.setObjectApiName(ApiNames.PRICE_POLICY_OBJ);
            promotionPolicyRule.setParentObjectId(objectData.getId());
            promotionPolicyRule.setVersion(promotionPolicyRuleVO.getVersion() + 1);
            promotionPolicyRule.setOrderGoodsId(orderGoodsId);
            promotionPolicyRule.setOrder(order.get());
            orderGoodsPromotionPolicyRuleDAO.add(tenantId, Integer.parseInt(userId), promotionPolicyRule);
            order.getAndIncrement();
        }
    }

    private String createPricePolicy(String tenantId,
                                     String userId,
                                     IObjectData toObjectData,
                                     String productGiftData,
                                     String actionCode,
                                     long order) {
        // 创建价格政策 And 价格政策客户从对象
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Add")
                .apiName(ApiNames.PRICE_POLICY_OBJ)
                .objectData(buildPromotionPolicyData(toObjectData, productGiftData, userId, "", actionCode, order))
                .detailApiName(ApiNames.PRICE_POLICY_ACCOUNT_OBJ)
                .details(buildPricePolicyAccountData(toObjectData, tenantId, userId))
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
        if (Objects.isNull(result.getObjectData())) {
            return null;
        }
        return result.getObjectData().getId();
    }

    private String editPricePolicy(String tenantId,
                                   String userId,
                                   String pricePolicyId,
                                   IObjectData toObjectData,
                                   String productGiftData,
                                   String actionCode) {
        // 创建价格政策 And 价格政策客户从对象
        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .actionName("Edit")
                .apiName(ApiNames.PRICE_POLICY_OBJ)
                .objectData(buildPromotionPolicyData(toObjectData, productGiftData, userId, pricePolicyId, actionCode, 0))
                .detailApiName(ApiNames.PRICE_POLICY_ACCOUNT_OBJ)
                .details(buildPricePolicyAccountData(toObjectData, tenantId, userId))
                .user(User.systemUser(tenantId))
                .triggerFlow(false)
                .triggerWorkflow(false)
                .build();

        BaseObjectSaveAction.Result result = triggerActionService.triggerAction(arg);
        if (Objects.isNull(result.getObjectData())) {
            return null;
        }
        return result.getObjectData().getId();
    }

    private List<IObjectData> buildPricePolicyAccountData(IObjectData activityData, String tenantId, String userId) {
        String storeRange = activityData.get(TPMActivityFields.STORE_RANGE, String.class);
        // 申请的门店范围
        JSONObject rangeObj = JSON.parseObject(storeRange);
        if (rangeObj.get("type") == null) {
            return Lists.newArrayList();
        }

        List<IObjectData> pricePolicyAccounts = Lists.newArrayList();
        if (rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.FIXED.value())) {
            List<IObjectData> detailList = queryActivityStoreByActivityId(tenantId, activityData.getId());
            for (IObjectData detail : detailList) {
                IObjectData objectData = getPricePolicyAccountData(activityData, userId, detail.get(TPMActivityStoreFields.STORE_ID, String.class));
                pricePolicyAccounts.add(objectData);
            }
        }
        return pricePolicyAccounts;
    }

    private List<IObjectData> queryActivityStoreByActivityId(String tenantId, String activityId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(2000);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityStoreFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        stq.setFilters(Lists.newArrayList(activityFilter));

        List<IObjectData> detailDataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                User.systemUser(tenantId),
                ApiNames.TPM_ACTIVITY_STORE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID, TPMActivityStoreFields.POLICY_LIMIT_AMOUNT, TPMActivityStoreFields.STORE_ID)).getData();

        if (CollectionUtils.isEmpty(detailDataList)) {
            return Lists.newArrayList();
        }
        return detailDataList;
    }

    @NotNull
    private IObjectData getPricePolicyAccountData(IObjectData toObjectData, String userId, String accountId) {
        IObjectData objectData = new ObjectData();

        objectData.setDescribeApiName(ApiNames.PRICE_POLICY_ACCOUNT_OBJ);
        objectData.setDataOwnDepartment(toObjectData.getDataOwnDepartment());
        objectData.setDataOwnOrganization(toObjectData.getDataOwnOrganization());
        objectData.setOutOwner(toObjectData.getOutOwner());
        objectData.setOwner(toObjectData.getOwner());
        objectData.setTenantId(toObjectData.getTenantId());
        objectData.setOutTenantId(toObjectData.getOutTenantId());
        objectData.setCreatedBy(userId);

        objectData.set(PricePolicyAccountFields.ACCOUNT_ID, accountId);
        objectData.set(PricePolicyAccountFields.APPLY_RANGE, UseRangeEnum.FIXED.value());
        return objectData;
    }

    private IObjectData buildPromotionPolicyData(IObjectData toObjectData,
                                                 String productGiftData,
                                                 String userId,
                                                 String pricePolicyId,
                                                 String actionCode,
                                                 long order) {
        Long beginDate = toObjectData.get(TPMActivityFields.BEGIN_DATE, Long.class);
        Long endDate = toObjectData.get(TPMActivityFields.END_DATE, Long.class);
        String name = toObjectData.get(TPMActivityFields.NAME, String.class);
        String modeTypeByData = toObjectData.get(TPMActivityFields.MODE_TYPE, String.class, "");
        List<String> owner = toObjectData.getOwner();
        JSONObject productGiftJson = JSONObject.parseObject(productGiftData);
        productGiftJson.put(PricePolicyFields.OBJECT_API_NAME, ApiNames.QUOTE_OBJ);
        String modeType = (String) CommonUtils.getOrDefault(productGiftJson.get(TPMActivityFields.MODE_TYPE), "");
        modeType = StringUtils.isNotBlank(modeType) ? modeType : modeTypeByData;
        // 申请的门店范围，
        String storeRange = toObjectData.get(TPMActivityFields.STORE_RANGE, String.class);
        IObjectData objectData = new ObjectData();
        objectData.setOwner(owner);
        // 政策id
        objectData.setId(pricePolicyId);
        objectData.set(TPMActivityFields.SOURCE_OBJECT_API_NAME, ApiNames.QUOTE_OBJ);
        objectData.setDescribeApiName(ApiNames.PRICE_POLICY_OBJ);
        objectData.setCreatedBy(userId);

        objectData.set(PricePolicyFields.ACTIVITY_ID, toObjectData.getId());
        objectData.set(PricePolicyFields.START_DATE, beginDate <= TimeUtils.MIN_DATE ? null : beginDate);
        objectData.set(PricePolicyFields.END_DATE, endDate >= TimeUtils.MAX_DATE ? null : endDate);

        if ("create".equals(actionCode)) {
            objectData.set(PricePolicyFields.NAME, getCreateName(name, order));
        }
        objectData.set(PricePolicyFields.PRODUCT_GIFT_DATA_JSON, productGiftJson);
        objectData.set(PricePolicyFields.ACCOUNT_RANGE, buildAccountRange(storeRange));
        objectData.set(PricePolicyFields.MODE_TYPE, modeType);
        objectData.set(PricePolicyFields.PRIORITY, "2");
        objectData.set(PricePolicyFields.ORDER_GOODS_MEETING_FLAG, true);

        if (modeType.startsWith("master")) {
            objectData.set(PricePolicyFields.MODIFY_TYPE, PricePolicyFields.MODIFY_TYPE__MASTER);
        } else if (modeType.startsWith("details")) {
            objectData.set(PricePolicyFields.MODIFY_TYPE, PricePolicyFields.MODIFY_TYPE__DETAIL);
        }
        // 计算状态
        objectData.set(PricePolicyFields.CALCULATE_STATUS, "0");
        // actionCode
        if ("create".equals(actionCode)) {
            objectData.set(PricePolicyFields.ACTIVE_STATUS, PricePolicyFields.ACTIVE_STATUS__ENABLE);
        }
        // 补充字段, 区分sfa和tpm 来源
        objectData.set(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);
        return objectData;
    }

    private String buildAccountRange(String storeRange) {
        JSONObject rangeObj = JSON.parseObject(storeRange);
        if (rangeObj.get("type") != null &&
                rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.CONDITION.value())) {
            String code = rangeObj.getString("code");
            if (!Strings.isNullOrEmpty(code)) {
                rangeObj.remove(code);
            }
        }

        if (rangeObj.get("type") != null &&
                rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.FIXED.value())) {
            String code = rangeObj.getString("code");
            if (!Strings.isNullOrEmpty(code)) {
                rangeObj.remove(code);
            }
        }
        return rangeObj.toJSONString();
    }

    private String buildRebatePolicyAccountRange(String storeRange, IObjectData toObjectData) {
        JSONObject rangeObj = JSON.parseObject(storeRange);
        if (rangeObj.get("type") != null &&
                rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.CONDITION.value())) {
            String code = rangeObj.getString("code");
            if (!Strings.isNullOrEmpty(code)) {
                rangeObj.remove(code);
            }
        }

        if (rangeObj.get("type") != null &&
                rangeObj.getString("type").toUpperCase().equals(UseRangeEnum.FIXED.value())) {
            String id = toObjectData.getId();
            List<IObjectData> detailList = queryActivityStoreByActivityId(toObjectData.getTenantId(), id);
            List<String> storeIds = detailList.stream().map(detail -> detail.get(TPMActivityStoreFields.STORE_ID, String.class)).collect(Collectors.toList());

            List<IObjectData> stores = serviceFacade.findObjectDataByIds(toObjectData.getTenantId(), storeIds, ApiNames.ACCOUNT_OBJ);
            Map<String, String> storeIdNameMap = stores.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));

            JSONArray value = new JSONArray();
            for (IObjectData detail : detailList) {
                String storeId = detail.get(TPMActivityStoreFields.STORE_ID, String.class);
                JSONObject accountValue = new JSONObject();
                accountValue.put("account_id", storeId);
                accountValue.put("account_id__r", storeIdNameMap.getOrDefault(storeId, ""));
                value.add(accountValue);
            }
            rangeObj.put("value", value.toJSONString());
        }

        return rangeObj.toJSONString();
    }

    @Override
    public void defaultValidateOrderGoods(ActivityTypePO activityTypePO, ActionContext actionContext, IObjectData getObjectData) {

        //判断web端请求 WEB.chrome
        String clientInfo = actionContext.getRequestContext().getClientInfo();
        if (Objects.isNull(clientInfo) || !clientInfo.startsWith("WEB")) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_27));
        }

        if (!getObjectData.get(TPMActivityFields.ORDER_GOODS_MEETING_FLAG, Boolean.class, false)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_28));
        }

        String productPromotionJson = getObjectData.get(TPMActivityFields.PRODUCT_PROMOTION_JSON, String.class, "");
        String rechargePromotionJson = getObjectData.get(TPMActivityFields.RECHARGE_PROMOTION_JSON, String.class, "");
        if (StringUtils.isBlank(productPromotionJson) && StringUtils.isBlank(rechargePromotionJson)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_EDIT_ACTION_34));
        }

        List<String> promotionType = getObjectData.get(TPMActivityFields.PROMOTION_TYPE, List.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(promotionType)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_29));
        }

        if (promotionType.contains(TPMActivityFields.PRODUCT_PROMOTION)) {
            if (StringUtils.isBlank(productPromotionJson)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_30));
            }

            List<OrderGoodsPromotionPolicyRuleVO> orderGoodsPromotionPolicyRuleVOS = JSONArray.parseArray(productPromotionJson, OrderGoodsPromotionPolicyRuleVO.class);
            orderGoodsPromotionPolicyRuleVOS = orderGoodsPromotionPolicyRuleVOS.stream().filter(rule -> StringUtils.isNotBlank(rule.getProductGiftData())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderGoodsPromotionPolicyRuleVOS)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_32));
            }

            if (orderGoodsPromotionPolicyRuleVOS.size() > 4) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_38));
            }

            for (OrderGoodsPromotionPolicyRuleVO rule : orderGoodsPromotionPolicyRuleVOS) {
                JSONObject productGiftJs = JSON.parseObject(rule.getProductGiftData());
                JSONArray groupings = productGiftJs.getJSONArray("groupings");
                if (CollectionUtils.isNotEmpty(groupings)) {
                    for (int i = 0; i < groupings.size(); i++) {
                        JSONObject group = groupings.getJSONObject(i);
                        JSONArray categoryProducts = group.getJSONObject("condition_category_product").getJSONArray("category_products");
                        if (CollectionUtils.isNotEmpty(categoryProducts)) {
                            if (categoryProducts.size() > 10) {
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_40));
                            }
                            Set<String> productSet = categoryProducts.stream().filter(categoryProduct -> {
                                JSONObject product = JSON.parseObject(JSON.toJSONString(categoryProduct));
                                return product.containsKey("category_product_id");
                            }).map(categoryProduct -> {
                                JSONObject product = JSON.parseObject(JSON.toJSONString(categoryProduct));
                                return product.getString("category_product_id");
                            }).collect(Collectors.toSet());
                            if (productSet.size() != categoryProducts.size()) {
                                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_41));
                            }
                        }
                    }
                }
            }
        }

        if (promotionType.contains(TPMActivityFields.RECHARGE_PROMOTION)) {
            if (StringUtils.isBlank(rechargePromotionJson)) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_31));
            }

            JSONObject rechargePromotion = JSON.parseObject(rechargePromotionJson);
            JSONArray ruleCondition = rechargePromotion.getJSONArray("rule_condition");
            if (ruleCondition.isEmpty()) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_32));
            }

            String calculateType = rechargePromotion.getString(RebatePolicyRuleFields.CALCULATE_TYPE);
            if (RebatePolicyRuleFields.CALCULATE_TYPE_CYCLE.equals(calculateType)) {
                if (ruleCondition.size() > 1) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_37));
                }
                JSONObject rule = ruleCondition.getJSONObject(0);
                BigDecimal amount = rule.getBigDecimal("amount");
                BigDecimal usedAmount = rule.getBigDecimal("used_amount");
                BigDecimal maxAmount = rule.getBigDecimal("max_amount");

                if (maxAmount.compareTo(usedAmount) < 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_33));
                }

                if (amount.compareTo(usedAmount) <= 0) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_34));
                }

            }

            if (RebatePolicyRuleFields.CALCULATE_TYPE_EXPRESSION.equals(calculateType)) {
                if (ruleCondition.size() < 1) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_35));
                }

                if (ruleCondition.size() > 5) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_36));
                }

                for (int r = 0; r < ruleCondition.size(); r++) {
                    JSONObject rule = ruleCondition.getJSONObject(r);
                    BigDecimal amount = rule.getBigDecimal("amount");
                    BigDecimal usedAmount = rule.getBigDecimal("used_amount");
                    if (usedAmount.compareTo(amount) > 0) {
                        throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_OBJ_ADD_ACTION_39));
                    }
                }
            }
        }

    }

    @Override
    public void disableOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData) {
        if (!judgedIsOrderGoodsPromotionData(objectData)) {
            return;
        }
        handleUpdateOrderGoodsActiveStatus(tenantId, userId, objectData.getId(), ActiveStatusEnum.DISABLE.code());
    }

    private void handleUpdateOrderGoodsActiveStatus(String tenantId, String userId, String activityId, String activeStatus) {
        log.info("disableOrderGoodsSFAData tenantId:{},userId:{},activityId:{},activeStatus:{}", tenantId, userId, activityId, activeStatus);
        OrderGoodsPO orderGoodsPO = orderGoodsDAO.find(tenantId, activityId, ApiNames.TPM_ACTIVITY_OBJ);
        List<IObjectData> orderGoodsObjDatas = Lists.newArrayList();
        List<IObjectData> pricePolicies = queryObjByActivityId(tenantId, activityId, ApiNames.PRICE_POLICY_OBJ);
        if (CollectionUtils.isNotEmpty(pricePolicies)) {
            List<OrderGoodsPromotionPolicyRulePO> orderGoodsPromotionPolicyRulePOS = orderGoodsPromotionPolicyRuleDAO.queryByParentObjectId(tenantId, activityId);
            pricePolicies = pricePolicies.stream().filter(pricePolicy ->
                    orderGoodsPromotionPolicyRulePOS.stream()
                            .map(OrderGoodsPromotionPolicyRulePO::getObjectId)
                            .collect(Collectors.toList())
                            .contains(pricePolicy.getId())
            ).collect(Collectors.toList());
            orderGoodsObjDatas.addAll(pricePolicies);
        }
        List<IObjectData> rebatePolicies = queryObjByActivityId(tenantId, activityId, ApiNames.REBATE_POLICY_OBJ);
        if (CollectionUtils.isNotEmpty(rebatePolicies) && (Objects.nonNull(orderGoodsPO) && StringUtils.isNotBlank(orderGoodsPO.getRechargePromotionJson()))) {
            orderGoodsObjDatas.addAll(rebatePolicies);
        }
        if (CollectionUtils.isNotEmpty(orderGoodsObjDatas)) {
            for (IObjectData orderGoodsObjData : orderGoodsObjDatas) {
                try {
                    if (activeStatus.equals(orderGoodsObjData.get("active_status", String.class, ""))) {
                        return;
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("active_status", activeStatus);
                    serviceFacade.updateWithMap(User.builder().userId(userId).tenantId(tenantId).build(), orderGoodsObjData, map);
                } catch (Exception exception) {
                    log.info("handleUpdateOrderGoodsActiveStatus  fail. activeStatus = {} ,dataId = {} , message = {}", activeStatus, orderGoodsObjData.getId(), exception.getMessage());
                }
            }
        }
    }

    private String getCreateName(String name, long order) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        if (order == 0) {
            return name;
        }
        String re = "-OGC_"; // orderGoodsCreate
        return name + re + order;
    }

    private AtomicLong gePricePolicyLastNameOrder(String tenantId, String activityId) {
        AtomicLong order = new AtomicLong(1);
        List<OrderGoodsPromotionPolicyRulePO> rulePOS = orderGoodsPromotionPolicyRuleDAO.queryAllByParentObjectId(tenantId, activityId);
        if (CollectionUtils.isNotEmpty(rulePOS)) {
            Optional.of(rulePOS.stream().max(Comparator.comparing(OrderGoodsPromotionPolicyRulePO::getOrder))).get().ifPresent(orderGoodsPromotionPolicyRulePO -> {
                order.set(orderGoodsPromotionPolicyRulePO.getOrder() + 1);
            });
        }
        log.info("gePricePolicyLastNameOrder. tenantId = {} , activityId = {} , order = {}", tenantId, activityId, order.get());
        return order;
    }

    @Override
    public void updateOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData, String productPromotionJson, String rechargePromotionJson) {
        log.info("updateOrderGoodsSFAData. tenantId = {} , userId = {} , objectId = {} , productPromotionJson = {} , rechargePromotionJson = {}", tenantId, userId, objectData.getId(), productPromotionJson, rechargePromotionJson);

        String lockKey = getOrderGoodsLockKey("Edit", tenantId, objectData.getId());
        String uuid = UUID.randomUUID().toString();
        if (redisDistributedLock.tryLock(lockKey, uuid)) {
            try {
                OrderGoodsPromotionPolicyEdit.Arg orderGoodsPromotionPolicyArg = new OrderGoodsPromotionPolicyEdit.Arg();
                orderGoodsPromotionPolicyArg.setObjectId(objectData.getId());
                orderGoodsPromotionPolicyArg.setTenantId(tenantId);
                orderGoodsPromotionPolicyArg.setProductPromotionJson(productPromotionJson);
                orderGoodsPromotionPolicyArg.setRechargePromotionJson(rechargePromotionJson);

                OrderGoodsPO orderGoodsPO = orderGoodsDAO.find(tenantId, objectData.getId(), ApiNames.TPM_ACTIVITY_OBJ);
                if (Objects.isNull(orderGoodsPO)) {
                    log.info("updateOrderGoodsSFAData orderGoodsPO is null. objectId = {}", objectData.getId());
                    return;
                }

                if (StringUtils.isNotBlank(productPromotionJson)) {
                    updateOrderGoodsSFAPromotionPolicyData(tenantId, userId, objectData, productPromotionJson, orderGoodsPO);
                } else {
                    List<OrderGoodsPromotionPolicyRulePO> promotionPolicyRule = orderGoodsPromotionPolicyRuleDAO.queryByParentObjectId(tenantId, objectData.getId());
                    if (CollectionUtils.isNotEmpty(promotionPolicyRule)) {
                        invalidPromotionPolicyRule(tenantId, userId, promotionPolicyRule);
                    }
                }

                if (StringUtils.isNotBlank(rechargePromotionJson)) {
                    JSONObject rechargePromotion = JSON.parseObject(rechargePromotionJson);
                    if (!rechargePromotion.containsKey("rebate_policy_id") || StringUtils.isBlank(rechargePromotion.getString("rebate_policy_id"))) {
                        if (StringUtils.isBlank(orderGoodsPO.getRechargePromotionJson())) {
                            orderGoodsPO.setRechargePromotionJson(rechargePromotionJson);
                        }
                        createOrderGoodsSFARebatePolicyData(tenantId, userId, objectData, orderGoodsPO);
                    } else {
                        String rebatePolicyId = rechargePromotion.getString("rebate_policy_id");
                        String rechargePromotionJsonOld = orderGoodsPO.getRechargePromotionJson();
                        if (!rechargePromotionJson.equals(rechargePromotionJsonOld)) {
                            orderGoodsPO.setRechargePromotionJson(rechargePromotionJson);
                            updateOrderGoodsSFARebatePolicyData(tenantId, userId, rebatePolicyId, objectData, orderGoodsPO);
                        }
                    }
                } else {
                    String rechargePromotionJsonOld = orderGoodsPO.getRechargePromotionJson();
                    if (StringUtils.isNotBlank(rechargePromotionJsonOld)) {
                        JSONObject rechargePromotion = JSON.parseObject(rechargePromotionJsonOld);
                        String rebatePolicyId = rechargePromotion.getString("rebate_policy_id");
                        invalidRebatePolicy(tenantId, userId, rebatePolicyId, orderGoodsPO);
                    }
                }
            } finally {
                redisDistributedLock.unlock(lockKey, uuid);
            }
        }
    }

    private void updateOrderGoodsSFAPromotionPolicyData(String tenantId, String userId, IObjectData objectData, String productPromotionJson, OrderGoodsPO orderGoodsPO) {

        List<OrderGoodsPromotionPolicyRuleVO> orderGoodsPromotionPolicyRuleVOS = JSONArray.parseArray(productPromotionJson, OrderGoodsPromotionPolicyRuleVO.class);
        List<OrderGoodsPromotionPolicyRulePO> promotionPolicyRule = orderGoodsPromotionPolicyRuleDAO.queryByParentObjectId(tenantId, objectData.getId());
        if (CollectionUtils.isNotEmpty(promotionPolicyRule)) {
            // invalid
            Map<String, OrderGoodsPromotionPolicyRuleVO> promotionPolicyRuleVOMap = orderGoodsPromotionPolicyRuleVOS.stream().filter(v -> StringUtils.isNotBlank(v.getObjectId())).collect(Collectors.toMap(OrderGoodsPromotionPolicyRuleVO::getObjectId, v -> v));
            List<OrderGoodsPromotionPolicyRulePO> needValidRule = promotionPolicyRule.stream().filter(rule -> !promotionPolicyRuleVOMap.containsKey(rule.getObjectId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needValidRule)) {
                invalidPromotionPolicyRule(tenantId, userId, needValidRule);
            }

            //edit
            List<OrderGoodsPromotionPolicyRulePO> needEditRule = promotionPolicyRule.stream().filter(rule ->
                    promotionPolicyRuleVOMap.containsKey(rule.getObjectId())
            ).peek(po -> po.setProductGiftData(promotionPolicyRuleVOMap.get(po.getObjectId()).getProductGiftData())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(needEditRule)) {
                for (OrderGoodsPromotionPolicyRulePO po : needEditRule) {
                    editPricePolicy(tenantId, userId, po.getObjectId(), objectData, po.getProductGiftData(), "Edit");
                }
                needEditRule.forEach(po -> orderGoodsPromotionPolicyRuleDAO.edit(tenantId, Integer.parseInt(userId), po.getUniqueId(), po));
            }
        }

        //add
        boolean existNewRule = orderGoodsPromotionPolicyRuleVOS.stream().anyMatch(rule -> StringUtils.isBlank(rule.getObjectId()));
        if (existNewRule) {
            List<OrderGoodsPromotionPolicyRuleVO> needAddRule = orderGoodsPromotionPolicyRuleVOS.stream().filter(rule -> StringUtils.isBlank(rule.getObjectId())).collect(Collectors.toList());
            createOrderGoodsSFAPromotionPolicyData(tenantId, userId, objectData, orderGoodsPO.getId().toString(), needAddRule);
        }
    }

    private void invalidRebatePolicy(String tenantId, String userId, String rebatePolicyId, OrderGoodsPO orderGoodsPO) {
        if (StringUtils.isBlank(rebatePolicyId)) {
            log.info("invalidRebatePolicy rebatePolicyId  is blank");
            return;
        }
        log.info("invalidRebatePolicy rebatePolicyId:{},rechargePromotionJson:{}", rebatePolicyId, orderGoodsPO.getRechargePromotionJson());
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build();
        ActionContext invalidActionContext = new ActionContext(requestContext, ApiNames.REBATE_POLICY_OBJ, "BulkInvalid");
        invalidActionContext.setAttribute("triggerWorkflow", false);
        invalidActionContext.setAttribute("triggerFlow", false);
        StandardBulkInvalidAction.Arg arg = new StandardBulkInvalidAction.Arg();
        arg.setJson(buildInvalidJsonArg(tenantId, Lists.newArrayList(rebatePolicyId), ApiNames.REBATE_POLICY_OBJ));
        arg.putExtraData(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);

        try {
            serviceFacade.triggerAction(invalidActionContext, arg, StandardBulkInvalidAction.Result.class);
            orderGoodsPO.setRechargePromotionJson("");
            orderGoodsDAO.edit(tenantId, Integer.parseInt(userId), orderGoodsPO.getUniqueId(), orderGoodsPO);
        } catch (ValidateException validateException) {
            throw new ValidateException(validateException.getMessage());
        } catch (Exception ex) {
            log.error("作废返利产生政策失败：{},", rebatePolicyId, ex);
        }
    }

    private void invalidPromotionPolicyRule(String tenantId, String userId, List<OrderGoodsPromotionPolicyRulePO> needValidRule) {
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build();
        ActionContext invalidActionContext = new ActionContext(requestContext, ApiNames.PRICE_POLICY_OBJ, "BulkInvalid");
        invalidActionContext.setAttribute("triggerWorkflow", false);
        invalidActionContext.setAttribute("triggerFlow", false);
        StandardBulkInvalidAction.Arg arg = new StandardBulkInvalidAction.Arg();
        List<String> promotionPolicyIds = needValidRule.stream().map(OrderGoodsPromotionPolicyRulePO::getObjectId).collect(Collectors.toList());
        arg.setJson(buildInvalidJsonArg(tenantId, promotionPolicyIds, ApiNames.PRICE_POLICY_OBJ));
        arg.putExtraData(TPMTriggerActionService.REQUEST_FROM, TPMTriggerActionService.REQUEST_APP_NAME);

        try {
            serviceFacade.triggerAction(invalidActionContext, arg, StandardBulkInvalidAction.Result.class);
            needValidRule.forEach(po -> orderGoodsPromotionPolicyRuleDAO.delete(tenantId, Integer.parseInt(userId), po.getUniqueId()));
        } catch (ValidateException validateException) {
            throw new ValidateException(validateException.getMessage());
        } catch (Exception ex) {
            log.error("作废价格政策失败：{},", JSON.toJSONString(promotionPolicyIds), ex);
        }
    }

    private String buildInvalidJsonArg(String tenantId, List<String> dataIds, String objectApiName) {
        List<JSONObject> invalidDataList = new ArrayList<>();
        for (String dataId : dataIds) {
            JSONObject invalidData = new JSONObject();
            invalidData.put("object_describe_api_name", objectApiName);
            invalidData.put("tenant_id", tenantId);
            invalidData.put("_id", dataId);
            invalidDataList.add(invalidData);
        }
        JSONObject invalidArg = new JSONObject();
        invalidArg.put("dataList", invalidDataList);
        return JSON.toJSONString(invalidArg);
    }


    @Override
    public void enableOrderGoodsSFAData(String tenantId, String userId, IObjectData objectData) {
        if (!judgedIsOrderGoodsPromotionData(objectData)) {
            return;
        }
        handleUpdateOrderGoodsActiveStatus(tenantId, userId, objectData.getId(), ActiveStatusEnum.ENABLE.code());
    }

    @Override
    public void batchInvalidOrderGoodsSFAData(String tenantId, String userId, List<IObjectData> activities) {
        activities.stream().filter(this::judgedIsOrderGoodsPromotionData).forEach(activity -> handleUpdateOrderGoodsActiveStatus(tenantId, userId, activity.getId(), ActiveStatusEnum.DISABLE.code()));
    }

    @Override
    public List<String> queryUsedOrderGoodsPromotionActivityNames(String tenantId, String userId, List<IObjectData> activities) {

        List<String> activityIds = activities.stream().filter(this::judgedIsOrderGoodsPromotionData).map(DBRecord::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityIds)) {
            return Collections.emptyList();
        }
        List<IObjectData> quotes = queryObjByActivityIds(tenantId, activityIds, ApiNames.QUOTE_OBJ);
        List<String> quotesActivityIds = quotes.stream().map(o -> o.get(PaymentFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());
        activityIds.removeAll(quotesActivityIds);

        if (CollectionUtils.isEmpty(activityIds)) {
            return Collections.emptyList();
        }

        List<IObjectData> payments = queryObjByActivityIds(tenantId, activityIds, ApiNames.PAYMENT_OBJ);
        List<String> paymentsActivityIds = payments.stream().map(o -> o.get(PaymentFields.ACTIVITY_ID, String.class)).collect(Collectors.toList());

        return activities.stream()
                .filter(o -> quotesActivityIds.contains(o.getId()) || paymentsActivityIds.contains(o.getId()))
                .map(IObjectData::getName)
                .collect(Collectors.toList());
    }

    private List<IObjectData> queryObjByActivityIds(String tenantId, List<String> activityIds, String objApiName) {

        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(200);
        stq.setOffset(0);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(PaymentFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.IN);
        activityFilter.setFieldValues(activityIds);

        Filter orderGoodsMeetingFlagFilter = new Filter();
        orderGoodsMeetingFlagFilter.setFieldName(TPMActivityFields.ORDER_GOODS_MEETING_FLAG);
        orderGoodsMeetingFlagFilter.setOperator(Operator.EQ);
        orderGoodsMeetingFlagFilter.setFieldValues(Lists.newArrayList("true"));

        stq.setFilters(Lists.newArrayList(activityFilter, orderGoodsMeetingFlagFilter));

        List<IObjectData> detailDataList = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(
                User.systemUser(tenantId),
                objApiName,
                stq,
                Lists.newArrayList(CommonFields.ID, CommonFields.NAME, PaymentFields.ACTIVITY_ID)).getData();

        if (CollectionUtils.isEmpty(detailDataList)) {
            return Lists.newArrayList();
        }
        return detailDataList;
    }

    @Override
    public OrderGoodsQueryAccountFilter.Result orderGoodsQueryAccountFilter(OrderGoodsQueryAccountFilter.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String activityId = arg.getObjectId();
        List<IFilter> filters = activityService.getObjAccountFiltersByActivity(context.getTenantId(), -10000, activityId);
        return OrderGoodsQueryAccountFilter.Result.builder().filters(filters).build();
    }

    @Override
    public OrderGoodsMeetingFilter.Result orderGoodsMeetingFilter(OrderGoodsMeetingFilter.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        List<IFilter> filters = activityService.getOrderGoodsMeetingByAccountId(Integer.parseInt(context.getTenantId()), context.getEmployeeId(), arg.getAccountId());
        return OrderGoodsMeetingFilter.Result.builder().filters(filters).build();
    }

    @Override
    public OrderGoodsInvalidByActivityId.Result invalidByActivityId(OrderGoodsInvalidByActivityId.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        IActionContext actionContext = ActionContextExt.of(User.systemUser(context.getTenantId()), RequestContextManager.getContext())
                .allowUpdateInvalid(true)
                .setNotValidate(true)
                .getContext();
        List<IObjectData> activities = serviceFacade.findByIdsIncludeInvalid(Lists.newArrayList(arg.getObjectId()), context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, actionContext);
        disableOrderGoodsSFAData(context.getTenantId(), "-10000", activities.get(0));

        return OrderGoodsInvalidByActivityId.Result.builder().build();
    }

    @Override
    public OrderGoodsEnableByActivityId.Result enableByActivityId(OrderGoodsEnableByActivityId.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        IActionContext actionContext = ActionContextExt.of(User.systemUser(context.getTenantId()), RequestContextManager.getContext())
                .allowUpdateInvalid(true)
                .setNotValidate(true)
                .getContext();
        List<IObjectData> activities = serviceFacade.findByIdsIncludeInvalid(Lists.newArrayList(arg.getObjectId()), context.getTenantId(), ApiNames.TPM_ACTIVITY_OBJ, actionContext);
        enableOrderGoodsSFAData(context.getTenantId(), "-10000", activities.get(0));

        return OrderGoodsEnableByActivityId.Result.builder().build();
    }
}
