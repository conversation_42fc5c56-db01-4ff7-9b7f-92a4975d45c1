package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.proof.CorrectActivityStatus;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.service.RedPacketRecordService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import redis.clients.jedis.params.SetParams;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/2/20 4:12 PM
 */
@SuppressWarnings("Duplicates")
public class TPMActivityObjCorrectStatusController extends PreDefineController<CorrectActivityStatus.Arg, CorrectActivityStatus.Result> {

    private static final MergeJedisCmd redisCmd = SpringUtil.getContext().getBean("redisCmd", MergeJedisCmd.class);

    private static final String REDIS_UNIQUE_TEMPLATE = "TPM.CorrectStatus.1.%s";

    private static final Long LOCK_TIME = 3 * 3600L;
    private static final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private static final RedPacketRecordService redPacketRecordService = SpringUtil.getContext().getBean(RedPacketRecordService.class);

    private static final int MAX_QUERY_TIMES = 2000;

    private static final RedissonClient redissonCmd = SpringUtil.getContext().getBean("redissonCmd", RedissonClient.class);

    private static final List<String> RESULT_FIELDS = Lists.newArrayList("tenant_id", "object_describe_api_name", "_id");


    @Override
    protected CorrectActivityStatus.Result doService(CorrectActivityStatus.Arg arg) {
        log.info("correct arg:{}", arg);
        this.controllerContext.getRequestContext().setAttribute("not_validate", true);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            String lockKey = String.format(REDIS_UNIQUE_TEMPLATE, controllerContext.getTenantId());
            String value = UUID.randomUUID().toString();


            try {
                controllerContext.setAttribute("limiter_fail", arg.getLimiterFail());
                String lockResult = redisCmd.set(lockKey, value, SetParams.setParams().nx().ex(LOCK_TIME));
                if (!"ok".equalsIgnoreCase(lockResult) && !Boolean.TRUE.equals(arg.getEscapeLock())) {
                    log.info("correct task is running ,ei:{}", controllerContext.getTenantId());
                    return;
                }

                if (TPMGrayUtils.skipUnifiedCaseHandler(controllerContext.getTenantId()) && isExistUnifiedCase(controllerContext)) {
                    correctScheduleUnifiedCase(controllerContext, serviceFacade);
                    stopWatch.lap("correctScheduleUnifiedCase");

                    correctEndUnifiedCase(controllerContext, serviceFacade);
                    stopWatch.lap("correctEndUnifiedCase");
                }

                correctScheduleActivity(controllerContext, serviceFacade);
                stopWatch.lap("correctScheduleActivity");

                correctEndActivity(controllerContext, serviceFacade);
                stopWatch.lap("correctEndActivity");

                if (TPMGrayUtils.isLjjUpdateStatusByCloseDate(controllerContext.getTenantId())) {
                    correctEarlyCloseActivity(controllerContext, serviceFacade);
                }

                correctScheduleAgreement(controllerContext, serviceFacade);
                stopWatch.lap("correctScheduleAgreement");

                correctEndAgreement(controllerContext, serviceFacade);
                stopWatch.lap("correctEndAgreement");

                correctIneffectiveAgreement();
                stopWatch.lap("correctIneffectiveAgreement");

                if (TPMGrayUtils.isMNUpdateStatusByRedStatus(controllerContext.getTenantId())) {
                    correctRedPacketStatus(controllerContext, serviceFacade);
                    stopWatch.lap("correctRedPacketStatus");
                }
            } finally {
                redisCmd.del(lockKey);
            }
        });
        parallelTask.run();

        return new CorrectActivityStatus.Result();
    }

    private boolean isExistUnifiedCase(ControllerContext controllerContext) {
        try {
            serviceFacade.findObject(controllerContext.getTenantId(), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        } catch (ObjectDefNotFoundError ex){
            return false;
        }
        return true;
    }

    private void correctRedPacketStatus(ControllerContext context, ServiceFacade serviceFacade) {
        try {
            serviceFacade.findObject(context.getTenantId(), ApiNames.RED_PACKET_RECORD_OBJ);
        } catch (ObjectDefNotFoundError ex) {
            return;
        }

        long now = System.currentTimeMillis();
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter redStatusFilter = new Filter();
        redStatusFilter.setFieldName(RedPacketRecordObjFields.RED_PACKET_STATUS);
        redStatusFilter.setOperator(Operator.EQ);
        redStatusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(RedPacketRecordObjFields.WITHDRAWAL_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(RedPacketRecordObjFields.WithdrawalStatus.AWAIT));

        Filter expirationTimeFilter = new Filter();
        expirationTimeFilter.setFieldName(RedPacketRecordObjFields.EXPIRATION_TIME);
        expirationTimeFilter.setOperator(Operator.LT);
        expirationTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        query.setFilters(Lists.newArrayList(expirationTimeFilter, redStatusFilter, statusFilter));
        User user = User.systemUser(context.getTenantId());
        CommonUtils.queryDataWithoutOffset(serviceFacade, user, ApiNames.RED_PACKET_RECORD_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, dataList -> {
            redPacketRecordService.batchUpdateRedPacketStatus(user, dataList);
        });
    }

    private void correctEarlyCloseActivity(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");
        // 亮晶晶企业， 费用取消开始执行日期字段，自定义字段 __c
        Filter closeDateFilter = new Filter();
        closeDateFilter.setFieldName("field_f1sJK__c");
        closeDateFilter.setOperator(Operator.LT);
        closeDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
            TPMActivityFields.ACTIVITY_STATUS__SCHEDULE,
            TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS,
            TPMActivityFields.ACTIVITY_STATUS__END));

        query.setFilters(Lists.newArrayList(closeDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__CLOSED);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });

    }

    private void correctEndUnifiedCase(ControllerContext context, ServiceFacade serviceFacade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityUnifiedCaseFields.END_DATE);
        endDateFilter.setOperator(Operator.LT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
            TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE,
            TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS));

        query.setFilters(Lists.newArrayList(endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__END);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                serviceFacade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });

    }

    private void correctScheduleUnifiedCase(ControllerContext context, ServiceFacade serviceFacade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityUnifiedCaseFields.START_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityUnifiedCaseFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
            TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE,
            TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__END)
        );

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS, TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                serviceFacade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });

    }

    private void correctScheduleActivity(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS__SCHEDULE, TPMActivityFields.ACTIVITY_STATUS__END));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });

    }

    private void correctEndActivity(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityFields.END_DATE);
        endDateFilter.setOperator(Operator.LT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityFields.ACTIVITY_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
            TPMActivityFields.ACTIVITY_STATUS__SCHEDULE,
            TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS));

        query.setFilters(Lists.newArrayList(endDateFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, activities -> {
            for (IObjectData activity : activities) {
                activity.set(TPMActivityFields.ACTIVITY_STATUS, TPMActivityFields.ACTIVITY_STATUS__END);
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityFields.ACTIVITY_STATUS);
            for (List<IObjectData> parts : Lists.partition(activities, 100)) {
                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });
    }

    private void correctScheduleAgreement(ControllerContext context, ServiceFacade facade) {
        if (TPMGrayUtils.isYinLu(context.getTenantId())) {
            return;
        }
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__END, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, statusFilter, lifeStatusFilter));

        List<String> resultFields = Lists.newArrayList(TPMActivityAgreementFields.STORE_ID);
        resultFields.addAll(RESULT_FIELDS);

        RRateLimiter limiter = initRateLimiter(context.getTenantId(), "CORRECT_SCHEDULE_AGREEMENT");

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query, resultFields, MAX_QUERY_TIMES, agreements -> {
            List<String> storeIds = new ArrayList<>();
            for (IObjectData agreement : agreements) {
                agreement.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS);
                String storeId = agreement.get(TPMActivityAgreementFields.STORE_ID, String.class);
                if (!Strings.isNullOrEmpty(storeId)) {
                    storeIds.add(storeId);
                }
            }
            List<String> updateFields = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS);
            for (List<IObjectData> parts : Lists.partition(agreements, 50)) {
                limiter.acquire(parts.size());

                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
            storeBusiness.updateStoreLabel(controllerContext.getTenantId(), storeIds, AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 1);
        });
    }

    private void correctEndAgreement(ControllerContext context, ServiceFacade facade) {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.LT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE));

        query.setFilters(Lists.newArrayList(endDateFilter, statusFilter));

        List<String> resultFields = Lists.newArrayList(TPMActivityAgreementFields.STORE_ID);
        resultFields.addAll(RESULT_FIELDS);

        RRateLimiter limiter = initRateLimiter(context.getTenantId(), "CORRECT_END_AGREEMENT");

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(context.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query, resultFields, MAX_QUERY_TIMES, agreements -> {
            List<String> storeIds = new ArrayList<>();
            for (IObjectData agreement : agreements) {
                agreement.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__END);
                String storeId = agreement.get(TPMActivityAgreementFields.STORE_ID, String.class);
                if (!Strings.isNullOrEmpty(storeId)) {
                    storeIds.add(storeId);
                }
            }

            List<String> updateFields = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS);
            for (List<IObjectData> parts : Lists.partition(agreements, 50)) {
                limiter.acquire(parts.size());

                facade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }

            storeBusiness.updateStoreLabel(controllerContext.getTenantId(), storeIds, AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 0);
        });
    }

    private RRateLimiter initRateLimiter(String tenantId, String operation) {
        String key = String.format("FMCG:TPM:CORRECT_STATUS:%s:%s", operation, tenantId);
        RRateLimiter limiter = redissonCmd.getRateLimiter(key);

        String rateKey = String.format("FMCG.TPM.CORRECT_STATUS.%s.%s", operation, tenantId);
        long rate = ConfigFactory.getConfig("gray-rel-fmcg").getLong(rateKey, 1200);

        limiter.setRate(RateType.PER_CLIENT, rate, 1, RateIntervalUnit.MINUTES);
        return limiter;
    }

    private void correctIneffectiveAgreement() {
        long now = System.currentTimeMillis();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMActivityAgreementFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.LT);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMActivityAgreementFields.END_DATE);
        endDateFilter.setOperator(Operator.GT);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(now)));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(ObjectLifeStatus.INEFFECTIVE.getCode()));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(
            TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS,
            TPMActivityAgreementFields.AGREEMENT_STATUS__END));

        query.setFilters(Lists.newArrayList(beginDateFilter, endDateFilter, lifeStatusFilter, statusFilter));

        CommonUtils.queryDataWithoutOffset(serviceFacade, User.systemUser(controllerContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query, RESULT_FIELDS, MAX_QUERY_TIMES, agreements -> {
            for (IObjectData activity : agreements) {
                activity.set(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
            }

            List<String> updateFields = Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS);
            for (List<IObjectData> parts : Lists.partition(agreements, 100)) {
                serviceFacade.batchUpdateByFields(ActionContextExt.of(User.systemUser(controllerContext.getTenantId()), this.controllerContext.getRequestContext()).getContext(), parts, updateFields);
            }
        });
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
