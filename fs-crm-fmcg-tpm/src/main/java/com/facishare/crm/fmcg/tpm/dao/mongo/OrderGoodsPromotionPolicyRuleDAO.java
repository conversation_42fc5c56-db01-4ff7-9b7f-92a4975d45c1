package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.OrderGoodsPromotionPolicyRulePO;
import org.mongodb.morphia.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/26 20:59
 */
public class OrderGoodsPromotionPolicyRuleDAO extends UniqueIdBaseDAO<OrderGoodsPromotionPolicyRulePO> {

    protected OrderGoodsPromotionPolicyRuleDAO(Class<OrderGoodsPromotionPolicyRulePO> clazz) {
        super(clazz);
    }

    public List<OrderGoodsPromotionPolicyRulePO> queryAllByParentObjectId(String tenantId, String parentObjectId) {
        Query<OrderGoodsPromotionPolicyRulePO> query = mongoContext.createQuery(OrderGoodsPromotionPolicyRulePO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(OrderGoodsPromotionPolicyRulePO.F_PARENT_OBJECT_ID).equal(parentObjectId);
        query.order(OrderGoodsPromotionPolicyRulePO.F_LAST_UPDATE_TIME);
        return query.asList();
    }

    public List<OrderGoodsPromotionPolicyRulePO> queryByParentObjectId(String tenantId, String parentObjectId) {
        Query<OrderGoodsPromotionPolicyRulePO> query = mongoContext.createQuery(OrderGoodsPromotionPolicyRulePO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(OrderGoodsPromotionPolicyRulePO.F_PARENT_OBJECT_ID).equal(parentObjectId);
        query.field(OrderGoodsPromotionPolicyRulePO.F_IS_DELETED).equal(false);
        query.order(OrderGoodsPromotionPolicyRulePO.F_LAST_UPDATE_TIME);
        return query.asList();
    }
}
