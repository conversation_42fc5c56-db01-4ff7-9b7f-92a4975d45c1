package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNameUtil;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.business.DescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.business.enums.ActivityTemplateI18nEnum;
import com.facishare.crm.fmcg.tpm.common.constant.OrderGoodsContents;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeTemplatePO;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.TPMRoleService;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryModule;
import com.facishare.crm.fmcg.tpm.service.abstraction.BuryOperation;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateGet;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateList;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeTemplateNameList;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityTypeTemplateService;
import com.facishare.crm.fmcg.tpm.web.utils.TPMI18Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutLogicServiceImpl;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeMatchInfo;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.dto.auth.RoleViewForWebPojo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.auth.model.RolePojo;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectRelationMatch;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.notifier.support.NotifierClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.text.StringSubstitutor;
import org.bson.Document;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class ActivityTypeTemplateService implements IActivityTypeTemplateService {

    @Resource
    private StoreBusiness storeBusiness;
    @Resource
    private LayoutLogicServiceImpl layoutLogicService;
    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;
    @Resource
    private RecordTypeLogicServiceImpl recordTypeLogicService;
    @Resource
    private ActivityTypeDAO activityTypeDAO;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private PromotionPolicyService promotionPolicyService;

    @Resource
    private DescribeCacheService describeCacheService;

    @Resource
    private ITPM2Service tpm2Service;

    @Resource
    private TPMRoleService tPMRoleService;

    public static final String DETAIL_LAYOUT_TYPE = "detail";
    private static final Map<String, List<String>> OBJ_FIELD_API_NAME_MAP = Maps.newHashMap();

    static {
        OBJ_FIELD_API_NAME_MAP.put(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, Lists.newArrayList("source_object_api_name", "mode_type", "unified_total_policy_dynamic_amount"));
        OBJ_FIELD_API_NAME_MAP.put(ApiNames.TPM_ACTIVITY_OBJ, Lists.newArrayList("source_object_api_name", "mode_type", "limit_obj_type", "total_policy_dynamic_amount", "account_unify_limit_amount"));
        OBJ_FIELD_API_NAME_MAP.put(ApiNames.TPM_ACTIVITY_STORE_OBJ, Lists.newArrayList("policy_limit_amount", "used_limit_amount", "remain_limit_amount"));
        OBJ_FIELD_API_NAME_MAP.put(ApiNames.SALES_ORDER_OBJ, Lists.newArrayList("activity_store_id"));
        OBJ_FIELD_API_NAME_MAP.put(ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ, Lists.newArrayList("activity_id"));
        OBJ_FIELD_API_NAME_MAP.put(ApiNames.PRICE_POLICY_OBJ, Lists.newArrayList("activity_id"));
    }

    @Override
    public ActivityTypeTemplateList.Result list(ActivityTypeTemplateList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        JSONObject json = loadResource();
        ActivityTypeTemplateList.Result result = json.toJavaObject(ActivityTypeTemplateList.Result.class);
        result.setInfoTips(new HashMap<>());
        List<ActivityNodeTemplatePO> all = activityNodeTemplateDAO.all(context.getTenantId(), false).stream().filter(f -> "system".equals(f.getPackageType())).collect(Collectors.toList());
        Map<String, String> nodeNameMap = all.stream().collect(Collectors.toMap(ActivityNodeTemplatePO::getType, ActivityNodeTemplatePO::getName));
        for (ActivityTypeTemplateList.ActivityTypeTemplateGroupVO group : result.getTemplateGroups()) {
            validateOpenPricePolicy(group.getId(), context, result.getInfoTips());
            for (ActivityTypeTemplateList.ActivityTypeTemplateVO template : group.getTemplates()) {
                template.setTemplateId(template.getId());
                for (ActivityTypeTemplateList.ActivityTypeTemplateNodeVO node : template.getActivityNodeList()) {
                    node.setName(nodeNameMap.get(node.getType()));
                }
                putVirtuallyNode(context.getTenantId(), template);
            }
        }
        //todo 是否去掉灰度配置
       /* if (TPMGrayUtils.disallowEnableRewardRuleTemplate(context.getTenantId())) {
            result.setTemplateGroups(result.getTemplateGroups().stream().filter(f -> !f.getId().equals("reward")).collect(Collectors.toList()));
        }*/

        int tenantId = Integer.parseInt(context.getTenantId());
        if (tpm2Service.existTPMCodeLicenseTenant(tenantId) && !tpm2Service.existTPMLicenseTenant(tenantId)) {
            result.setTemplateGroups(result.getTemplateGroups().stream().filter(f -> f.getId().equals("reward")).collect(Collectors.toList()));
        }

        if (!tpm2Service.existTPMCodeLicenseTenant(tenantId) && tpm2Service.existTPMLicenseTenant(tenantId)) {
            result.setTemplateGroups(result.getTemplateGroups().stream().filter(f -> !f.getId().equals("reward")).collect(Collectors.toList()));
        }
        result.setTemplateGroups(result.getTemplateGroups().stream().filter(f -> !f.getId().equals(OrderGoodsContents.ORDER_GOODS_TEMPLATE_ID)).collect(Collectors.toList()));

        return result;

    }

    private void putVirtuallyNode(String tenantId, ActivityTypeTemplateList.ActivityTypeTemplateVO template) {
        // 促销类
        if (template.getId().startsWith("promotion")) {
            ActivityTypeTemplateList.ActivityTypeTemplateNodeVO virtuallyNode = new ActivityTypeTemplateList.ActivityTypeTemplateNodeVO();
            virtuallyNode.setName(TPMI18Utils.getActivitySystemNodeText(tenantId, null, String.format("promotion.virtually.%s", ApiNames.SALES_ORDER_OBJ), null, null));
            virtuallyNode.setType("virtually");
            template.getActivityNodeList().add(virtuallyNode);
        }
    }

    @Override
    public ActivityTypeTemplateGet.Result get(ActivityTypeTemplateGet.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        List<ActivityTypeTemplateGet.ActivityTypeTemplateVO> templates = loadTemplates();
        ActivityTypeTemplateGet.ActivityTypeTemplateVO template = templates.stream().filter(f -> f.getId().equals(arg.getId())).findFirst().orElse(null);
        if (Objects.isNull(template)) {
            throw new MetaDataBusinessException("activity type template not found.");
        }

        Function<String, Boolean> isDuplicateFunc = (String code) -> activityTypeDAO.isDuplicateApiName(context.getTenantId(), code);

        template.setApiName(ApiNameUtil.getActivityTypeApiName(isDuplicateFunc));
        template.setDepartmentIds(Lists.newArrayList(999999));
        template.setDescription("");

        Map<String, ActivityNodeTemplatePO> nodeMap = activityNodeTemplateDAO.all(context.getTenantId(), false).stream().filter(f -> "system".equals(f.getPackageType())).collect(Collectors.toMap(ActivityNodeTemplatePO::getType, n -> n));

        List<String> nodeDisplayNameList = nodeMap.values().stream().map(ActivityNodeTemplatePO::getObjectApiName).distinct().collect(Collectors.toList());
        Map<String, String> objectDisplayNameMap = serviceFacade.findObjects(context.getTenantId(), nodeDisplayNameList).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getDisplayName()));

        LanguageReplaceWrapper.doInChinese(() -> {
            for (ActivityTypeTemplateGet.ActivityTypeTemplateNodeVO node : template.getActivityNodeList()) {
                ActivityNodeTemplatePO nodeTemplate = nodeMap.get(node.getType());
                if (Objects.nonNull(nodeTemplate)) {
                    node.setTemplateId(nodeTemplate.getId().toString());
                    node.setObjectApiName(nodeTemplate.getObjectApiName());
                    node.setReferenceActivityFieldApiName(nodeTemplate.getReferenceFieldApiName());
                    node.setPackageType(nodeTemplate.getPackageType());
                    node.setName(nodeTemplate.getName());
                    node.setObjectDisplayName(objectDisplayNameMap.getOrDefault(node.getObjectApiName(), "--"));
                }

                if (CollectionUtils.isNotEmpty(node.getInitFields())) {
                    initFields(context.getTenantId(), node.getObjectApiName(), node.getInitFields());
                }

                if (!"default__c".equals(node.getObjectRecordType())) {
                    initNodeObject(context.getTenantId(), node);
                }

                if (CollectionUtils.isNotEmpty(node.getDetailObjects())) {
                    for (ActivityTypeTemplateGet.ActivityTypeTemplateNodeDetailVO detailObject : node.getDetailObjects()) {
                        initNodeDetailObject(context.getTenantId(), node, detailObject);
                    }
                }
            }
        });
        String dealerApiName = storeBusiness.findDealerFieldApiName(context.getTenantId());

        // 别问，问就是为了汪军
        template.setTemplateId(template.getId());
        BuryService.asyncTpmLog(
                Integer.valueOf(context.getTenantId()),
                context.getEmployeeId(),
                BuryModule.TPM.TPM_ACTIVITY_TYPE_TEMPLATE,
                String.format(BuryOperation.USE_ACTIVITY_TYPE_TEMPLATE, template.getId())
        );

        template.setId(null);

        BuryService.asyncTpmLog(
                Integer.valueOf(context.getTenantId()),
                context.getEmployeeId(),
                BuryModule.TPM.TPM_ACTIVITY_TYPE_TEMPLATE,
                String.format(BuryOperation.USE_ACTIVITY_TYPE_TEMPLATE, template.getTemplateId())
        );

        return ActivityTypeTemplateGet.Result.builder().activityType(template).dealerApiName(dealerApiName).build();
    }

    @Override
    public ActivityTypeTemplateNameList.Result queryNameList(ActivityTypeTemplateNameList.Arg arg) {
        List<ActivityTypeTemplateGet.ActivityTypeTemplateVO> templates = loadTemplates();
        templates = templates.stream().filter(f -> !f.getId().equals(OrderGoodsContents.ORDER_GOODS_TEMPLATE_ID)).collect(Collectors.toList());

        Map<String, String> nameMap = templates.stream().collect(Collectors.toMap(ActivityTypeTemplateGet.ActivityTypeTemplateVO::getId, ActivityTypeTemplateGet.ActivityTypeTemplateVO::getName));
        return ActivityTypeTemplateNameList.Result.builder().templateName(nameMap).build();

    }

    private void initFields(String tenantId, String objectApiName, List<String> fields) {
        LanguageReplaceWrapper.doInChinese(() -> {
            ParallelUtils.createParallelTask().submit(() -> {
                User sys = User.systemUser(tenantId);
                fields.forEach(field -> {
                    if (!describeCacheService.isExistField(tenantId, objectApiName, field)) {
                        try {
                            serviceFacade.addDescribeCustomField(sys, objectApiName, loadModuleFieldsString(objectApiName + "." + field), null, null);
                        } catch (Exception e) {
                            log.info("add field err.", e);
                        }
                    }
                });
            }).run();
        });
    }

    private void validateOpenPricePolicy(String templateId, ApiContext context, Map<String, String> infoTips) {
        if (templateId.startsWith("promotion") && !isOpenPricePolicy(context.getTenantId())) {
            infoTips.put(templateId, I18N.text(I18NKeys.IS_NOT_OPEN_PROMOTION_LICENSE));
        }
    }

    private void initNodeDetailObject(String tenantId, ActivityTypeTemplateGet.ActivityTypeTemplateNodeVO node, ActivityTypeTemplateGet.ActivityTypeTemplateNodeDetailVO detailObject) {
        initLayout(tenantId, detailObject.getObjectApiName(), detailObject.getObjectLayoutApiName());
        initRecordType(tenantId, detailObject.getObjectApiName(), detailObject.getObjectRecordTypeApiName(), detailObject.getObjectRecordTypeName(), detailObject.getObjectLayoutApiName());

        assignLayout(tenantId, "detail", detailObject.getObjectApiName(), detailObject.getObjectRecordTypeApiName(), detailObject.getObjectLayoutApiName());
        assignMasterDetailRecordTypeRelation(tenantId, node.getObjectApiName(), node.getObjectRecordType(), detailObject.getObjectApiName(), detailObject.getObjectRecordTypeApiName());
        if (CollectionUtils.isNotEmpty(detailObject.getInitFields())) {
            initFields(tenantId, detailObject.getObjectApiName(), detailObject.getInitFields());
        }
    }

    private void assignMasterDetailRecordTypeRelation(String tenantId, String masterApiName, String masterRecordTypeApiName, String detailApiName, String detailRecordTypeApiName) {
        List<IObjectRelationMatch> matches = recordTypeLogicService.findMatchRecordTypeRelation(tenantId, masterApiName, masterRecordTypeApiName, detailApiName);
        for (IObjectRelationMatch match : matches) {
            List<RecordTypeMatchInfo.RecordInfo> data = JSON.parseArray(match.getTargetValue(), RecordTypeMatchInfo.RecordInfo.class);
            for (RecordTypeMatchInfo.RecordInfo datum : data) {
                datum.setMatch(datum.getApiName().equals(detailRecordTypeApiName));
            }
            match.setTargetValue(JSON.toJSONString(data));
        }
        recordTypeLogicService.createOrUpdateRecordTypeRelation(User.systemUser(tenantId), matches);
    }

    private List<ActivityTypeTemplateGet.ActivityTypeTemplateVO> loadTemplates() {
        List<ActivityTypeTemplateGet.ActivityTypeTemplateVO> data = Lists.newArrayList();
        JSONObject resource = loadResource();
        JSONArray groups = resource.getJSONArray("template_groups");
        for (int i = 0; i < groups.size(); i++) {
            JSONObject group = groups.getJSONObject(i);
            JSONArray templates = group.getJSONArray("templates");
            for (int j = 0; j < templates.size(); j++) {
                JSONObject template = templates.getJSONObject(j);
                data.add(template.toJavaObject(ActivityTypeTemplateGet.ActivityTypeTemplateVO.class));
            }
        }
        return data;
    }

    private JSONObject loadResource() {
        String json;
        try {
            File file = ResourceUtils.getFile("classpath:tpm/module_activity_type_template/activity_type_template.json");
            json = new String(Files.readAllBytes(file.toPath()));
            StringSubstitutor sub = new StringSubstitutor(ActivityTemplateI18nEnum.getInnerMap());
            json = sub.replace(json);
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("activity type template resource file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read activity type template resource failed.");
        }
        log.info("loadResource activity_type_template I18 json:{}", json);
        return JSON.parseObject(json);
    }

    private String loadModuleFieldsString(String fileName) {
        String str;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_fields/%s.json", fileName));
            str = new String(Files.readAllBytes(file.toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("field file not found." + fileName);
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read field file resource failed." + fileName);
        }
        return str;
    }

    private void initNodeObject(String tenantId, ActivityTypeTemplateGet.ActivityTypeTemplateNodeVO node) {
        boolean enableEditLayout = serviceFacade.getLayoutLogicService().isEditLayoutEnable(tenantId, node.getObjectApiName(), false);
        if (!enableEditLayout) {
            serviceFacade.getLayoutLogicService().enableEditLayout(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ);
            if (ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(node.getObjectApiName())) {
                serviceFacade.getLayoutLogicService().enableEditLayout(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
            }
        }

        if (node.getObjectRecordType().contains("promotion")) {
            addFieldsOfDefaultLayout(tenantId, node.getObjectApiName());
            promotionPolicyService.bindPluginInstance(tenantId, 1000, ApiNames.PRICE_POLICY_OBJ, "tpm_price_policy");
            promotionPolicyService.bindPluginInstance(tenantId, 1000, ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ, "tpm_price_policy_limit_account");
        }

        initLayout(tenantId, node.getObjectApiName(), node.getObjectLayoutApiName());
        initLayout(tenantId, node.getObjectApiName(), node.getObjectEditLayoutApiName());

        initRecordType(tenantId, node.getObjectApiName(), node.getObjectRecordType(), node.getObjectRecordTypeName(), node.getObjectLayoutApiName(), node.getObjectEditLayoutApiName());

        assignLayout(tenantId, "detail", node.getObjectApiName(), node.getObjectRecordType(), node.getObjectLayoutApiName());
        assignLayout(tenantId, "edit", node.getObjectApiName(), node.getObjectRecordType(), node.getObjectEditLayoutApiName());
    }

    private void addFieldsOfDefaultLayout(String tenantId, String objectApiName) {

        List<String> objs = Lists.newArrayList(ApiNames.SALES_ORDER_OBJ, ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ, ApiNames.PRICE_POLICY_OBJ);

        boolean isNeedAddActivityStoreFields = false;
        if (ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(objectApiName)) {
            objs.add(ApiNames.TPM_ACTIVITY_OBJ);
            isNeedAddActivityStoreFields = true;
        }

        objs.add(objectApiName);
        for (String objApiName : objs) {
            addPricePolicyField(tenantId, objApiName);
        }

        if (ApiNames.TPM_ACTIVITY_OBJ.equals(objectApiName) || isNeedAddActivityStoreFields) {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_STORE_OBJ);
            List<String> fieldApiNames = OBJ_FIELD_API_NAME_MAP.get(ApiNames.TPM_ACTIVITY_STORE_OBJ);
            for (String fieldApiName : fieldApiNames) {
                if (isContainFieldOfObj(describe, fieldApiName)) {
                    continue;
                }
                String fieldDescribe = loadFieldDescribeJsonFromResource(ApiNames.TPM_ACTIVITY_STORE_OBJ, fieldApiName);
                doAddField(tenantId, describe, fieldDescribe, true);
            }
        }

    }

    private void addPricePolicyField(String tenantId, String objectApiName) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, objectApiName);
        List<String> fieldApiNames = OBJ_FIELD_API_NAME_MAP.get(objectApiName);
        for (String fieldApiName : fieldApiNames) {
            if (isContainFieldOfObj(describe, fieldApiName)) {
                continue;
            }
            String fieldDescribe = loadFieldDescribeJsonFromResource(objectApiName, fieldApiName);
            doAddField(tenantId, describe, fieldDescribe, false);
        }
        clearDescribeCache(tenantId, describe.getApiName());
    }

    private boolean isContainFieldOfObj(IObjectDescribe describe, String fieldApiName) {
        if (describe == null || fieldApiName == null) {
            return false;
        }
        List<String> fields = describe.getFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        return fields.contains(fieldApiName);
    }

    private boolean isOpenPricePolicy(String tenantId) {
        return promotionPolicyService.isOpenPromotionPolicy(tenantId);
    }

    private void doAddField(String tenantId, IObjectDescribe describe, String fieldDescribe, boolean isShow) {
        User superUser = User.systemUser(tenantId);

        JSONObject field = JSON.parseObject(fieldDescribe);

        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(superUser, DETAIL_LAYOUT_TYPE, describe.getApiName());

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(Boolean.TRUE.equals(field.getBoolean("is_readonly")));
        fieldLayout.setRequired(false);
        fieldLayout.setShow(isShow);
        fieldLayout.setLayoutType(DETAIL_LAYOUT_TYPE);

        LanguageReplaceWrapper.doInChinese(() -> {
            serviceFacade.addDescribeCustomField(superUser,
                    describe.getApiName(),
                    fieldDescribe,
                    Lists.newArrayList(fieldLayout),
                    Lists.newArrayList());
        });
    }

    private String loadFieldDescribeJsonFromResource(String objectApiName, String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_fields/%s.%s.json", objectApiName, fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataBusinessException("read field describe from file cause io exception.");
        }
    }

    private void assignLayout(String tenantId, String layoutType, String objectApiName, String objectRecordType, String objectLayoutApiName) {
        RecordTypeResult old = recordTypeLogicService.findAssignedLayout(layoutType, objectApiName, User.systemUser(tenantId));

        Set<String> roleCodes = Sets.newHashSet();
        for (Object obj : old.getRole_list()) {
            Document oldRole = (Document) obj;
            roleCodes.add(oldRole.getString("roleCode"));
        }

        JSONArray roles = new JSONArray();
        for (String roleCode : roleCodes) {
            JSONObject role = new JSONObject();
            role.put("roleCode", roleCode);
            JSONObject recordLayout = new JSONObject();
            recordLayout.put("record_api_name", objectRecordType);
            recordLayout.put("layout_api_name", objectLayoutApiName);
            JSONArray recordLayouts = new JSONArray();
            recordLayouts.add(recordLayout);
            role.put("record_layout", recordLayouts);
            roles.add(role);
        }

        recordTypeLogicService.saveLayoutAssign(layoutType, objectApiName, roles.toJSONString(), User.systemUser(tenantId), "");
    }

    private void initLayout(String tenantId, String objectApiName, String layoutApiName) {
        if (Strings.isNullOrEmpty(layoutApiName)) {
            return;
        }
        ILayout old = layoutLogicService.findLayoutByApiName(User.systemUser(tenantId), layoutApiName, objectApiName);
        if (Objects.nonNull(old)) {
            return;
        }
        Layout layout = loadLayout(layoutApiName);
        layoutLogicService.createLayout(User.systemUser(tenantId), layout, true);
    }

    private Layout loadLayout(String layoutApiName) {
        String json;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_activity_type_template/layout.%s.json", layoutApiName));
            json = new String(Files.readAllBytes(file.toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("activity type template resource file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read activity type template resource failed.");
        }
        return new Layout(JSON.parseObject(json));
    }

    private void initRecordType(String tenantId, String objectApiName, String recordTypeApiName, String recordTypeLabel, String... layoutApiNames) {
        List<IRecordTypeOption> recordTypes = recordTypeLogicService.findRecordTypeOptionList(tenantId, objectApiName, false);
        boolean exists = recordTypes.stream().anyMatch(option -> option.getApiName().equals(recordTypeApiName));
        if (exists) {
            return;
        }

        RecordTypeRoleViewPojo recordType = new RecordTypeRoleViewPojo();
        recordType.setApi_name(recordTypeApiName);
        recordType.setIs_active(true);
        recordType.setLabel(recordTypeLabel);
        recordType.setDescription("");
        recordType.setConfig(Maps.newHashMap());
        recordType.setRoles(Lists.newArrayList());

        List<String> roles = Lists.newArrayList("marketingActivitiesManager", "00000000000000000000000000000006", "cityManager");
        List<RolePojo> existRoles = tPMRoleService.queryExistRole(tenantId, Sets.newHashSet(roles));
        if (CollectionUtils.isNotEmpty(existRoles)) {
            roles = roles.stream().filter(role -> existRoles.stream().anyMatch(existRole -> existRole.getRoleCode().equals(role))).collect(Collectors.toList());
        }

        for (String role : roles) {
            for (String layoutApiName : layoutApiNames) {
                RoleViewForWebPojo detailRole = new RoleViewForWebPojo();
                detailRole.setIs_default(false);
                detailRole.setIs_used(true);
                detailRole.setLayout_api_name(layoutApiName);
                detailRole.setRoleCode(role);
                recordType.getRoles().add(detailRole);
            }
        }

        RecordTypeResult createResult = recordTypeLogicService.createRecordType(tenantId, objectApiName, recordType, User.systemUser(tenantId));

        log.info("create record type result: {}", createResult);
    }

    private void clearDescribeCache(String tenantId, String apiName) {
        NotifierClient.send("describe-extra-clear-room", String.format("%s_%s", tenantId, apiName));
    }
}