package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * author: wuyx
 * description:
 * createTime: 2024/11/22 20:01
 */
public interface OrderGoodsInvalidByActivityId {

    @Data
    @ToString
    class Arg implements Serializable {
        @JSONField(name = "object_id")
        @JsonProperty(value = "object_id")
        @SerializedName("object_id")
        private String objectId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {
        private String msg;
        private String code;
    }
}