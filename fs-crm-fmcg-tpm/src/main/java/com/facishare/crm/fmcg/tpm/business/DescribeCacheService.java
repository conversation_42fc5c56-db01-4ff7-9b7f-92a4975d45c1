package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutLogicServiceImpl;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeCreate;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.retry.RetryException;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2022/4/8 下午3:37
 */
@Slf4j
@Service
public class DescribeCacheService implements IDescribeCacheService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private LayoutLogicServiceImpl layoutLogicService;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;
    @Resource
    private RecordTypeLogicServiceImpl recordTypeLogicService;

    private static final Cache<String, Map<String, IFieldDescribe>> FILED_CACHE;

    private static final String FILED_KEY_FORMAT = "%s.%s";

    private static final Lock LOCK = new ReentrantLock();

    static {
        FILED_CACHE = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .build();
    }

    @Override
    public boolean isExistField(String tenantId, String objApiName, String fieldApiName) {

        Map<String, IFieldDescribe> tenantDescribeMap = FILED_CACHE.getIfPresent(tenantId);
        String key = String.format(FILED_KEY_FORMAT, objApiName, fieldApiName);
        if (tenantDescribeMap == null) {
            LOCK.lock();
            try {
                tenantDescribeMap = FILED_CACHE.getIfPresent(tenantId);
                if (tenantDescribeMap != null) {
                    return tenantDescribeMap.get(key) != null;
                }
                tenantDescribeMap = new HashMap<>();
                DescribeResult describeResult = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), objApiName, false, null);
                tenantDescribeMap.put(key, describeResult.getObjectDescribe().getFieldDescribeMap().get(fieldApiName));
                FILED_CACHE.put(tenantId, tenantDescribeMap);
            } finally {
                LOCK.unlock();
            }
        } else {
            if (!tenantDescribeMap.containsKey(key)) {
                LOCK.lock();
                try {
                    if (tenantDescribeMap.containsKey(key)) {
                        return tenantDescribeMap.get(key) != null;
                    }
                    DescribeResult describeResult = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), objApiName, false, null);
                    tenantDescribeMap.put(key, describeResult.getObjectDescribe().getFieldDescribeMap().get(fieldApiName));
                } finally {
                    LOCK.unlock();
                }
            }
        }

        return tenantDescribeMap.get(key) != null;
    }

    @Override
    public void initFields(String tenantId, String objectApiName, List<String> fields) {
        LanguageReplaceWrapper.doInChinese(() -> {
            ParallelUtils.createParallelTask().submit(() -> {
                User sys = User.systemUser(tenantId);
                fields.forEach(field -> {
                    if (!isExistField(tenantId, objectApiName, field)) {
                        try {
                            serviceFacade.addDescribeCustomField(sys, objectApiName, loadModuleFieldsString(objectApiName + "." + field), null, null);
                        } catch (Exception e) {
                            log.info("add field err.", e);
                        }
                    }
                });
            }).run();
        });
    }

    @Override
    public void updateFields(String tenantId, String objectApiName, List<String> fields) {

    }

    @Override
    public void saveOrUpdateFields(String tenantId, String objectApiName, List<String> fields) {

    }

    @Override
    public void initLayout(String tenantId, String objectApiName, String layoutApiName) {
        if (Strings.isNullOrEmpty(layoutApiName)) {
            return;
        }
        ILayout old = layoutLogicService.findLayoutByApiName(User.systemUser(tenantId), layoutApiName, objectApiName);
        if (Objects.nonNull(old)) {
            return;
        }
        Layout layout = loadLayout(layoutApiName);
        layoutLogicService.createLayout(User.systemUser(tenantId), layout, true);
    }

    @Override
    public void createDefaultDescribe(String tenantId, List<String> objectApiNames) {
        for (String objectApiName : objectApiNames) {
            try {

                LanguageReplaceWrapper.doInChinese(() -> {
                    PaasDescribeCreate.Arg arg = buildDescribeArg(objectApiName);
                    PaasDescribeCreate.Result result = paasDescribeProxy.create(Integer.parseInt(tenantId), -10000, arg);
                    if (result.getErrCode() != 0 && result.getErrCode() != 320001401) {
                        log.error("init obj error,objectApiName:{},error msg:{}", objectApiName, result.getErrMessage());
                    }
                });
                if (ApiNames.TPM_ACTIVITY_OBJ.equals(objectApiName)) {
                    IObjectDescribe redPacketRecord = serviceFacade.findObject(tenantId, ApiNames.RED_PACKET_RECORD_OBJ);
                    if (Objects.nonNull(redPacketRecord)) {
                        initFields(tenantId, ApiNames.RED_PACKET_RECORD_OBJ, Lists.newArrayList("activity_id"));
                    }
                }
            } catch (Exception ex) {
                log.error("create describe has exception", ex);
            }
        }
    }

    @Override
    public void assignLayout(String tenantId, String layoutType, String objectApiName, String objectRecordType, String objectLayoutApiName) {
        try {
            ILayout layout = layoutLogicService.findLayoutByApiName(User.systemUser(tenantId), objectLayoutApiName, objectApiName);
            if (Objects.isNull(layout)) {
                return;
            }

            RecordTypeResult old = recordTypeLogicService.findAssignedLayout(layoutType, objectApiName, User.systemUser(tenantId));

            Set<String> roleCodes = Sets.newHashSet();
            for (Object obj : old.getRole_list()) {
                Document oldRole = (Document) obj;
                roleCodes.add(oldRole.getString("roleCode"));
            }

            JSONArray roles = new JSONArray();
            for (String roleCode : roleCodes) {
                JSONObject role = new JSONObject();
                role.put("roleCode", roleCode);
                JSONObject recordLayout = new JSONObject();
                recordLayout.put("record_api_name", objectRecordType);
                recordLayout.put("layout_api_name", objectLayoutApiName);
                JSONArray recordLayouts = new JSONArray();
                recordLayouts.add(recordLayout);
                role.put("record_layout", recordLayouts);
                roles.add(role);
            }

            recordTypeLogicService.saveLayoutAssign(layoutType, objectApiName, roles.toJSONString(), User.systemUser(tenantId), "");
        } catch (Exception e) {
            log.error("assignLayout error", e);
        }
    }

    protected PaasDescribeCreate.Arg buildDescribeArg(String apiName) {
        PaasDescribeCreate.Arg arg = new PaasDescribeCreate.Arg();
        arg.setActive(true);
        arg.setIncludeLayout(true);

        try {
            arg.setJsonData(describe(apiName));
            arg.setJsonLayout(layout(apiName));
            arg.setJsonListLayout(mobileListLayout(apiName));
        } catch (IOException ex) {
            throw new MetaDataBusinessException("init describe error,", ex);
        }

        arg.setLayoutType("detail");
        return arg;
    }

    private String describe(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm/module_obj/%s.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private String layout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm/module_obj/%sDetailLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private String mobileListLayout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:tpm/module_obj/%sMobileLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private Layout loadLayout(String layoutApiName) {
        String json;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_activity_type_template/layout.%s.json", layoutApiName));
            json = new String(Files.readAllBytes(file.toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("activity type template resource file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read activity type template resource failed.");
        }
        return new Layout(JSON.parseObject(json));
    }


    private String loadModuleFieldsString(String fileName) {

        String str;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_fields/%s.json", fileName));
            str = new String(Files.readAllBytes(file.toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("field file not found." + fileName);
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read field file resource failed." + fileName);
        }
        return str;
    }

}
