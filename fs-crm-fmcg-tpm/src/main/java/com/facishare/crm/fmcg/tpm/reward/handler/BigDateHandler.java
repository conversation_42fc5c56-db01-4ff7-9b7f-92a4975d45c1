package com.facishare.crm.fmcg.tpm.reward.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.adapter.ReceiveMoneyService;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.TransferDetailStatusEnum;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryTransferDetailForReceipts;
import com.facishare.crm.fmcg.common.apiname.AccountFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.FMCGSerialNumberFields;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.tpm.business.dto.GetRewardDetailDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BizCodePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BizCodeStatusEnum;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardBase;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.service.AdvancedRewardLimitService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/3/27 17:41
 */
//IgnoreI18nFile
@Component
@Slf4j
public class BigDateHandler extends ActivityRewardBase implements ActivityRewardHandler<String, Void> {


    @Resource
    private ReceiveMoneyService receiveMoneyService;

    @Resource
    private AdvancedRewardLimitService advancedRewardLimitService;

    @Override
    public Void handle(String bizCodeId) {
        log.info("start bizCodeId: {}", bizCodeId);
        BizCodePO bizCodePO = bizCodeDAO.getByBizCode(bizCodeId);
        //必须和 scan时候的lock保持一致
        List<RLock> rLockList = Lists.newArrayList();
        try {
            if (bizCodePO == null) {
                log.info("bizCodePO is null");
                return null;
            }
            bizCodePO.getRelatedBizIds().forEach(id -> {
                RLock rLock = tryLock(String.format(ScanCodeActionConstants.CODE_LOCK_KEY, id));
                rLockList.add(rLock);
            });
            if (bizCodePO.getStatus() == BizCodeStatusEnum.USED.code()) {
                log.info("bizCode is used");
                return null;
            }
            String tenantId = bizCodePO.getTenantId();
            Map<String, String> code2ActivityMap = (Map<String, String>) bizCodePO.getBizDataMap().get("code2activityMap");
            Map<String, String> code2StoreMap = (Map<String, String>) bizCodePO.getBizDataMap().get("code2StoreMap");
            List<IObjectData> rewardDetails = JSON.parseArray((String) bizCodePO.getBizDataMap().get(ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ), Map.class).stream().map(ObjectData::new).collect(Collectors.toList());
            List<IObjectData> salesRecords = JSON.parseArray((String) bizCodePO.getBizDataMap().get(ApiNames.STORE_PROMOTION_RECORD_OBJ), Map.class).stream().map(ObjectData::new).collect(Collectors.toList());
            Integer receiverTenantId = (Integer) bizCodePO.getBizDataMap().get("receiverTenantId");
            boolean jumpValidateBizCodePaid = Boolean.TRUE.equals(bizCodePO.getBizDataMap().get("jumpValidateBizCodePaid"));
            if (!jumpValidateBizCodePaid && receiverTenantId != null && !validateBizCodePaid(receiverTenantId, bizCodeId)) {
                log.info("receiverTenantId is not paid.receiverTenantID:{}", receiverTenantId);
                throw new MetaDataBusinessException("receiverTenantId is not paid");
            }
            saveSalesRecord(tenantId, rewardDetails, salesRecords);
            List<GetRewardDetailDTO> rewardDetailDTOS = new ArrayList<>();
            Map<IObjectData, List<IObjectData>> redPacketDetails = new HashMap<>();
            for (String code : code2ActivityMap.keySet()) {
                String activityId = code2ActivityMap.get(code);
                String storeId = getStoreId(code2StoreMap, code);
                IObjectData store = serviceFacade.findObjectData(User.systemUser(tenantId), storeId, ApiNames.ACCOUNT_OBJ);
                IObjectData serialNumberObj = serviceFacade.findObjectData(User.systemUser(tenantId), code, ApiNames.FMCG_SERIAL_NUMBER_OBJ);
                if (!advancedRewardLimitService.overlimit(tenantId, activityId, store.get(AccountFields.MENGNIU_STORE_TYPE, String.class), storeId, serialNumberObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class))) {
                    rewardDetailDTOS = getRewardDetails(tenantId, activityId, code, bizCodePO.getBizCode(), storeId);
                } else {
                    log.info("code:{} is limit to reward.", code);
                }
            }
            if (CollectionUtils.isNotEmpty(rewardDetailDTOS)) {
                rewardDetailDTOS.forEach(detail -> {
                    if (CollectionUtils.isNotEmpty(detail.getActivityRewardDetails())) {
                        rewardDetails.addAll(detail.getActivityRewardDetails());
                    }
                    if (Objects.nonNull(detail.getRedPacket())) {
                        redPacketDetails.put(detail.getRedPacket(), detail.getRedPacketDetails());
                    }
                });
            }
            saveRewardData(tenantId, bizCodeId, rewardDetails, redPacketDetails, null, null, "产品代售");
        } catch (Exception e) {
            rewardExceptionRecordService.writeRecord(bizCodePO.getTenantId(), bizCodePO.getBizCode(), "产品代售", e.getMessage(), "1");
            throw e;
        } finally {
            rLockList.forEach(this::unlock);
        }
        return null;
    }

    private String getStoreId(Map<String, String> code2StoreMap, String code) {
        if (MapUtils.isEmpty(code2StoreMap)) {
            return null;
        }
        String storeCode = code2StoreMap.get(code);
        if (Strings.isNullOrEmpty(storeCode)) {
            return null;
        }
        // Format is tenantId#storeId, extract the storeId part
        String[] parts = storeCode.split("#");
        return parts.length > 1 ? parts[1] : null;
    }


    private boolean validateBizCodePaid(Integer tenantId, String bizCode) {
        if (bizCode.equals("SP:65D5CD6E9224D5DC8EC5BBCB")) {
            return true;
        }
        QueryTransferDetailForReceipts.Arg arg = new QueryTransferDetailForReceipts.Arg();
        arg.setBusinessId(bizCode);
        arg.setTenantId(String.valueOf(tenantId));
        TransferDetail transfer = receiveMoneyService.queryTransferDetailForReceipts(arg).getTransferDetail();
        return TransferDetailStatusEnum.SUCCESS.codes().contains(transfer.getStatus());
    }

    protected void saveSalesRecord(String tenantId, List<IObjectData> rewardDetails, List<IObjectData> salesRecords) {
        User user = User.systemUser(tenantId);
        List<IObjectData> newRewards = needCreateDataList(user, rewardDetails);
        List<IObjectData> newSales = needCreateDataList(user, salesRecords);

        transactionProxy.run(() -> {
            if (CollectionUtils.isNotEmpty(newRewards)) {
                serviceFacade.bulkSaveObjectData(newRewards, user);
            }
            if (CollectionUtils.isNotEmpty(newSales)) {
                serviceFacade.bulkSaveObjectData(newSales, user);
            }
        });
        rewardDetails.clear();
        salesRecords.clear();
    }
}
