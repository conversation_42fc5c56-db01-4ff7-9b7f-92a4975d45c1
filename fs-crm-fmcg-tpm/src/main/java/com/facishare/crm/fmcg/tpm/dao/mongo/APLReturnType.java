package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.google.common.collect.Lists;

import java.util.List;

public enum APLReturnType {
    LIST("List", Lists.newArrayList(List.class));
    private final String returnType;
    private final List<Class<?>> classTypes;

    APLReturnType(String returnType, List<Class<?>> classType) {
        this.returnType = returnType;
        this.classTypes = classType;
    }

    public List<Class<?>> getClassTypes() {
        return classTypes;
    }

    public String getReturnType() {
        return returnType;
    }
}
