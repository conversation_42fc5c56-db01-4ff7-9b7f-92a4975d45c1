package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDescribeCacheService;
import com.facishare.paas.metadata.api.IObjectData;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

public interface OrderGoodsBaseService {

    default boolean isFromTPM(Map<String, Object> extraData) {
        if (Objects.isNull(extraData)) {
            return false;
        }
        if (!extraData.isEmpty() && extraData.containsKey(TPMTriggerActionService.REQUEST_FROM)) {
            Object requestFrom = extraData.get(TPMTriggerActionService.REQUEST_FROM);
            return TPMTriggerActionService.REQUEST_APP_NAME.equals(requestFrom);
        }
        return false;
    }

    default boolean judgedIsOrderGoodsPromotionData(IObjectData objectData) {
        if (Objects.isNull(objectData)) {
            return false;
        }

        if (Objects.isNull(objectData.get(TPMActivityFields.ORDER_GOODS_MEETING_FLAG))) {
            return false;
        }

        return objectData.get(TPMActivityFields.ORDER_GOODS_MEETING_FLAG, Boolean.class, false);
    }

}
