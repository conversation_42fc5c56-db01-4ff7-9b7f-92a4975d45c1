package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * author: wuyx
 * description:
 * createTime: 2024/11/22 20:01
 */
public interface OrderGoodsPromotionPolicyEdit {

    @Data
    @ToString
    class Arg implements Serializable {

        private String tenantId;

        private String userId;

        @JSONField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;

        @JSONField(name = "object_id")
        @JsonProperty(value = "object_id")
        @SerializedName("object_id")
        private String objectId;

        @JSONField(name = "product_promotion_json")
        @JsonProperty(value = "product_promotion_json")
        @SerializedName("product_promotion_json")
        private String productPromotionJson;

        @JSONField(name = "recharge_promotion_json")
        @JsonProperty(value = "recharge_promotion_json")
        @SerializedName("recharge_promotion_json")
        private String rechargePromotionJson;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private Boolean enable;
        private String msg;

    }
}