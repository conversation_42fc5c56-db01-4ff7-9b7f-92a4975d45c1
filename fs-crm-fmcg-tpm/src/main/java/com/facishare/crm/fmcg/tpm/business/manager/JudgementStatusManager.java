package com.facishare.crm.fmcg.tpm.business.manager;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.enums.StandardStatusType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class JudgementStatusManager {

    /**
     * 设置产品和物料陈列结果
     */
    public void setProductAndMaterialDisplayStatus(IObjectData proofDetail, IObjectData detailObj,
                                                    Map<String, TPMDisplayReportService.ProductOrMaterialAchieveStatusDTO> productOrMaterialAchieveStatusMap) {
        String productDisplayStatus = "";
        String materialDisplayStatus = "";

        TPMDisplayReportService.ProductOrMaterialAchieveStatusDTO statusDTO = productOrMaterialAchieveStatusMap.get(detailObj.getId());
        if (Objects.nonNull(statusDTO)) {
            productDisplayStatus = StringUtils.isNotBlank(statusDTO.getProductAchieveStatus())
                    ? statusDTO.getProductAchieveStatus() : "";
            materialDisplayStatus = StringUtils.isNotBlank(statusDTO.getMaterialAchieveStatus())
                    ? statusDTO.getMaterialAchieveStatus() : "";
        }

        proofDetail.set(TPMActivityProofDetailFields.PRODUCT_DISPLAY_STATUS, productDisplayStatus);
        proofDetail.set(TPMActivityProofDetailFields.MATERIAL_DISPLAY_STATUS, materialDisplayStatus);
    }

    /**
     * 计算并设置系统判定结果
     */
    public void setSystemJudgmentStatus(IObjectData proofDetail, String tenantId, IObjectData detailObj,
                                         Map<String, IObjectData> rioSpecialDisplayFormDTOMap, Double aiNumber, Double aiFaceNumber, Double aiVisibleNumber, Double amountAmountStandard) {
        String productDisplayStatus = proofDetail.get(TPMActivityProofDetailFields.PRODUCT_DISPLAY_STATUS, String.class, "");
        String materialDisplayStatus = proofDetail.get(TPMActivityProofDetailFields.MATERIAL_DISPLAY_STATUS, String.class, "");
        String productItemStandardId = detailObj.get(TPMActivityDetailFields.PRODUCT_ITEM_STANDARD_ID, String.class, "");
        String materialItemStandardId = detailObj.get(TPMActivityDetailFields.MATERIAL_STANDARD_REQUIREM_ID, String.class, "");

        // 确定标准状态类型
        StandardStatusType standardStatus = determineStandardStatusType(productItemStandardId, materialItemStandardId);

        String systemJudgmentStatus = "";
        String judgmentMode = TPMActivityProofDetailFields.ONLY_STANDARD;
        if (aiNumber.compareTo(amountAmountStandard) >= 0) {

            systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;

            if (TPMGrayUtils.aiDisplayRioMode(tenantId)) {
                judgmentMode = TPMActivityProofDetailFields.METRIC_AND_STANDARD;
                systemJudgmentStatus = determineRioSystemJudgmentStatus(rioSpecialDisplayFormDTOMap, detailObj, aiFaceNumber, aiVisibleNumber);
                //如果没有额外的衡量标准，这里还是赋值达标状态
                if (StringUtils.isEmpty(systemJudgmentStatus)) {
                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                    judgmentMode = TPMActivityProofDetailFields.ONLY_STANDARD;
                }
            }
            if (!TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS.equals(systemJudgmentStatus)) {
                systemJudgmentStatus = handleAiNumberMeetsStandard(tenantId, systemJudgmentStatus, productDisplayStatus, materialDisplayStatus, standardStatus);
            }
        } else if (aiNumber.compareTo(0.0) == 0) {
            systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (aiNumber.compareTo(amountAmountStandard) < 0) {
            systemJudgmentStatus = handleAiNumberBelowStandard(tenantId, systemJudgmentStatus, productDisplayStatus, materialDisplayStatus, standardStatus);
        }
        proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_STATUS, systemJudgmentStatus);
        proofDetail.set(TPMActivityProofDetailFields.SYSTEM_JUDGMENT_MODE__C, judgmentMode);
    }

    public String handleAiNumberMeetsStandard(String tenantId, String systemJudgmentStatus,
                                               String productDisplayStatus, String materialDisplayStatus,
                                               StandardStatusType standardStatus) {

        switch (standardStatus) {
            case BOTH_STANDARD:
                if (StringUtils.isBlank(productDisplayStatus) || StringUtils.isBlank(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus) && TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                } else if (TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus) || TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case PRODUCT_STANDARD_ONLY:
                if (StringUtils.isBlank(productDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                } else if (TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case MATERIAL_STANDARD_ONLY:
                if (StringUtils.isBlank(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.PASS_STATUS;
                } else if (TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case NO_STANDARD:
            default:
                break;
        }
        return systemJudgmentStatus;
    }

    /**
     * 处理AI数量低于标准数量的情况
     */
    public String handleAiNumberBelowStandard(
            String tenantId, String systemJudgmentStatus, String productDisplayStatus, String materialDisplayStatus,
            StandardStatusType standardStatus) {
        switch (standardStatus) {
            case NO_STANDARD:
                systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                break;
            case BOTH_STANDARD:
                if (TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus) && TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus) || TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case PRODUCT_STANDARD_ONLY:
                if (StringUtils.isBlank(productDisplayStatus) || TPMActivityProofDetailFields.FAIL_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(productDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;
            case MATERIAL_STANDARD_ONLY:
                if (StringUtils.isBlank(materialDisplayStatus) || TPMActivityProofDetailFields.FAIL_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMActivityProofDetailFields.FAIL_STATUS;
                } else if (TPMActivityProofDetailFields.PASS_STATUS.equals(materialDisplayStatus)) {

                    systemJudgmentStatus = TPMGrayUtils.partialPassConvertToFail(tenantId) ? TPMActivityProofDetailFields.FAIL_STATUS : TPMActivityProofDetailFields.PARTIAL_PASS_STATUS;
                }
                break;

            default:
                break;
        }

        return systemJudgmentStatus;
    }

    /**
     * 确定标准状态类型
     */
    public StandardStatusType determineStandardStatusType(String productItemStandardId, String materialItemStandardId) {
        if (StringUtils.isBlank(productItemStandardId) && StringUtils.isBlank(materialItemStandardId)) {
            return StandardStatusType.NO_STANDARD;
        } else if (StringUtils.isNotBlank(productItemStandardId) && StringUtils.isNotBlank(materialItemStandardId)) {
            return StandardStatusType.BOTH_STANDARD;
        } else if (StringUtils.isNotBlank(productItemStandardId) && StringUtils.isBlank(materialItemStandardId)) {
            return StandardStatusType.PRODUCT_STANDARD_ONLY;
        } else {
            return StandardStatusType.MATERIAL_STANDARD_ONLY;
        }
    }

    /**
     * 确定RIO系统的判定结果
     */
    public String determineRioSystemJudgmentStatus(Map<String, IObjectData> rioSpecialDisplayFormDTOMap,
                                                    IObjectData detailObj, Double aiFaceNumber, Double aiVisibleNumber) {
        String activityDetailId = ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ.equals(detailObj.getDescribeApiName()) ?
                detailObj.get(TPMActivityAgreementDetailFields.ACTIVITY_DETAIL_ID, String.class) : detailObj.getId();
        IObjectData displayProjectJudgmentStandardObj = rioSpecialDisplayFormDTOMap.get(activityDetailId);
        BigDecimal aiVisibleNumberDecimal = BigDecimal.valueOf(aiVisibleNumber);

        if (Objects.nonNull(displayProjectJudgmentStandardObj)) {
            Double targetValue = displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.TARGET_VALUE, Double.class, 0.0);
            Double unmetValue = displayProjectJudgmentStandardObj.get(DisplayProjectJudgmentStandardFields.UNMET_VALUE, Double.class, 0.0);

            if (BigDecimal.ZERO.compareTo(aiVisibleNumberDecimal) == 0) {
                return recorrectStatus(aiFaceNumber, targetValue, unmetValue);
            } else {
                return recorrectStatus(aiVisibleNumber, targetValue, unmetValue);
            }

        }
        return "";
    }

    public String recorrectStatus(Double currentValue, Double targetValue, Double unmetValue) {
        if (currentValue.compareTo(targetValue) >= 0) {
            return TPMActivityProofDetailFields.PASS_STATUS;
        } else if (currentValue.compareTo(unmetValue) < 0) {
            return TPMActivityProofDetailFields.FAIL_STATUS;
        } else if (currentValue.compareTo(unmetValue) >= 0 && currentValue.compareTo(targetValue) < 0) {
            return TPMActivityProofDetailFields.PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS;
        }
        return "";
    }
}
