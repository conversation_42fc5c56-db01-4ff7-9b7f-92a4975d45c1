package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.api.withdraw.*;

public interface IWithdrawService {

    /**
     * 红包统计
     *
     * @param arg
     * @return result
     */
    Dashboard.Result dashboard(Dashboard.Arg arg);

    /**
     * 红包记录列表
     *
     * @param arg
     * @return result
     */
    QueryRedPacketRecords.Result queryRedPacketRecords(QueryRedPacketRecords.Arg arg);

    /**
     * 提现记录列表
     *
     * @param arg
     * @return result
     */
    QueryWithdrawRecords.Result queryWithdrawRecords(QueryWithdrawRecords.Arg arg);

    /**
     * 预览提现
     *
     * @param arg
     * @return result
     */
    PreviewWithdraw.Result previewWithdraw(PreviewWithdraw.Arg arg);

    /**
     * 红包提现
     *
     * @param arg
     * @return result
     */
    SubmitWithdraw.Result submitWithdraw(SubmitWithdraw.Arg arg);

    /**
     * 红包人员认证 - auth_status  1 认证 0 未认证
     *
     * @param arg
     * @return result
     */
    PersonAuthIdCard.Result personAuthIdCard(PersonAuthIdCard.Arg arg);

    /**
     * 人员提示协议 -  alertRecord
     *
     * @param arg
     * @return result
     */
    PersonAlertRecord.Result personAlertRecord(PersonAlertRecord.Arg arg);

    /**
     * 红包异常重发 （实时发放的、用户信息导致的异常）
     *
     * @param arg
     * @return result
     */
    RedPacketRepeat.Result redPacketRepeat(RedPacketRepeat.Arg arg);
}
