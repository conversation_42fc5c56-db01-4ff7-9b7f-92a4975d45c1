package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.BuryService;
import com.facishare.crm.fmcg.tpm.service.abstraction.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.annotation.SendLog;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.*;
import com.facishare.crm.fmcg.tpm.web.designer.IActivityNodeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IUseScopeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityTypeService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ITPMFieldService;
import com.facishare.crm.fmcg.tpm.web.utils.ListParameterUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasLayoutProxy;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.fmcg.framework.http.contract.paas.layout.PaasFindAssignedLayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 17:42
 */
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class ActivityTypeService extends BaseService implements IActivityTypeService {

    @Resource
    private ActivityTypeDAO activityTypeDAO;
    @Resource
    private IActivityTypeManager activityTypeManager;
    @Resource
    private IActivityNodeManager activityNodeManager;
    @Resource
    private IUseScopeManager useScopeManager;
    @Resource
    private StoreBusiness storeBusiness;
    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;
    @Resource(name = "tpmRoleService")
    private IRoleService roleService;
    @Resource
    private PluginInstanceService pluginService;

    @Resource
    private PaasLayoutProxy paasLayoutProxy;

    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private IDealerActivityCostRebateService dealerActivityCostRebateService;
    @Resource
    private ITPMFieldService tpmFieldService;
    @Resource
    private ActivityTypeTemplateService activityTypeTemplateService;


    private static final String PLUGIN_NAME = "tpm_store_write_off";
    private static final String FUND_ACCOUNT_PLUGIN_NAME = "activity_dealer_cost_relation_rebate_validate";

    @Override
    public SetActivityTypeStatus.Result setStatus(SetActivityTypeStatus.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        activityTypeDAO.setStatus(context.getTenantId(), context.getEmployeeId(), arg.getId(), arg.getStatus());
        return SetActivityTypeStatus.Result.builder().activityType(ActivityTypeVO.fromPO(activityTypeDAO.get(context.getTenantId(), arg.getId()))).build();
    }

    @SendLog(message = "新建活动类型")
    @Override
    public AddActivityType.Result add(AddActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        activityTypeManager.basicInformationValidation(context.getTenantId(), arg.getActivityType());
        activityNodeManager.nodeListValidation(context.getTenantId(), arg.getActivityType().getActivityNodeList(), arg.getActivityType());

        log.info("activity type after node validation : {}", JSON.toJSONString(arg.getActivityType()));

        arg.getActivityType().setVersion(0);
        arg.getActivityType().setScopeDescription(useScopeManager.calculateScopeDescription(context.getTenantId(), arg.getActivityType().getEmployeeIds(), arg.getActivityType().getDepartmentIds(), arg.getActivityType().getRoleIds()));
        arg.getActivityType().setAllEmployeeIds(useScopeManager.calculateScope(context.getTenantId(), arg.getActivityType().getEmployeeIds(), arg.getActivityType().getDepartmentIds(), arg.getActivityType().getRoleIds()));


        initArgException(arg);
        String id = activityTypeDAO.add(context.getTenantId(), context.getEmployeeId(), ActivityTypePO.fromVO(arg.getActivityType()));
        activityTypeManager.publishSyncActivityTypeFieldTask(context.getTenantId());

        asyncTpmActivityTypeLogV2(context, arg);

        //判断是否有门店费用核销节点，
        checkActivityStoreWriteOffNode(context.getTenantId(), arg.getActivityType(), null);
        //检查是否开启上账能力
        checkActivityDealerCostNode(context.getTenantId(), arg.getActivityType());
        activityTypeManager.createReference(context.getTenantId(), arg.getActivityType());
        return AddActivityType.Result.builder().activityType(ActivityTypeVO.fromPO(activityTypeDAO.get(context.getTenantId(), id))).build();
    }

    private void checkActivityStoreWriteOffNode(String tenantId, ActivityTypeVO activityType, ActivityTypePO old) {
        ActivityNodeVO activityNodeVO = activityType.getActivityNodeList().stream().filter(v -> NodeType.STORE_WRITE_OFF.value().equals(v.getType())).findFirst().orElse(null);
        if (Objects.isNull(activityNodeVO)) {
            return;
        }
        if (Objects.nonNull(old)) {
            //改动老的 apiName
            verifyStoreWriteOffConfig(tenantId, old);
        }
        ActivityStoreWriteOffConfigVO config = activityNodeVO.getActivityStoreWriteOffConfig();
        //依据的apiName
        String apiName = config.getStoreWriteOffSourceConfig().getApiName();
        //查询对象是否已绑定插件, 否，则add
        if (!pluginService.findPluginUnit(tenantId, apiName, PLUGIN_NAME)) {
            try {
                pluginService.addPluginUnit(Integer.valueOf(tenantId), -10000, apiName, PLUGIN_NAME);
            } catch (Exception e) {
                log.error("add storeWriteOff plugin fail, e:", e);
            }
        }
    }

    private void checkActivityDealerCostNode(String tenantId, ActivityTypeVO activityType) {
        ActivityNodeVO activityNodeVO = activityType.getActivityNodeList().stream().filter(v -> NodeType.WRITE_OFF.value().equals(v.getType())).findFirst().orElse(null);
        if (Objects.isNull(activityNodeVO)) {
            return;
        }

        ActivityWriteOffConfigVO config = activityNodeVO.getActivityWriteOffConfig();
        if (!Boolean.TRUE.equals(config.getChargeUpConfig().getChargeUpAccountStatus())) {
            return;
        }
        ParallelUtils.createParallelTask().submit(() -> checkDealerCostField(tenantId)).run();
        if (!dealerActivityCostRebateService.isOpenRebate(tenantId)) {
            return;
        }
        ParallelUtils.createParallelTask().submit(() -> checkRebateFiled(tenantId)).run();
        //依据的apiName
        String apiName = ApiNames.REBATE_OBJ;
        //查询对象是否已绑定插件, 否，则add
        if (!pluginService.findPluginUnit(tenantId, apiName, FUND_ACCOUNT_PLUGIN_NAME)) {
            try {
                pluginService.addPluginUnit(Integer.valueOf(tenantId), -10000, apiName, FUND_ACCOUNT_PLUGIN_NAME);
            } catch (Exception e) {
                log.error("add writeOff plugin fail, e:", e);
            }
        }
    }

    private void checkDealerCostField(String tenantId) {
        try {
            List<String> fieldsName = Lists.newArrayList(TPMDealerActivityCostFields.GOODS_PAY_USAGE, TPMDealerActivityCostFields.GOODS_PAY_NUMBER, TPMDealerActivityCostFields.CASH_USAGE, TPMDealerActivityCostFields.ENTER_ACCOUNT_WAY, TPMDealerActivityCostFields.EFFECTIVE_PERIOD, TPMDealerActivityCostFields.EFFECTIVE_DATE, TPMDealerActivityCostFields.EXPIRING_DATE, TPMDealerActivityCostFields.COST_CASHING_QUANTITY, TPMDealerActivityCostFields.ENTER_ACCOUNT, TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID);
            List<String> readOnlyFiled = Lists.newArrayList(TPMDealerActivityCostFields.GOODS_PAY_NUMBER, TPMDealerActivityCostFields.CASHING_FUND_ACCOUNT_ID);
            List<String> showFiled = Lists.newArrayList(TPMDealerActivityCostFields.GOODS_PAY_USAGE, TPMDealerActivityCostFields.COST_CASHING_QUANTITY);


            IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_DEALER_ACTIVITY_COST);
            for (String fieldApiName : fieldsName) {
                try {
                    if (tpmFieldService.containsFiled(describe, fieldApiName)) {
                        continue;
                    }
                    String fieldDescribe = tpmFieldService.loadFieldDescribeJsonFromResource(ApiNames.TPM_DEALER_ACTIVITY_COST, fieldApiName);
                    tpmFieldService.addField(tenantId, describe, fieldDescribe, showFiled.contains(fieldApiName), readOnlyFiled.contains(fieldApiName));
                } catch (Exception ex) {
                    log.error("add field error,", ex);
                }
            }

        } catch (Exception ex) {
            log.error("checkRebateFiled fail,", ex);
        }
    }

    private void checkRebateFiled(String tenantId) {
        try {

            List<String> fieldsName = Lists.newArrayList("tpm_activity_id", "tpm_dealer_activity_cost_id");
            IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.REBATE_OBJ);
            for (String fieldApiName : fieldsName) {
                try {
                    if (tpmFieldService.containsFiled(describe, fieldApiName)) {
                        continue;
                    }
                    String fieldDescribe = tpmFieldService.loadFieldDescribeJsonFromResource(ApiNames.REBATE_OBJ, fieldApiName);
                    tpmFieldService.addField(tenantId, describe, fieldDescribe, false, true);
                } catch (Exception ex) {
                    log.error("add field error,", ex);
                }
            }

        } catch (Exception ex) {
            log.error("checkRebateFiled fail,", ex);
        }
    }

    private void verifyStoreWriteOffConfig(String tenantId, ActivityTypePO old) {
        String oldApiName = old.getActivityNodes().stream().filter(v -> v.getActivityStoreWriteOffConfig() != null).map(v -> v.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getApiName()).findFirst().orElse(null);
        log.info("oldApiName is {}", oldApiName);
        if (Strings.isNullOrEmpty(oldApiName)) {
            return;
        }
        List<ActivityTypePO> activityTypePOS = activityTypeDAO.all(tenantId, true);
        boolean exist = false;
        for (ActivityTypePO activityTypePO : activityTypePOS) {
            ActivityNodeEntity activityNodeEntity = activityTypePO.getActivityNodes().stream().filter(v -> verifyActivityStoreWriteOffConfig(v, oldApiName)).findFirst().orElse(null);
            if (Objects.nonNull(activityNodeEntity)) {
                exist = true;
                break;
            }
        }
        log.info("exist boolean status is {}", exist);
        if (!exist) {
            //没有依据的了，
            pluginService.deletePluginUnit(tenantId, oldApiName, PLUGIN_NAME);
        }
    }

    private boolean verifyActivityStoreWriteOffConfig(ActivityNodeEntity activityNodeEntity, String oldApiName) {
        return activityNodeEntity.getActivityStoreWriteOffConfig() != null && activityNodeEntity.getActivityStoreWriteOffConfig().getStoreWriteOffSourceConfig().getApiName().equals(oldApiName);
    }


    private void asyncTpmActivityTypeLogV2(ApiContext context, AddActivityType.Arg arg) {

        for (ActivityNodeVO node : arg.getActivityType().getActivityNodeList()) {
            NodeType nodeType = NodeType.of(node.getType());
            switch (nodeType) {
                case AGREEMENT:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.AGREEMENT, BuryOperation.CREATE, true);
                    break;
                case PROOF:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.PROOF, BuryOperation.CREATE, true);
                    break;
                case AUDIT:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.AUDIT, BuryOperation.CREATE, true);
                    break;
                case STORE_WRITE_OFF:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.STORE_WRITE_OFF, BuryOperation.CREATE, true);
                    break;
                case COST_ASSIGN:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.COST_ASSIGN, BuryOperation.CREATE, true);
                    break;
                case WRITE_OFF:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.WRITE_OFF, BuryOperation.CREATE, true);
                    break;
                case CUSTOM:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.CUSTOM, BuryOperation.CREATE, true);
                    break;
                case PLAN:
                    BuryService.asyncTpmLog(Integer.valueOf(context.getTenantId()), context.getEmployeeId(), BuryModule.ACTIVITY_TYPE_NODE.PLAN, BuryOperation.CREATE, true);
                    break;
                default:
                    break;
            }
        }
    }


    @SendLog(message = "编辑活动类型")
    @Override
    public EditActivityType.Result edit(EditActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        ActivityTypePO old = activityTypeDAO.get(context.getTenantId(), arg.getActivityType().getId());

        if (old == null) {
            throw new ValidateException("activity type not found.");
        }
        if (old.getVersion() != arg.getActivityType().getVersion()) {
            throw new ValidateException("version validate fail.");
        }
        if (PreActivityType.TYPE_ACTIVITY_ORDER_GOODS.apiName().equals(old.getApiName())) {
            throw new ValidateException("orderGoods activity type is not allow edit.");
        }

        activityTypeManager.fieldEditAbleValidation(context.getTenantId(), old.getId().toString(), old, arg.getActivityType());

        activityTypeManager.basicInformationValidation(context.getTenantId(), arg.getActivityType().getId(), arg.getActivityType());
        activityNodeManager.nodeListValidation(context.getTenantId(), arg.getActivityType().getActivityNodeList(), arg.getActivityType());

        arg.getActivityType().setScopeDescription(useScopeManager.calculateScopeDescription(context.getTenantId(), arg.getActivityType().getEmployeeIds(), arg.getActivityType().getDepartmentIds(), arg.getActivityType().getRoleIds()));
        arg.getActivityType().setAllEmployeeIds(useScopeManager.calculateScope(context.getTenantId(), arg.getActivityType().getEmployeeIds(), arg.getActivityType().getDepartmentIds(), arg.getActivityType().getRoleIds()));

        initArgException(arg);
        ActivityTypePO po = ActivityTypePO.fromVO(arg.getActivityType());

        activityTypeDAO.edit(context.getTenantId(), arg.getActivityType().getId(), context.getEmployeeId(), po.getName(), po.getEmployeeIds(), po.getDepartmentIds(), po.getRoleIds(), po.getScopeDescription(), po.getDescription(), po.getStatus(), po.getExceptionStatus(), po.getActivityNodes(), Boolean.TRUE.equals(po.getForbidRelateCustomer()));

        activityTypeManager.publishSyncActivityTypeFieldTask(context.getTenantId());

        activityTypeManager.editPlatI18Key(context.getTenantId(), arg.getActivityType());

        activityTypeManager.createReference(context.getTenantId(), arg.getActivityType());
        //判断是否有门店费用核销节点，
        checkActivityStoreWriteOffNode(context.getTenantId(), arg.getActivityType(), old);
        //检查是否开启上账能力
        checkActivityDealerCostNode(context.getTenantId(), arg.getActivityType());
        return EditActivityType.Result.builder().activityType(ActivityTypeVO.fromPO(activityTypeDAO.get(context.getTenantId(), arg.getActivityType().getId()))).build();
    }

    private void initArgException(AddActivityType.Arg arg) {
        arg.getActivityType().setExceptionStatus(ExceptionStatusType.NORMAL.value());
        for (ActivityNodeVO node : arg.getActivityType().getActivityNodeList()) {
            node.setExceptionStatus(ExceptionStatusType.NORMAL.value());
            node.setNodeExceptionInfo(new NodeExceptionInfoVO(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.NORMAL.value()));
        }
    }

    private void initArgException(EditActivityType.Arg arg) {
        arg.getActivityType().setExceptionStatus(ExceptionStatusType.NORMAL.value());
        for (ActivityNodeVO node : arg.getActivityType().getActivityNodeList()) {
            node.setExceptionStatus(ExceptionStatusType.NORMAL.value());
            node.setNodeExceptionInfo(new NodeExceptionInfoVO(ExceptionStatusType.NORMAL.value(), ExceptionStatusType.NORMAL.value()));
        }
    }

    @Override
    public DeleteActivityType.Result delete(DeleteActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForTPM(context);

        if (!activityTypeManager.deleteAble(context.getTenantId(), arg.getId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_CAN_NOT_DELETE_ERROR));
        }
        activityTypeDAO.delete(context.getTenantId(), context.getEmployeeId(), arg.getId());

        ActivityTypePO activityTypePO = activityTypeDAO.getById(context.getTenantId(), arg.getId());
        activityTypeManager.deleteReference(context.getTenantId(), activityTypePO);
        activityTypeManager.publishSyncActivityTypeFieldTask(context.getTenantId());
        return DeleteActivityType.Result.builder().build();
    }

    @Override
    public GetActivityType.Result get(GetActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        Map<String, String> activityMap = Maps.newHashMap();
        if (!Strings.isNullOrEmpty(arg.getActivityId())) {
            IObjectData activity = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getActivityId(), ApiNames.TPM_ACTIVITY_OBJ);
            arg.setId(ObjectDataExt.of(activity).getStringValue(TPMActivityFields.ACTIVITY_TYPE));

            fillWebDetailWithData(context.getTenantId(), Lists.newArrayList(activity));
            activityMap.put(activity.getDescribeApiName(), activity.getName());
            activityMap.put(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, activity.get("activity_unified_case_id__r", String.class, ""));
        }

        if (Strings.isNullOrEmpty(arg.getId())) {
            throw new ValidateException("Invalid parameter error. Parameter '_id' can not be null or empty.");
        }

        ActivityTypePO po = activityTypeDAO.get(context.getTenantId(), arg.getId());
        if (Objects.isNull(po)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NOT_EXISTS_ERROR));
        }
        addTemplateName(po);

        //校验活动类型异常
        if (!Strings.isNullOrEmpty(po.getExceptionStatus())) {
            String exceptionStatus = po.getExceptionStatus();
            String errorMessage = activityTypeManager.checkValidation(context.getTenantId(), po);
            if (!exceptionStatus.equals(po.getExceptionStatus())) {
                activityTypeManager.publishSyncActivityTypeExceptionTask(context.getTenantId(), errorMessage, po);
            }
        }

        ActivityTypeVO vo = ActivityTypeVO.fromPO(po);
        activityTypeManager.fillObjectDisplayNameAndRecordTypeDisplayName(context.getTenantId(), vo);
        activityTypeManager.fillNodeTemplateDescription(context.getTenantId(), vo);
        if (!Strings.isNullOrEmpty(arg.getActivityId())) {
            activityTypeManager.fillActivityReportData(context.getTenantId(), arg.getActivityId(), vo);
            fillReportDateObjectName(activityMap, vo);
        }

        return GetActivityType.Result.builder().activityType(vo).dealerApiName(storeBusiness.findDealerFieldApiName(context.getTenantId())).build();
    }

    private void fillReportDateObjectName(Map<String, String> activityMap, ActivityTypeVO vo) {
        for (ActivityNodeVO activityNodeVO : vo.getActivityNodeList()) {
            String objectApiName = activityNodeVO.getObjectApiName();
            if (ApiNames.TPM_ACTIVITY_OBJ.equals(objectApiName) || ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(objectApiName)) {
                activityNodeVO.getActivityPlanReportData().setName(activityMap.get(objectApiName));
            }
        }
    }

    private List<IObjectData> fillWebDetailWithData(String tenantId, List<IObjectData> data) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        return data;
    }

    @Override
    public ListActivityType.Result list(ListActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        List<String> templateIds = activityTypeManager.tryInitSystemTemplate(context.getTenantId(), -10000);
        if (CollectionUtils.isNotEmpty(templateIds)) {
            activityTypeManager.publishSyncActivityTypeFieldTask(context.getTenantId());
        }
        ListParameterUtils.correctListParameter(arg);

        List<ActivityTypePO> data = activityTypeDAO.list(context.getTenantId(), arg.getKeyword(), null, arg.getLimit(), arg.getOffset());
        addTemplateNameList(data);
        long total = activityTypeDAO.count(context.getTenantId(), arg.getKeyword());
        return ListActivityType.Result.builder().data(data.stream().map(ActivityTypeVO::fromPOToListDatumVO).collect(Collectors.toList())).total(total).build();
    }

    private void addTemplateNameList(List<ActivityTypePO> types) {
        ActivityTypeTemplateNameList.Result result = activityTypeTemplateService.queryNameList(new ActivityTypeTemplateNameList.Arg());
        for (ActivityTypePO type : types) {
            if (StringUtils.isEmpty(type.getTemplateName())) {
                type.setTemplateName(result.getTemplateName().getOrDefault(type.getTemplateId(), ""));
            }
        }
    }

    private void addTemplateName(ActivityTypePO type) {
        ActivityTypeTemplateNameList.Result result = activityTypeTemplateService.queryNameList(new ActivityTypeTemplateNameList.Arg());
        if (StringUtils.isEmpty(type.getTemplateName())) {
            type.setTemplateName(result.getTemplateName().getOrDefault(type.getTemplateId(), ""));
        }
    }

    @Override
    public ValidActivityType.Result validActivityType(ValidActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        Integer employeeId = context.getEmployeeId();
        List<Integer> departmentIds = organizationService.getDepartmentIds(Integer.parseInt(context.getTenantId()), employeeId);
        List<String> roleIds = roleService.queryRoleByEmployeeId(Integer.parseInt(context.getTenantId()), employeeId);
        List<ActivityTypePO> data = activityTypeDAO.validActivityType(context.getTenantId(), arg.getObjectApiName(), departmentIds, employeeId, roleIds);
        List<ValidActivityTypeVO> resultData = data.stream().map(datum -> ValidActivityTypeVO.fromPO(datum, arg.getObjectApiName())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(arg.getCurrentObjectRecordType())) {
            resultData = resultData.stream().filter(datum -> datum.getCurrentObjectRecordType().equals(arg.getCurrentObjectRecordType())).collect(Collectors.toList());
        }
        return ValidActivityType.Result.builder().data(resultData).dealerRecordType(storeBusiness.findDealerRecordType(context.getTenantId())).dealerApiName(storeBusiness.findDealerFieldApiName(context.getTenantId())).build();
    }

    @Override
    public LoadDesignerConfig.Result loadDesignerConfig(LoadDesignerConfig.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        boolean enableCostAssign = activityTypeManager.judgeOpenCostAssignConfig(context.getTenantId());
        boolean enableExistsStoreWriteOff = activityTypeManager.verifyExistsStoreWriteOff(arg.getId(), context.getTenantId());
        boolean enableAgreementStoreConfirm = isEnableAgreementStoreConfirm(context);
        boolean enableRebate = "1".equals(getConfigValue(context.getTenantId(), "rebate"));
        boolean enableChargeUp = activityTypeManager.getEnableChargeUp(context.getTenantId());
        boolean enableAi = activityTypeManager.getEnableAi(context.getTenantId());

        if (!Strings.isNullOrEmpty(arg.getId()) && !activityTypeManager.editAble(context.getTenantId(), arg.getId())) {
            boolean enableAuditMode = activityTypeManager.getEnableAuditMode(context.getTenantId(), arg.getId());
            return LoadDesignerConfig.Result.builder()
                    .chargeUpEnable(enableChargeUp)
                    .enableAuditMode(enableAuditMode)
                    .enableAgreementStoreConfirm(enableAgreementStoreConfirm)
                    .enableCostAssign(enableCostAssign)
                    .enableExistsStoreWriteOff(enableExistsStoreWriteOff)
                    .enableRebate(enableRebate)
                    .enableAi(enableAi)
                    .showAiConfig(TPMGrayUtils.allowEnableActivityAI(context.getTenantId()))
                    .nodePermission(NodePermissionVO.builder().add(0).edit(0).delete(0).move(0).build())
                    .build();
        }

        return LoadDesignerConfig.Result.builder()
                .chargeUpEnable(enableChargeUp)
                .enableAuditMode(true)
                .enableCostAssign(enableCostAssign)
                .enableExistsStoreWriteOff(enableExistsStoreWriteOff)
                .enableAgreementStoreConfirm(enableAgreementStoreConfirm)
                .enableRebate(enableRebate)
                .enableAi(enableAi)
                .showAiConfig(TPMGrayUtils.allowEnableActivityAI(context.getTenantId()))
                .nodePermission(NodePermissionVO.builder().add(1).edit(1).delete(1).move(1).build())
                .build();
    }

    private boolean isEnableAgreementStoreConfirm(ApiContext context) {
        try {
            IObjectDescribe agreementDescribe = serviceFacade.findObject(context.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            return agreementDescribe.containsField(TPMActivityAgreementFields.SIGNING_MODE);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public DataActivityType.Result dataActivityType(DataActivityType.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        List<ActivityTypePO> activityTypePOS = activityTypeDAO.all(context.getTenantId(), true, false);

        Map<String, DataActivityType.ObjectFilter> activityTypeMap = Maps.newHashMap();
        for (ActivityTypePO activityTypePO : activityTypePOS) {
            DataActivityType.ObjectFilter objectFilter = new DataActivityType.ObjectFilter();
            objectFilter.setName(activityTypePO.getName());
            objectFilter.setIsIncludeUnifiedCase(activityTypePO.getActivityNodes().stream().anyMatch(v -> ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(v.getObjectApiName())));

            activityTypeMap.put(activityTypePO.getId().toString(), objectFilter);
        }

        return DataActivityType.Result.builder().data(activityTypeMap).build();
    }

    @Override
    public FindActivityData.Result findActivityData(FindActivityData.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        String recordType = getActivityTypeNodeRecordType(context, arg);

        ObjectDataDocument data = getData(context, arg);

        FindActivityData.DealerData dealer = getDealerId(context, arg);
        return FindActivityData.Result.builder().data(data).recordType(recordType).dealerData(dealer).build();
    }

    @Override
    public ValidActivityDateByRecordType.Result validActivityDateByRecordType(ValidActivityDateByRecordType.Arg arg) {
        String recordType = arg.getRecordType();
        String slaveApiName = arg.getSlaveApiName();
        String objectApiName = arg.getObjectApiName() == null ? ApiNames.TPM_ACTIVITY_OBJ : arg.getObjectApiName();
        ApiContext context = ApiContextManager.getContext();

        List<String> layoutAPiNames = findLayoutApiNamesByRecordType(context.getTenantId(), objectApiName, recordType);
        Map<String, Layout> layoutByApiNames = serviceFacade.getLayoutLogicService().findLayoutByApiNames(context.getTenantId(), layoutAPiNames, objectApiName);

        boolean isExistsDate = true;
        boolean isExistsSlave = true;
        for (Layout layoutByName : layoutByApiNames.values()) {
            if (Objects.nonNull(layoutByName)) {
                LayoutExt layout = LayoutExt.of(layoutByName);
//                isExistsSlave = isExistsSlave(slaveApiName, layout);
                isExistsDate = isExistsDate(layout);
            }
        }
        return ValidActivityDateByRecordType.Result.builder().isExistsDate(isExistsDate).isExistsSlave(true).build();
    }

    @Override
    public TPMPermission.Result permission() {
        ApiContext context = ApiContextManager.getContext();
        BaseService.TPMAppPermission tpmAppPermission = super.appPermission(context.getTenantAccount(), context.getEmployeeId());
        return TPMPermission.Result.builder().isTPMAppAdmin(tpmAppPermission.isTPMAppAdmin()).errCode(tpmAppPermission.getErrCode()).errMessage(tpmAppPermission.getErrMessage()).build();
    }

    private boolean isExistsDate(LayoutExt layout) {
        FormComponentExt formComponentExt = layout.getFormComponent().orElse(null);
        if (Objects.nonNull(formComponentExt)) {
            for (IFieldSection fieldSection : formComponentExt.getFieldSections()) {
                if (fieldSection.getFields().stream().anyMatch(v -> v.getFieldName().equals(TPMActivityFields.BEGIN_DATE)) && fieldSection.getFields().stream().anyMatch(v -> v.getFieldName().equals(TPMActivityFields.END_DATE))) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isExistsSlave(String slaveApiName, LayoutExt layout) {
        if (StringUtils.isEmpty(slaveApiName)) {
            return false;
        }
        try {
            for (IComponent component : layout.getComponents()) {
                if (slaveApiName.equals(component.get("ref_object_api_name", String.class))) {
                    return true;
                }
            }
        } catch (Exception exception) {
            log.error("layout component error", exception);
        }
        return false;
    }

    private List<String> findLayoutApiNamesByRecordType(String tenantId, String objectApiName, String recordType) {
        PaasFindAssignedLayout.Arg arg = new PaasFindAssignedLayout.Arg();
        arg.setDescribeApiName(objectApiName);
        arg.setLayoutType("edit");
        PaasFindAssignedLayout.Result result = paasLayoutProxy.findAssignedLayout(Integer.parseInt(tenantId), -10000, arg);
        PaasFindAssignedLayout.Content data = result.getData();
        if (Objects.isNull(data)) {
            return Lists.newArrayList();
        }
        List<String> layoutApiNames = Lists.newArrayList();
        for (PaasFindAssignedLayout.Role role : data.getRoleList()) {
            role.getRecordLayouts().stream().filter(v -> v.getRecordApiName().equals(recordType)).map(PaasFindAssignedLayout.RecordLayout::getLayoutApiName).findFirst().ifPresent(layoutApiNames::add);
        }
        return layoutApiNames.stream().distinct().collect(Collectors.toList());
    }

    private FindActivityData.DealerData getDealerId(ApiContext context, FindActivityData.Arg arg) {
        FindActivityData.DealerData dealerData = new FindActivityData.DealerData();

        if (StringUtils.isEmpty(arg.getStoreId())) {
            return dealerData;
        }

        IObjectData store = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getStoreId(), ApiNames.ACCOUNT_OBJ);
        if (Objects.isNull(store)) {
            throw new ValidateException("store data is Empty");
        }

        List<String> recordType = storeBusiness.findDealerRecordType(context.getTenantId());
        if (recordType.contains(store.getRecordType())) {
            dealerData.setName(store.getName());
            dealerData.setDealerId(store.getId());
        } else {
            String dealerId = storeBusiness.findDealerId(context.getTenantId(), store);
            if (!Strings.isNullOrEmpty(dealerId)) {
                IObjectData dealer = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(context.getTenantId()), dealerId, ApiNames.ACCOUNT_OBJ);
                dealerData.setName(dealer.getName());
                dealerData.setDealerId(dealer.getId());
            }
        }

        return dealerData;

    }


    private ObjectDataDocument getData(ApiContext context, FindActivityData.Arg arg) {
        FindActivityData.FindObjectData data = arg.getData();

        if (Objects.isNull(data)) {
            return null;
        }
        if (StringUtils.isEmpty(arg.getData().getId())) {
            throw new ValidateException("data id is Empty");
        }
        if (StringUtils.isEmpty(arg.getData().getApiName())) {
            throw new ValidateException("data apiName is Empty");
        }

        return ObjectDataDocument.of(serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getData().getId(), arg.getData().getApiName()));
    }

    private String getActivityTypeNodeRecordType(ApiContext context, FindActivityData.Arg arg) {
        FindActivityData.ActivityTypeNodeRecordType activityTypeNodeRecordType = arg.getActivityTypeNodeRecordType();

        if (Objects.isNull(activityTypeNodeRecordType)) {
            return "";
        }
        if (StringUtils.isEmpty(activityTypeNodeRecordType.getId())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_SERVICE_0));
        }
        if (StringUtils.isEmpty(activityTypeNodeRecordType.getSourceApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_SERVICE_1));
        }
        if (StringUtils.isEmpty(activityTypeNodeRecordType.getTargetApiName())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_SERVICE_2));
        }

        ActivityTypeExt activityTypeExt = null;
        if (ApiNames.TPM_ACTIVITY_OBJ.equals(activityTypeNodeRecordType.getSourceApiName())) {
            activityTypeExt = activityTypeManager.findByActivityId(context.getTenantId(), activityTypeNodeRecordType.getId());
        }

        // 860 需要加上
        if (ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(activityTypeNodeRecordType.getSourceApiName())) {
            activityTypeExt = activityTypeManager.findByActivityUnifiedCaseId(context.getTenantId(), activityTypeNodeRecordType.getId());
        }

        if (Objects.isNull(activityTypeExt) || CollectionUtils.isEmpty(activityTypeExt.get().getActivityNodes())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_SERVICE_3));
        }
        List<ActivityNodeEntity> nodes = activityTypeExt.get().getActivityNodes().stream().filter(node -> activityTypeNodeRecordType.getTargetApiName().equals(node.getObjectApiName())).collect(Collectors.toList());

        return CollectionUtils.isNotEmpty(nodes) ? nodes.get(0).getObjectRecordType() : "";
    }

    private String getConfigValue(String tenantId, String key) {
        GetConfigValueByKey.Arg arg = new GetConfigValueByKey.Arg();
        arg.setKey(key);
        GetConfigValueByKey.Result result = paasDataProxy.getConfigValueByKey(Integer.parseInt(tenantId), -10000, arg);
        if (result.getCode() != 0) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_TYPE_SERVICE_4) + key);
        }
        return result.getData().getValue();
    }

    @Override
    public QueryActivityTypeByModelAndRule.Result queryByModelAndRule(QueryActivityTypeByModelAndRule.Arg arg) {
        String tenantId = ApiContextManager.getContext().getTenantId();
        List<ActivityTypeExt> activityTypeExtList = activityTypeManager.queryActivityTypesByModelIdOrRuleId(tenantId, arg.getModelId(), arg.getRuleId());
        QueryActivityTypeByModelAndRule.Result result = new QueryActivityTypeByModelAndRule.Result();
        result.setActivityTypeList(activityTypeExtList.stream().map(v -> {
            QueryActivityTypeByModelAndRule.ActivityTypeVO vo = new QueryActivityTypeByModelAndRule.ActivityTypeVO();
            vo.setId(v.get().getId().toString());
            vo.setName(v.get().getName());
            ActivityProofAiConfigEntity aiConfigEntity = v.proofConfig().getAiConfig();
            vo.setRuleId(aiConfigEntity.getAdaptationRule());
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public ListActivityTypeForProofAi.Result listForPoofAi(ListActivityTypeForProofAi.Arg arg) {
        // 获取当前上下文
        ApiContext context = ApiContextManager.getContext();
        if (arg.getLimit() == null) {
            arg.setLimit(-1);
        }

        // 校正分页参数
        ListParameterUtils.correctListParameter(arg);

        // 设置默认模板和AI开关状态
        String template = Optional.ofNullable(arg.getTemplate()).orElse("display.");
        String openAi = Optional.ofNullable(arg.getOpenAi()).orElse("1");

        // 查询活动类型数据
        List<ActivityTypePO> activityTypeList = activityTypeDAO.list(
                context.getTenantId(),
                arg.getKeyword(),
                template,
                arg.getLimit(),
                arg.getOffset()
        );

        // 过滤符合条件的活动类型ID
        List<String> activityTypeIds = activityTypeList.stream()
                .map(ActivityTypeExt::of)
                .filter(activityTypeExt -> {
                    if (activityTypeExt.proofNode() == null) {
                        return false;
                    }
                    ActivityProofAiConfigEntity aiConfig = activityTypeExt.proofConfig().getAiConfig();
                    if (aiConfig == null) {
                        return false;
                    }

                    boolean hasAgreementNode = activityTypeExt.agreementNode() != null;
                    boolean aiEnabled = aiConfig.getEnableAiDisplayRecognition();

                    // 根据openAi参数决定过滤条件
                    return "1".equals(openAi)
                            ? aiEnabled && hasAgreementNode
                            : !aiEnabled && hasAgreementNode;
                })
                .map(activityTypeExt -> activityTypeExt.get().getUniqueId())
                .collect(Collectors.toList());

        return ListActivityTypeForProofAi.Result.builder()
                .data(activityTypeIds)
                .build();
    }

    @Override
    public ActivityTypeForProofAiEnable.Result listEnableProofAI(ActivityTypeForProofAiEnable.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (CollectionUtils.isEmpty(arg.getIds())) {
            throw new ValidateException("activity type id can not empty");
        }
        List<ActivityTypeExt> activityTypes = activityTypeManager.findByActivityTypeIds(context.getTenantId(), arg.getIds());
        List<ActivityTypeForProofAiEnable.EnableVO> voList = Lists.newArrayList();

        // 过滤符合条件的活动类型ID
        for (ActivityTypeExt activityType : activityTypes) {
            ActivityTypeExt activityTypeExt = ActivityTypeExt.of(activityType.get());
            ActivityTypeForProofAiEnable.EnableVO vo = new ActivityTypeForProofAiEnable.EnableVO();
            vo.setEnableAi(false);
            vo.setId(activityTypeExt.get().getUniqueId());

            if (activityTypeExt.proofNode() == null) {
                voList.add(vo);
                continue;
            }
            ActivityProofAiConfigEntity aiConfig = activityTypeExt.proofConfig().getAiConfig();
            if (aiConfig == null) {
                voList.add(vo);
                continue;
            }
            vo.setEnableAi(aiConfig.getEnableAiDisplayRecognition());
            voList.add(vo);
        }


        return ActivityTypeForProofAiEnable.Result.builder().data(voList).build();
    }
}
