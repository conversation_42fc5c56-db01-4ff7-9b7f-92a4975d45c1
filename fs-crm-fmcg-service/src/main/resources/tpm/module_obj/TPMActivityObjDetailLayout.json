{"components": [{"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list"}, {"field_section": [], "buttons": [], "api_name": "sale_log", "related_list_name": "", "header": "跟进动态", "nameI18nKey": "paas.udobj.follow_up_dynamic", "type": "related_record"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "is_hidden": false, "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "order": 1}, {"components": [["form_component"], ["TPMActivityDetailObj_md_group_component"], ["TPMActivityStoreObj_md_group_component"], ["operation_log"]], "buttons": [], "api_name": "container_default_layout_default_TPMActivityObj__c", "tabs": [{"api_name": "tab_form_component", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "tab_TPMActivityDetailObj_md_group_component", "header": "参与活动项目", "nameI18nKey": "TPMActivityDetailObj.field.activity_id.reference_label"}, {"api_name": "tab_TPMActivityStoreObj_md_group_component", "header": "参与活动门店", "nameI18nKey": "TPMActivityStoreObj.field.activity_id.reference_label"}, {"api_name": "tab_operation_log", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}], "header": "页签容器", "type": "tabs"}, {"buttons": [], "api_name": "TPMActivityDetailObj_md_group_component", "related_list_name": "target_related_list_TPMActivityDetailObj_TPMActivityObj__c", "button_info": [], "ref_object_api_name": "TPMActivityDetailObj", "child_components": [], "header": "参与活动项目", "nameI18nKey": "TPMActivityDetailObj.field.activity_id.reference_label", "type": "multi_table", "field_api_name": "activity_id"}, {"buttons": [], "api_name": "TPMActivityStoreObj_md_group_component", "related_list_name": "target_related_list_TPMActivityStoreObj_TPMActivityObj__c", "button_info": [], "ref_object_api_name": "TPMActivityStoreObj", "child_components": [], "header": "参与活动门店", "nameI18nKey": "TPMActivityStoreObj.field.activity_id.reference_label", "type": "multi_table", "field_api_name": "activity_id"}, {"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}, {"action_type": "default", "api_name": "CloseTPMActivity_button_default", "action": "CloseTPMActivity", "label": "结案", "isActive": true}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple"}, {"field_section": [{"render_type": "select_one", "field_name": "activity_type"}, {"render_type": "object_reference", "field_name": "dealer_id"}, {"render_type": "select_one", "field_name": "activity_status"}, {"render_type": "employee", "field_name": "owner"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": true, "is_required": true, "render_type": "auto_number", "field_name": "code"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "name"}, {"is_readonly": true, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "activity_type"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "dealer_id"}, {"is_readonly": false, "is_required": true, "render_type": "department_many", "field_name": "multi_department_range"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "begin_date"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "end_date"}, {"is_readonly": true, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "activity_status"}, {"is_readonly": true, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "closed_status"}, {"is_readonly": false, "full_line": false, "is_required": false, "render_type": "long_text", "field_name": "description"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "dealer_cashing_type"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "store_cashing_type"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "currency", "field_name": "activity_amount"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "activity_actual_amount"}], "api_name": "group_fUly2__c", "tab_index": "ltr", "column": 2, "header": "费用申请信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "budget_table"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "available_amount"}], "api_name": "group_n1Cb3__c", "tab_index": "ltr", "column": 2, "header": "关联预算信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}], "api_name": "group_Zg69Z__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "is_hidden": false, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "order": 4}], "ref_object_api_name": "TPMActivityObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "container_default_layout_default_TPMActivityObj__c"], ["relevant_team_component", "sale_log"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "buttons": [], "package": "CRM", "display_name": "默认布局", "is_default": true, "version": 8, "api_name": "layout_default_TPMActivityObj__c", "layout_description": ""}