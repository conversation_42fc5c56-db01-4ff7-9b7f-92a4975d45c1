package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.api.scan.PhysicalRewardDefault;
import com.facishare.crm.fmcg.tpm.api.scan.PhysicalRewardWriteOff;
import com.facishare.crm.fmcg.tpm.web.service.PhysicalRewardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/Physical", produces = "application/json")
public class MengNiuPhysicalRewardController {

    @Resource
    private PhysicalRewardService physicalRewardService;

    @PostMapping(value = "GetDefaultPhysicalReward")
    public PhysicalRewardDefault.Result getDefaultPhysicalReward(@RequestBody PhysicalRewardDefault.Arg arg) {
        return physicalRewardService.getDefaultPhysicalReward(arg);
    }

    @RequestMapping("OutPhysicalRewardWriteOff")
    public PhysicalRewardWriteOff.Result outPhysicalRewardWriteOff(@RequestBody PhysicalRewardWriteOff.Arg arg) {
        return physicalRewardService.outPhysicalRewardWriteOff(arg);
    }
}
