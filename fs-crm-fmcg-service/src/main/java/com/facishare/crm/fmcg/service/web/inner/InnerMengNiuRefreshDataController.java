package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.mengniu.api.FillActivityItem;
import com.facishare.crm.fmcg.mengniu.api.FillProductRangeData;
import com.facishare.crm.fmcg.mengniu.api.FillStoreInformation;
import com.facishare.crm.fmcg.mengniu.api.FillTenantInformation;
import com.facishare.crm.fmcg.mengniu.service.IMengNiuRefreshDataService;
import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/RefreshData", produces = "application/json")
public class InnerMengNiuRefreshDataController {

    @Resource
    private IMengNiuRefreshDataService mengNiuRefreshDataService;

    @PostMapping(value = "FillStoreInformation")
    public InnerApiResult<FillStoreInformation.Result> fillStoreInformation(@RequestBody FillStoreInformation.Arg arg) {
        return InnerApiResult.apply(mengNiuRefreshDataService::fillStoreInformation, arg);
    }

    @PostMapping(value = "FillStoreInformationV1")
    public InnerApiResult<FillStoreInformation.Result> fillStoreInformationV1(@RequestBody FillStoreInformation.Arg arg) {
        return InnerApiResult.apply(mengNiuRefreshDataService::fillStoreInformationV1, arg);
    }

    @PostMapping(value = "FillTenantInformation")
    public InnerApiResult<FillTenantInformation.Result> fillTenantInformation(@RequestBody FillTenantInformation.Arg arg) {
        return InnerApiResult.apply(mengNiuRefreshDataService::fillTenantInformation, arg);
    }

    @PostMapping(value = "FillActivityItem")
    public InnerApiResult<FillActivityItem.Result> fillActivityItem(@RequestBody FillActivityItem.Arg arg) {
        return InnerApiResult.apply(mengNiuRefreshDataService::fillActivityItem, arg);
    }

    @PostMapping(value = "FillProductRangeData")
    public InnerApiResult<FillProductRangeData.Result> fillProductRangeData(@RequestBody FillProductRangeData.Arg arg) {
        return InnerApiResult.apply(mengNiuRefreshDataService::fillProductRangeData, arg);
    }
}