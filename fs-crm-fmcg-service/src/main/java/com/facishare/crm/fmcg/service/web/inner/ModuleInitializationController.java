package com.facishare.crm.fmcg.service.web.inner;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.contract.kk.ActivityImport;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IModuleInitializationService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/ModuleInitialization", produces = "application/json")
public class ModuleInitializationController {

    @Resource
    private IModuleInitializationService moduleInitializationService;

    @GetMapping(value = "AddActivityTypeFields")
    public void addActivityTypeFields(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.addActivityTypeFields(tenantId);
    }

    @GetMapping(value = "UpgradeFor810")
    public void upgradeFor810(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.upgradeFor810(tenantId);
    }

    @RequestMapping(value = "init_sales_order_product_sales_type_fields")
    public JSONObject initSalesOrderProductSalesTypeFields(@RequestParam String tenantId) {
        moduleInitializationService.initSalesOrderProductSalesTypeFields(tenantId);
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result;
    }

    @RequestMapping(value = "init_sales_order_sales_mode_fields")
    public JSONObject initSalesOrderSalesModeFields(@RequestParam String tenantId) {
        moduleInitializationService.initSalesOrderSalesModeFields(tenantId);
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result;
    }

    @GetMapping(value = "init_sales_order_TPM_fields")
    public String initSalesOrderTPMFields(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.initSalesOrderTPMFields(tenantId);
        return "success";
    }

    @GetMapping(value = "disable_sales_order_TPM_810_fields")
    public String disableSalesOrderTPM810Fields(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.disableSalesOrderTPM810Fields(tenantId);
        return "success";
    }

    @GetMapping(value = "init_all_tenant_sales_order_TPM_fields")
    public String initAllTenantSalesOrderTPMFields() {
        moduleInitializationService.initAllTenantSalesOrderTPMFields();
        return "success";
    }

    @GetMapping(value = "init_sales_order_TPM_815_fields")
    public String initSalesOrderTPM815Fields(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.initSalesOrderTPM815Fields(tenantId);
        return "success";
    }

    @GetMapping(value = "init_all_tenant_sales_order_TPM_815_fields")
    public String initAllTenantSalesOrderTPM815Fields() {
        moduleInitializationService.initAllTenantSalesOrderTPM815Fields();
        return "success";
    }

    @GetMapping(value = "init_all_tenant_sales_order_TPM_815_delete_fields")
    public String initAllTenantSalesOrderTPM815DeleteFields(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.initAllTenantSalesOrderTPM815DeleteFields(tenantId);
        return "success";
    }

    @GetMapping(value = "init_one_more_order_module")
    public String initOneMoreOrderModule(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.initOneMoreOrderModule(tenantId);
        return "success";
    }

    @PostMapping(value = "doDisassembly")
    public String doDisassembly(@RequestParam String tenantId, @RequestParam String objectDataIds, @RequestParam String flag) {
        moduleInitializationService.doDisassembly(tenantId, objectDataIds, flag);
        return "success";
    }

    @PostMapping(value = "initCashingFieldFor840")
    public String initCashingFieldFor840(@RequestParam String tenantIds) {
        moduleInitializationService.initCashingFieldFor840(tenantIds);
        return "success";
    }

    @PostMapping(value = "addButton")
    public String addButton(@RequestBody StoreWriteButtonInit.Arg arg) {
        return moduleInitializationService.addStoreWriteOffButton(arg);
    }

    @PostMapping(value = "imgNPathToAPath")
    public String imgNPathToAPath(@RequestParam String tenantAccount, @RequestParam String nPaths) {
        moduleInitializationService.imgNPathToAPath(tenantAccount, nPaths);
        return "success";
    }

    @PostMapping(value = "kdhImport")
    public String kdhImport(@RequestParam String tenantId, @RequestBody(required = false) ActivityImport.Arg arg) {
        moduleInitializationService.kdhActivityImport(tenantId, arg);
        return "success";
    }

    @GetMapping(value = "add_all_tenant_dealer_cost_dealer_wheres")
    public String addAllTenantDealerCostDealerWheres(@RequestParam("tenant_id") String tenantId) {
        moduleInitializationService.addAllTenantDealerCostDealerWheres(tenantId);
        return "success";
    }

    @PostMapping(value = "fmCashingType")
    public String fmCashingType(@RequestParam String tenantId, @RequestParam(required = false) String limitStr) {
        moduleInitializationService.fmCashingType(tenantId, limitStr);
        return "success";
    }

    @PostMapping(value = "changeAccrualRuleData")
    public String changeAccrualRuleData(@RequestParam String tenantId, @RequestParam(required = false) Integer limit) {
        moduleInitializationService.changeAccrualRuleData(tenantId, limit);
        return "success";
    }

    @PostMapping(value = "fixDisassemblyStatus")
    public String fixDisassemblyStatus(@RequestParam String tenantId, @RequestParam(required = false) List<String> dataIds, @RequestParam(required = false) String status) {
        moduleInitializationService.fixDisassemblyStatus(tenantId, dataIds, status);
        return "success";
    }

    @PostMapping(value = "fixBudgetAccountDetailTraceId")
    public String fixBudgetAccountDetailTraceId(@RequestParam String tenantId, @RequestParam String id) {
        moduleInitializationService.fixBudgetAccountDetailTraceId(tenantId, id);
        return "success";
    }

    @GetMapping(value = "AddObjFields")
    public void addObjFields(@RequestParam("tenant_id") String tenantId, @RequestParam("objectApiName") String objectApiName, @RequestParam("fieldApiNames") String fieldApiNames) {
        moduleInitializationService.addObjFields(tenantId, objectApiName, fieldApiNames.split(","));
    }

    @GetMapping(value = "DisableDeleteObjFields")
    public void disableDeleteObjFields(@RequestParam("tenant_id") String tenantId, @RequestParam("objectApiName") String objectApiName, @RequestParam("fieldApiNames") String fieldApiNames) {
        moduleInitializationService.disableDeleteObjFields(tenantId, objectApiName, fieldApiNames.split(","));
    }

    @GetMapping(value = "disable_delete_obj_fields_870")
    public void disable_delete_obj_fields_870(@RequestParam("tenant_id") String tenantId) {
        Map<String, List<String>> objectApiNameFieldMap = Maps.newHashMap();
/*        objectApiNameFieldMap.put(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, Lists.newArrayList("source_object_api_name", "mode_type", "unified_total_policy_dynamic_amount"));
        objectApiNameFieldMap.put(ApiNames.TPM_ACTIVITY_OBJ, Lists.newArrayList("source_object_api_name", "mode_type", "limit_obj_type", "total_policy_dynamic_amount", "account_unify_limit_amount"));
        objectApiNameFieldMap.put(ApiNames.TPM_ACTIVITY_STORE_OBJ, Lists.newArrayList("policy_limit_amount", "used_limit_amount", "remain_limit_amount"));
        objectApiNameFieldMap.put(ApiNames.SALES_ORDER_OBJ, Lists.newArrayList("activity_store_id"));
        objectApiNameFieldMap.put(ApiNames.PRICE_POLICY_OBJ, Lists.newArrayList("activity_id"));
        objectApiNameFieldMap.put(ApiNames.PRICE_POLICY_LIMIT_ACCOUNT_OBJ, Lists.newArrayList("activity_id"));*/
        objectApiNameFieldMap.put(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ, Lists.newArrayList("unified_total_policy_dynamic_amount"));
        objectApiNameFieldMap.put(ApiNames.TPM_ACTIVITY_OBJ, Lists.newArrayList("total_policy_dynamic_amount"));
        objectApiNameFieldMap.put(ApiNames.TPM_ACTIVITY_STORE_OBJ, Lists.newArrayList("policy_limit_amount", "used_limit_amount", "remain_limit_amount"));

        objectApiNameFieldMap.forEach((k, v) -> moduleInitializationService.disableDeleteObjFields(tenantId, k, v.toArray(new String[]{})));
        return;
    }

    @PostMapping(value = "fixActivityStatus")
    public String fixActivityStatus(@RequestBody FixActivityStatus.Arg arg) {
        moduleInitializationService.fixActivityStatus(arg.getTenantId(), arg.getDataIds(), arg.getStatus());
        return "success";
    }

    @PostMapping(value = "fixHistoryData")
    public String fixHistoryData(@RequestBody FixHistoryData.Arg arg) {
        moduleInitializationService.fixHistoryData(arg.getTenantId(), arg.getDataIds(), arg.getUpdateFieldMap(), arg.getApiName());
        return "success";
    }

    @PostMapping(value = "updateActivityType")
    public String updateActivityType(@RequestBody UpdateActivityType.Arg arg) {
        return moduleInitializationService.updateActivityType(arg);
    }

    @PostMapping(value = "unFrozenForDisassemblyStatusFailed")
    public String unFrozenForDisassemblyStatusFailed(@RequestParam String tenantId, @RequestParam(required = false) List<String> dataIds) {
        moduleInitializationService.unFrozenForDisassemblyStatusFailed(tenantId, dataIds);
        return "success";
    }
    @PostMapping(value = "doDisassemblyV2")
    public String doDisassemblyV2(@RequestParam String tenantId, @RequestParam(required = false) List<String> dataIds) {
        moduleInitializationService.doDisassemblyV2(tenantId, dataIds);
        return "success";
    }

    @PostMapping(value = "updateRedPacketExpire")
    public String updateRedPacketExpire(@RequestBody UpdateRedPacketExpire.Arg arg) {
        moduleInitializationService.updateRedPacketExpire(arg);
        return "success";
    }

    @PostMapping(value = "initDmsObjectFor950")
    public String authUrl(@RequestParam String tenantIds) {
        moduleInitializationService.initDmsObjectFor950(tenantIds);
        return "success";
    }

    @PostMapping(value = "initDmsObjectFor955")
    public String initDmsObjectFor955(@RequestParam String tenantIds) {
        moduleInitializationService.initDmsObjectFor955(tenantIds);
        return "success";
    }

    @PostMapping(value = "updateRedPacket")
    public String updateRedPacket(@RequestParam String tenantId,@RequestParam(required = false) String flag,@RequestParam(required = false) String fromConfig) {
        moduleInitializationService.updateRedPacket(tenantId,flag,fromConfig);
        return "success";
    }

}