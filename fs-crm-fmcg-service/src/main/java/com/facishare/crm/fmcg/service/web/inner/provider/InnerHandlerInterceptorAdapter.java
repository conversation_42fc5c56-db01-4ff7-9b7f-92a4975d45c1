package com.facishare.crm.fmcg.service.web.inner.provider;

import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.paas.I18N;

import com.facishare.paas.appframework.core.rest.CEPXHeader;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/16 18:40
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class InnerHandlerInterceptorAdapter extends HandlerInterceptorAdapter {

    public static final String INNER_API_TENANT_HEADER_KEY = "x-fs-ei";
    public static final String INNER_API_USER_HEADER_KEY = "x-fs-userInfo";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        ApiContext context = loadContextFromRequest(request);
        ApiContextManager.setContext(context);
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ApiContextManager.removeContext();
        TraceContext.remove();
        super.afterCompletion(request, response, handler, ex);
    }

    private ApiContext loadContextFromRequest(HttpServletRequest request) {
        String tenantId = request.getHeader(INNER_API_TENANT_HEADER_KEY);
        String employeeId = request.getHeader(INNER_API_USER_HEADER_KEY);
        String appId = request.getHeader(CEPXHeader.APP_ID.key());
        String postId = request.getHeader(CEPXHeader.POST_ID.key());
        String local = request.getHeader(CEPXHeader.LOCALE.key());
        String outTenantId = request.getHeader(CEPXHeader.OUT_TENANT_ID.key());
        String outUserId = request.getHeader(CEPXHeader.OUT_USER_ID.key());
        if (!Strings.isNullOrEmpty(tenantId)) {
            TraceContext.get().setEi(tenantId);
        }


        Integer employeeIdInt = null;
        if (!Strings.isNullOrEmpty(employeeId)) {
            employeeIdInt = Integer.parseInt(employeeId);
        }

        if (!Strings.isNullOrEmpty(local)) {
            I18N.setContext(tenantId, local);
        }

        return ApiContext.builder()
                .tenantId(tenantId)
                .employeeId(employeeIdInt)
                .outTenantId(outTenantId)
                .outUserId(outUserId)
                .appId(appId)
                .postId(postId)
                .build();
    }
}