package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.api.activity.GetCustomizedInfo;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityCustomizedInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/ActivityCustomized", produces = "application/json")
public class ActivityCustomizedController {

    @Resource
    private IActivityCustomizedInfoService activityCustomizedInfoService;

    @PostMapping(value = "GetCustomizedInfo")
    public GetCustomizedInfo.Result getCustomizedInfo(@RequestBody GetCustomizedInfo.Arg arg) {
        return activityCustomizedInfoService.getCustomizedInfo(arg);
    }

}