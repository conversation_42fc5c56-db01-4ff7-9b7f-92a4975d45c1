package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.business.abstraction.IOrderGoodsPromotionPolicyService;
import com.facishare.crm.fmcg.tpm.web.contract.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/11/22 14:07
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/OrderGoodsPromotionPolicy", produces = "application/json")
public class OrderGoodsPromotionPolicyController {

    @Resource
    private IOrderGoodsPromotionPolicyService orderGoodsPromotionPolicyService;

    @PostMapping(value = "/Init")
    public OrderGoodsInit.Result init(@RequestBody OrderGoodsInit.Arg arg) {
        return orderGoodsPromotionPolicyService.init(arg);
    }

    @PostMapping(value = "/Get")
    public OrderGoodsPromotionPolicyGet.Result get(@RequestBody OrderGoodsPromotionPolicyGet.Arg arg) {
        return orderGoodsPromotionPolicyService.get(arg);
    }

    @PostMapping(value = "/EnableEditPromotionPolicy")
    public OrderGoodsPromotionPolicyEdit.Result enableEditPromotionPolicy(@RequestBody OrderGoodsPromotionPolicyEdit.Arg arg) {
        return orderGoodsPromotionPolicyService.enableEditOrderGoodsPromotionPolicy(arg);
    }

    @PostMapping(value = "/OrderGoodsQueryAccountFilter")
    public OrderGoodsQueryAccountFilter.Result orderGoodsQueryAccountFilter(@RequestBody OrderGoodsQueryAccountFilter.Arg arg) {
        return orderGoodsPromotionPolicyService.orderGoodsQueryAccountFilter(arg);
    }

    @PostMapping(value = "/OrderGoodsMeetingFilter")
    public OrderGoodsMeetingFilter.Result orderGoodsMeetingFilter(@RequestBody OrderGoodsMeetingFilter.Arg arg) {
        return orderGoodsPromotionPolicyService.orderGoodsMeetingFilter(arg);
    }

    @PostMapping(value = "/InvalidByActivityId")
    public OrderGoodsInvalidByActivityId.Result invalidByActivityId(@RequestBody OrderGoodsInvalidByActivityId.Arg arg) {
        return orderGoodsPromotionPolicyService.invalidByActivityId(arg);
    }

    @PostMapping(value = "/EnableByActivityId")
    public OrderGoodsEnableByActivityId.Result enableByActivityId(@RequestBody OrderGoodsEnableByActivityId.Arg arg) {
        return orderGoodsPromotionPolicyService.enableByActivityId(arg);
    }
}
