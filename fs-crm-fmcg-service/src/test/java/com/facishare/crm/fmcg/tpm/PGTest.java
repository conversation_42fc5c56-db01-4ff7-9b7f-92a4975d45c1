package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordObjFields;
import com.facishare.crm.fmcg.common.apiname.WithdrawRecordObjFields;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.mengniu.service.IAgreementAuditService;
import com.facishare.crm.fmcg.tpm.api.scan.PhysicalRewardWriteOff;
import com.facishare.crm.fmcg.tpm.api.withdraw.PreviewWithdraw;
import com.facishare.crm.fmcg.tpm.api.withdraw.QueryRedPacketRecords;
import com.facishare.crm.fmcg.tpm.api.withdraw.QueryWithdrawRecords;
import com.facishare.crm.fmcg.tpm.api.withdraw.SubmitWithdraw;
import com.facishare.crm.fmcg.tpm.dao.pg.ActivityProofMapper;
import com.facishare.crm.fmcg.tpm.web.contract.AgreementAuditSummaryList;
import com.facishare.crm.fmcg.tpm.web.service.PhysicalRewardService;
import com.facishare.crm.fmcg.tpm.web.service.WithdrawService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FmcgServiceProxy;
import com.fmcg.framework.http.contract.fmcgservice.GetLicense;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/21 上午11:41
 */
public class PGTest extends BaseTest {

    @Resource
    private ActivityProofMapper activityProofMapper;

    @Resource(name = "objectDataPgService")
    private IObjectDataService objectDataService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private WithdrawService withdrawService;

    @Resource
    private IAgreementAuditService agreementAuditService;

    @Resource
    private FmcgServiceProxy fmcgServiceProxy;

    @Resource
    private PhysicalRewardService physicalRewardService;

    @Test
    public void testSql1() {
        System.out.println(JSON.toJSONString(activityProofMapper.setTenantId("84931").queryAuditSummaryGroupByAccount("84931", "TPMActivityProofObj", false, "fmcg_tpm_activity_proof"
                , false, false, "dealer_id", false, "activity_id", false, "store_id"
                , false, "life_status", false, "activity_proof_id", "all", "default__c", "all", "1d", "1d", 1L, 1L, 0L, null, 20L, 0L, false
        )));
    }

    @Test
    public void testEncode() throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
    String  key = "physicalRewardGrayKey###";
    String content = "123456AAAAAA"; // 待加密内容
    try {
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        SecretKey secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        cipher.init(1, secretKey);
        String s = Base64.getEncoder().encodeToString(cipher.doFinal(content.getBytes(StandardCharsets.UTF_8)));
        System.out.println(s);
    } catch (Exception e) {
        System.out.println("加密失败");
        throw e;
    }
    }

    @Test
    public void physicalReward() throws MetadataServiceException {


        PhysicalRewardWriteOff.Arg arg = new PhysicalRewardWriteOff.Arg();
        arg.setBottleScan("1fsa9crydNjEbyuqUjtqZw==");

        PhysicalRewardWriteOff.Result result = physicalRewardService.outPhysicalRewardWriteOff(arg);

        System.out.println(result);


    }


    @Test
    public void test111() {

        String value;
        GetLicense.Arg arg = new GetLicense.Arg();
        arg.setAppCode("FMCG.BUDGET_LIMIT");
        GetLicense.Result result = fmcgServiceProxy.getLicense(85494, -10000, arg);
        value = String.valueOf(result.getLicense() != null);
        System.out.println(value);

    }


    @Test
    public void testDecode() throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        String  key = "secretKey-physicalReward";
        String content = "frhF9SVgR4V0a2zXbac/3w==";
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKey secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            cipher.init(2, secretKey);
            String s = new String(cipher.doFinal(Base64.getDecoder().decode(content.getBytes(StandardCharsets.UTF_8))), Charset.defaultCharset());
            System.out.println(s);
        } catch (Exception e) {
            System.out.println("加密失败");
            throw e;
        }
    }

    @Test
    public void testSql2() throws MetadataServiceException {
        System.out.println(objectDataService.findBySql("84931", "select distinct data_auth_code from dt_auth_simple where tenant_id = '84931' and ((object_describe_api_name = any('{TPMActivityObj}') and auth_target = any('{t.td.84931.1000,t.td.84931.1001,t.84931.1003,t.84931.1001,t.84931.1000,r.84931.marketingActivitiesManager,cd.84931.999999,tr.84931.inspector,t.tr.84931.00000000000000000000000000000006,t.84931.1007,t.84931.1006,t.84931.1005,t.84931.1004,cd.84931.1001,t.84931.1008,tr.84931.budgetFinance,dd.84931.1121,dd.84931.1000,dd.84931.1122,dd.84931.1001,84931.1003,84931.1004,84931.1005,td.84931.1157,r.84931.inspector,84931.1000,84931.1001,t.tr.84931.personnelrole,r.84931.personnelrole,84931.1006,84931.1007,84931.1008,t.tr.84931.00000000000000000000000000000032,t.tr.84931.00000000000000000000000000000033,cog.84931.1001,dd.84931.1110,td.84931.1000,td.84931.1001,tr.84931.marketingActivitiesManager,dd.84931.1117,dd.84931.1118,dd.84931.1119,tr.84931.00000000000000000000000000000006,tr.84931.personnelrole,u.84931.1000,r.84931.00000000000000000000000000000033,dd.84931.1120,tm.84931.1008,tm.84931.1007,tm.84931.1006,tm.84931.1005,tm.84931.1004,r.84931.00000000000000000000000000000006,tm.84931.1003,tr.84931.00000000000000000000000000000032,tr.84931.00000000000000000000000000000033,t.tr.84931.marketingActivitiesManager,tm.84931.1001,t.tr.84931.budgetFinance,tm.84931.1000,d.84931.1001,t.td.84931.1157,t.tr.84931.inspector,og.84931.1001}') )  )"));
    }

    @Test
    public void testRole() {
        System.out.println(serviceFacade.getUserRole(User.builder().tenantId("84931").userId("1000").build()));
    }

    @Test
    public void createWithdrawObj() {

        SearchTemplateQuery queryActivity = new SearchTemplateQuery();

        queryActivity.setOffset(0);
        queryActivity.setLimit(12);
        queryActivity.setSearchSource("db");

        Filter filter1 = new Filter();
        filter1.setFieldName("name");
        filter1.setOperator(Operator.IN);
        filter1.setFieldValues(Lists.newArrayList(
                "WDR--2024-03-01-000078",
                "WDR--2024-03-01-000088", "WDR--2024-03-01-000079", "WDR--2024-03-01-000089",
                "WDR--2024-03-01-000077", "WDR--2024-03-01-000076", "WDR--2024-03-01-000075",
                "WDR--2024-02-29-000074", "WDR--2024-02-29-000073", "WDR--2024-02-29-000072",
                "WDR--2024-02-29-000071", "WDR--2024-02-29-000070"));

        queryActivity.addFilters(Lists.newArrayList(filter1));
        User user = User.systemUser("82958");
        List<IObjectData> data = serviceFacade.findBySearchQuery(user, ApiNames.WITHDRAW_RECORD_OBJ, queryActivity).getData();

        data.forEach(obj -> {
            obj.set(WithdrawRecordObjFields.REWARD_PERSON_ID, "89386.1003");
        });
        List<IObjectData> dataList2 = serviceFacade.batchUpdateByFields(user, data,
                Lists.newArrayList(
                        WithdrawRecordObjFields.REWARD_PERSON_ID
                ));
    }

    @Test
    public void createObj() {
//        IObjectData objectData = serviceFacade.findObjectData(User.systemUser("82958"), "65a125a51aab3f0001b41e27", "RedPacketRecordObj");
//
//        List<IObjectData> dataList = new ArrayList<>();
//        for (int i = 0; i < 10; i++) {
//
//            IObjectData data = ObjectDataExt.of(objectData).copy();
//            data.setId(null);
//
//            dataList.add(data);
//        }
//        List<IObjectData> dataList1 = serviceFacade.bulkSaveObjectData(dataList, User.systemUser("82958"));

        SearchTemplateQuery queryActivity = new SearchTemplateQuery();

        queryActivity.setOffset(0);
        queryActivity.setLimit(10);
        queryActivity.setSearchSource("db");

        Filter filter = new Filter();
        filter.setFieldName("name");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList("RPR-2024-03-05-000108"));

        queryActivity.addFilters(Lists.newArrayList(filter));
        User user = User.systemUser("82958");
        List<IObjectData> data = serviceFacade.findBySearchQuery(user, ApiNames.RED_PACKET_RECORD_OBJ, queryActivity).getData();

        data.forEach(obj -> {
//            obj.set(RedPacketRecordObjFields.EXPIRATION_TIME, "1710227580000");
//            obj.set(RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.RedPacketStatus.EFFECTUATE);
//            obj.set(RedPacketRecordObjFields.WITHDRAWAL_STATUS, RedPacketRecordObjFields.WithdrawalStatus.AWAIT);

            obj.set(RedPacketRecordObjFields.REWARD_PART_CODE, "6296e9deead62b24377c1441.1");
        });
        List<IObjectData> dataList2 = serviceFacade.batchUpdateByFields(user, data,
                Lists.newArrayList(
//                        RedPacketRecordObjFields.EXPIRATION_TIME, RedPacketRecordObjFields.RED_PACKET_STATUS, RedPacketRecordObjFields.WITHDRAWAL_STATUS
                        RedPacketRecordObjFields.REWARD_PART_CODE
                ));
//
    }

    @Test
    public void redPacketWithdraw() {
//        ApiContextManager.getContext().setTenantId("89150");
//        ApiContextManager.getContext().setEmployeeId(1000);
//
//        PreviewWithdraw.Arg arg = new PreviewWithdraw.Arg();
//        arg.setRedPacketRecordIdList(Lists.newArrayList("655dc864f12bd70dec972e65", "655dc864f12bd70dec972e43",
//                "655dc864f12bd70dec972e49"));
//
//        arg.setWhiteIdList(Lists.newArrayList("655dc864f12bd70dec972e65","655dc864f12bd70dec972e43"));
//
//        PreviewWithdraw.Result result = withdrawService.previewWithdraw(arg);
//        System.out.println(result);

        String PREFIX = "withdraw-%s-%s-%s-%s-%s";
        IObjectData data = serviceFacade.findObjectData(User.systemUser("82958"), "6584237bd78f810001a51606", "RedPacketRecordObj");

        //根据 转出方企业ei + 转出方账户类型 + 转出方账户 + 转入方appId + 转入方身份证号 分组，创建提现记录
        String transfereeTenantId = data.get(RedPacketRecordObjFields.TRANSFEROR_TENANT_ID, String.class);
        String transfereeAccountType = data.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT_TYPE, String.class);
        String transfereeAccount = data.get(RedPacketRecordObjFields.TRANSFEROR_ACCOUNT, String.class);
        String wechatAppId = data.get(RedPacketRecordObjFields.TRANSFEREE_WECHAT_APP_ID, String.class);
        String transfereeId = data.get(RedPacketRecordObjFields.TRANSFEREE_ID, String.class);
        String key =  String.format(PREFIX, transfereeTenantId, transfereeAccountType, transfereeAccount, wechatAppId, transfereeId);
        System.out.println(key);



    }

    @Test
    public void redSubmitWithdraw() {
        ApiContextManager.getContext().setTenantId("89386");
        ApiContextManager.getContext().setEmployeeId(1003);
//        ApiContextManager.getContext().setOutTenantId("*********");
//        ApiContextManager.getContext().setOutUserId("*********");

        SubmitWithdraw.Arg arg = new SubmitWithdraw.Arg();
        //"65703e5ea11f15ee5ef28fe8", "65703e5da11f15ee5ef28f71"
//        arg.setRedPacketRecordIdList(Lists.newArrayList("65703e5da11f15ee5ef28f71"));
        arg.setWhiteIdList(Lists.newArrayList("65a126331aab3f0001b4359d", "65a125a41aab3f0001b41adb", "65a125171aab3f0001b3f262", "65a125a51aab3f0001b41d81"));
        arg.setAmount("1.2");
        SubmitWithdraw.Result result = withdrawService.submitWithdraw(arg);
        System.out.println(result);
    }


    @Test
    public void redPreSubmitWithdraw() {
        ApiContextManager.getContext().setTenantId("89386");
        ApiContextManager.getContext().setEmployeeId(1003);

        PreviewWithdraw.Arg arg = new PreviewWithdraw.Arg();
        //"65703e5ea11f15ee5ef28fe8", "65703e5da11f15ee5ef28f71"
//        arg.setRedPacketRecordIdList(Lists.newArrayList("65703e5da11f15ee5ef28f71"));
        arg.setAmount("1");
        PreviewWithdraw.Result result = withdrawService.previewWithdraw(arg);
        System.out.println(result);
    }
    @Test
    public void redPacketWithdrawDashboard() {
//        ApiContextManager.getContext().setTenantId("89386");
//        ApiContextManager.getContext().setEmployeeId(1003);
//        ApiContextManager.getContext().setOutTenantId("*********");
//        ApiContextManager.getContext().setOutUserId("*********");
//
//        Dashboard.Arg arg = new Dashboard.Arg();
//
//        Dashboard.Result dashboard = withdrawService.dashboard(arg);
//        System.out.println(dashboard);

        System.out.println(getPattern(5));
    }

    private String getPattern(int size) {
        StringBuilder builder = new StringBuilder("( 1 or 2 ) and");
        for (int i = 3; i <= size; i++) {
            builder.append(" ").append(i);
            if (i != size) {
                builder.append(" and");
            }
        }
        return builder.toString();
    }

    @Test
    public void redPacketWithdrawQuery() {
        ApiContextManager.getContext().setTenantId("89386");
        ApiContextManager.getContext().setEmployeeId(1003);
//        ApiContextManager.getContext().setOutTenantId("*********");
//        ApiContextManager.getContext().setOutUserId("*********");

        QueryRedPacketRecords.Arg arg = new QueryRedPacketRecords.Arg();
        arg.setPageSize(20);
        arg.setPageIndex(0);
//        arg.setLastItem("65f17b4f9ce5c30001364090");
        arg.setRedPacketStatus("await");
        arg.setNeedSimpleLayout(true);
//        arg.setNeedTotalAmount(true);
        QueryRedPacketRecords.Result result = withdrawService.queryRedPacketRecords(arg);
        System.out.println(result);
    }

    @Test
    public void withdrawRecordsQuery() {
        ApiContextManager.getContext().setTenantId("89386");
        ApiContextManager.getContext().setEmployeeId(1003);

        QueryWithdrawRecords.Arg arg = new QueryWithdrawRecords.Arg();
        arg.setPageSize(20);
        arg.setPageIndex(0);
        arg.setNeedSimpleLayout(true);
        QueryWithdrawRecords.Result result = withdrawService.queryWithdrawRecords(arg);
        System.out.println(result);
    }

    @Test
    public void auditSummaryListTset() {

        AgreementAuditSummaryList.Arg arg = new AgreementAuditSummaryList.Arg();
        arg.setAgreementId("65b75f3bcb1c9900018a5463");
        arg.setDealerId("11");
        arg.setOutTenantId("*********");
        AgreementAuditSummaryList.Result result = agreementAuditService.auditSummaryList(arg);

        System.out.println(result);

    }

    @Test
    public void auditTest() {

//        IObjectData objectData = serviceFacade.findObjectData(User.systemUser("84931"), "65b378e74bd5bd0001a89011", "object_Jk6CW__c");
//        IObjectDescribe describe = serviceFacade.findObject("84931", ApiNames.ACCOUNT_OBJ);
//
//        AgreementAuditDataList.Arg arg = new AgreementAuditDataList.Arg();
//        arg.setAgreementId("65b75f3bcb1c9900018a5463");
//        arg.setDealerId("111");
//        arg.setId("65b326cf83728e0007231b81");
//        arg.setOutTenantId("*********");
//        arg.setDataIdList(Lists.newArrayList("65b8c391c5bb5e00012e0dc3", "65b868fdcb1c990001a89c1c", "65b76257cb1c9900018a716f", "65b75ff8cb1c9900018a5fde"));
//        AgreementAuditDataList.Result result = agreementAuditService.dataList(arg);
//
//        System.out.println(result);
        /*
{trading_id=WD:65E1906277B122178DDC9654, payment_status=0, order_id=CAP100005103202403011623012160014}
User{tenantId=82958, userId=-10000, outUserId=null, outTenantId=null, upstreamOwnerId=null}
{trading_id=WD:65E1906277B122178DDC9655, payment_status=0, order_id=CAP100007041202403011623012590015}
User{tenantId=82958, userId=-10000, outUserId=null, outTenantId=null, upstreamOwnerId=null}
SubmitWithdraw.Result(amount=1.20, receivedAccount=微信零钱)
         */


        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put("trading_id", "65E1906277B122178DDC9654");
        updateMap.put("payment_status", "0");
        updateMap.put("order_id", "CAP100005103202403011623012160014");
        System.out.println(updateMap);
        IObjectData withdrawRecord = serviceFacade.findObjectData(User.systemUser("82958"), "65e1820928b9a2103cfa143d", "WithdrawRecordObj");
        User sysUser = User.systemUser("82958");
        IObjectData objectData1 = serviceFacade.updateWithMap(sysUser, withdrawRecord, updateMap);
        System.out.println(objectData1);

    }

    @Test
    public void operateAuditTest() {

//        IObjectData objectData = serviceFacade.findObjectData(User.systemUser("84931"), "65b378e74bd5bd0001a89011", "object_Jk6CW__c");
//
//        OperateAudit.Arg arg = new OperateAudit.Arg();
//        arg.setAgreementId("65b75f3bcb1c9900018a5463");
//        arg.setDealerId("65b326cf83728e0007231b81");
//        arg.setOutTenantId("*********");
//        arg.setAuditStatus("reject");
//        arg.setAuditOpinion("重新触发操作。");
//        OperateAudit.Result result = agreementAuditService.operateAudit(arg);
//
//        System.out.println(result);

        IObjectData objectData = serviceFacade.findObjectData(User.systemUser("84931"), "663897387f0ce00001945bbc", "TPMActivityAgreementObj");

        IObjectDescribe describe = serviceFacade.findObject("84931", "TPMActivityAgreementObj");
        serviceFacade.logWithCustomMessage(
                User.systemUser("84931"),
                EventType.MODIFY,
                ActionType.MODIFY,
                describe,
                objectData,
                getMessage());

    }

    private String getMessage() {
        Map<String,String> messageMap = new HashMap<String, String>();
        messageMap.put("01","12");
        messageMap.put("02","15");
        messageMap.put("03","16");

        return JSONObject.toJSONString(messageMap);
    }


}
