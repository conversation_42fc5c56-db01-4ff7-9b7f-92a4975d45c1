package com.facishare.crm.fmcg.tpm;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.api.activity.GetCustomizedInfo;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigEnum;
import com.facishare.crm.fmcg.tpm.web.contract.AddConfig;
import com.facishare.crm.fmcg.tpm.web.contract.model.APLDisplayCustomizedInfoVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.FunctionInfo;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityCustomizedInfoService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IConfigService;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;


public class ConfigServiceTest extends BaseTest {

    @Resource
    private IConfigService tpmConfigService;
    @Resource
    private IActivityCustomizedInfoService activityCustomizedInfoService;

    @Test
    public void gray815Test() {
        APLDisplayCustomizedInfoVO vo = new APLDisplayCustomizedInfoVO();
        vo.setTenantId("84931");
        vo.setObjectApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        FunctionInfo functionInfo = new FunctionInfo();
        functionInfo.setFunctionName("测试返回");
        functionInfo.setBindingObjectApiName("NONE");
        functionInfo.setNameSpace("fmcg_tpm_activity_display_customized_information");
        functionInfo.setReturnType("List");
        functionInfo.setApiName("fmcg_tpmdci_testReturn__c");
        vo.setFunctionInfo(functionInfo);

        APLDisplayCustomizedInfoVO vo2 = new APLDisplayCustomizedInfoVO();
        vo2.setTenantId("84931");
        vo2.setObjectApiName(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        FunctionInfo functionInfo2 = new FunctionInfo();
        functionInfo2.setFunctionName("测试返回");
        functionInfo2.setBindingObjectApiName("NONE");
        functionInfo2.setNameSpace("fmcg_tpm_activity_display_customized_information");
        functionInfo2.setReturnType("List");
        functionInfo2.setApiName("fmcg_tpmdci_testReturn__c");
        vo2.setFunctionInfo(functionInfo2);

        List<APLDisplayCustomizedInfoVO> list = Lists.newArrayList(vo, vo2);

        ConfigVO configVO = new ConfigVO();
        configVO.setKey(ConfigEnum.APL_DISPLAY_CUSTOMIZED_INFORMATION.key());
        configVO.setValue(JSON.toJSONString(list));

        AddConfig.Arg arg = new AddConfig.Arg();
        arg.setConfigs(Lists.newArrayList(configVO));
        tpmConfigService.save(arg);

        System.out.println("success");
    }

    @Test
    public void test2() {
        GetCustomizedInfo.Arg arg = new GetCustomizedInfo.Arg();
        arg.setObjectApiName(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        arg.setStoreId("store");
        arg.setActivityIds(Lists.newArrayList("activity"));
        GetCustomizedInfo.Result customizedInfo = activityCustomizedInfoService.getCustomizedInfo(arg);
        System.out.println(JSON.toJSONString(customizedInfo));
    }
}
