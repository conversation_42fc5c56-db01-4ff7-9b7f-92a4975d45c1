package com.facishare.crm.fmcg.pay;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.checkins.api.model.GetOpenIdByToken;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.crm.fmcg.common.adapter.ReceiveMoneyService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.QueryCloudTransferDetails;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.WXCloudPayReceiverAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.QueryWXTenantTransferDetail;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryEnterpriseUnionISV;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordObjFields;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.mengniu.retry.RewardRecordRetryHandler;
import com.facishare.crm.fmcg.mengniu.business.RewardRecordService;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.CloseWXOrder;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.FormWxPayMini;
import com.facishare.crm.fmcg.common.adapter.dto.receipts.QueryTransferDetailForReceipts;
import com.facishare.crm.fmcg.tpm.dao.mongo.RetryTaskDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.retry.handler.RetryHandlerEnum;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.redisson.api.RDeque;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/8/1 16:11
 */
public class PayTest extends BaseTest {

    @Resource
    private IPayService payService;

    @Resource
    private ShopMMService shopMMService;

    @Resource
    private RewardRecordService rewardRecordService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private TenantHierarchyService tenantHierarchyService;

    @Resource
    private RetryTaskDAO retryTaskDAO;

    @Resource
    private RedissonClient redissonCmd;

    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;


    @Resource
    private RewardRecordRetryHandler rewardRecordRetryHandler;


    @Resource
    private ReceiveMoneyService receiveMoneyService;


    @Test
    public void testQuery() {

        QueryCloudTransferDetails.Arg arg = new QueryCloudTransferDetails.Arg();
        arg.setBusinessId("RP668C9CEA498BF40001E93CFD");

        System.out.println(JSON.toJSONString(payService.queryCloudTransferDetails(UserInfo.builder().tenantId("82958").userId("1000").build(), arg)));
    }


    @Test
    public void testCloudTransfer() {
        //{"businessId":"mn0jiege066","refTransferId":"826282910603592388","transferId":"CAP100003082202308021957217320007"}
//        CloudTransfer.Arg cloudTransfer = new CloudTransfer.Arg();
//        cloudTransfer.setBusinessId("mn0jiege061");
//        cloudTransfer.setAmount(new BigDecimal("0.3"));
//        CloudAccount cloudAccount = new CloudAccount();
//        cloudAccount.setTenantAccount("89150");
//        cloudTransfer.setPayerCloudAccount(cloudAccount);
//        cloudTransfer.setRemarks("test");
//        WXCloudPayReceiverAccount cloudPayReceiverAccount = new WXCloudPayReceiverAccount();
//        cloudPayReceiverAccount.setOpenId("oLJW0wmjv0AuUZEmmWoaDR9YXMX1");
//        //cloudPayReceiverAccount.setAppId("wxf70ac4798732b66f");
//        cloudPayReceiverAccount.setRealName("林明杰");
//        cloudPayReceiverAccount.setIdCardNumber("350181199702201697");
//        cloudTransfer.setReceiverPayAccount(cloudPayReceiverAccount);
//        System.out.println(JSON.toJSONString(payService.cloudTransfer(UserInfo.builder().tenantId("89150").userId("1000").build(), cloudTransfer)));

/*
businessId=WD:6571444292363D332E9A6144, payerCloudAccount=CloudAccount(tenantAccount=default, account=null),
 receiverPayAccount=WXCloudPayReceiverAccount(openId=oLJW0wmjv0AuUZEmmWoaDR9YXMX4, appId=wxb210127d73a3dc2f, payMode=null),
  amount=0.10, remarks=null
 */
        CloudTransfer.Arg cloudTransfer = new CloudTransfer.Arg();
        cloudTransfer.setBusinessId("WD:6571444292363D332E9A61442");
        cloudTransfer.setAmount(new BigDecimal("0.30"));
        CloudAccount cloudAccount = new CloudAccount();
        cloudAccount.setTenantAccount("82958");
        cloudTransfer.setPayerCloudAccount(cloudAccount);
        cloudTransfer.setRemarks("test");
        WXCloudPayReceiverAccount cloudPayReceiverAccount = new WXCloudPayReceiverAccount();
        cloudPayReceiverAccount.setOpenId("oLJW0wmjv0AuUZEmmWoaDR9YXMX4");
        //cloudPayReceiverAccount.setAppId("wxf70ac4798732b66f");
        cloudPayReceiverAccount.setRealName("林明杰");
        cloudPayReceiverAccount.setIdCardNumber("350181199702201697");
        cloudTransfer.setReceiverPayAccount(cloudPayReceiverAccount);
        System.out.println(JSON.toJSONString(payService.cloudTransfer(UserInfo.builder().tenantId("82958").userId("1000").build(), cloudTransfer)));
    }


    @Test
    public void testWXTransfer() {
        BatchWXTenantTransfer.Arg arg = new BatchWXTenantTransfer.Arg();
        arg.setBatchTransferId("MNBT106");
        arg.setBatchName("ceshi2");
        arg.setBatchRemarks("ceshi it2");
        WXTenantAccount wxTenantAccount = new WXTenantAccount();
        wxTenantAccount.setTenantAccount("82958");
        wxTenantAccount.setSubMchId("test1");
        arg.setPayeeWXAccount(wxTenantAccount);
        WXPersonalAccount wxPersonalAccount = new WXPersonalAccount();
        wxPersonalAccount.setAmount(new BigDecimal("0.01"));
        wxPersonalAccount.setRemarks("detail test2");
        wxPersonalAccount.setBusinessId("MNBT106");
        wxPersonalAccount.setAppId("wxf70ac4798732b66f");
        wxPersonalAccount.setOpenId("o3TSW4gFGu46J7dT6MGTvp3SsxU0");

        arg.setReceiverAccounts(Lists.newArrayList(wxPersonalAccount));
        System.out.println(JSON.toJSONString(payService.batchWXTenantTransfer(UserInfo.builder().tenantId("82958").userId("1000").build(), arg)));
    }

    @Test
    public void getToken() {
        GetOpenIdByToken.Args args = new GetOpenIdByToken.Args();
        args.setToken("0c1klxHa1bffKF06UXGa1D3lQI1klxHG");
        args.setTenantId("84931");
        args.setAppId("wxf70ac4798732b66f");
        args.setPhoneToken("2cbbfe0a81e9a0526b5faf2f049ab979979850cce26ef3d19012939b4c717a9a");
        System.out.println(shopMMService.getOpenIdByToken(args));
    }

    @Test
    public void testQueryWX() {

        QueryWXTenantTransferDetail.Arg batchQueryArg = new QueryWXTenantTransferDetail.Arg();
        batchQueryArg.setBatchTransferId("**************************");
        UserInfo userInfo = UserInfo.builder().tenantId("89150").userId("1000").build();
        QueryWXTenantTransferDetail.Result batchQueryResult = payService.queryWXTenantTransferDetails(userInfo, batchQueryArg);
        System.out.println(JSON.toJSONString(batchQueryResult));
    }

    @Test
    public void testRefresh() {
        IObjectData data = serviceFacade.findObjectData(User.systemUser("89150"), "64d2080d562a940001fe2c7b", "red_packet_record__c");
        rewardRecordService.refreshRewardStatus(data);
    }

    @Test
    public void testTenantHierarchy() {
        //System.out.println(JSON.toJSONString(tenantHierarchyService.get("1","14444")));
    }

    @Test
    public void testDao() {
        retryTaskDAO.get("5ed60fc1e49907410676fec0");
    }


    @Test
    public void testRedis() {

        RDeque<String> deque = redissonCmd.getDeque("retry_task_queue_REWARD_RECORD_UPDATE_HANDLER");
        if (deque.isExists() && !deque.isEmpty()) {
            return;
        }
        deque.add("64d59eea7c4d340ddf357541");
    }


    @Test
    public void testRewardHandler() {
        RetryTaskPO retryTaskPO = retryTaskDAO.get("64d917a41743844c73e4d519");
        retryTaskPO.setRetryCount(0);
        rewardRecordRetryHandler.handle(retryTaskPO);
    }

    @Test
    public void addRetryPO() {
        RetryTaskPO po = new RetryTaskPO();

        po.setHandler(RetryHandlerEnum.REWARD_RECORD_UPDATE_HANDLER.code());
        po.setTenantId("89150");
        po.setRetryCount(0);
        po.setIndexDeleteTimeBase(new Date());

        retryTaskDAO.save(po);
    }


    @Test
    public void testReceive() {
        FormWxPayMini.Arg formWxPayMiniArg = new FormWxPayMini.Arg();
        FormWxPayMini.Receiver receiver = new FormWxPayMini.Receiver();
        formWxPayMiniArg.setReceiver(receiver);
        receiver.setReceiverUserId(1000L);
        receiver.setReceiverTenantAccount("89386");
        receiver.setReceiverTenantId(89386);
        FormWxPayMini.Payer payer = new FormWxPayMini.Payer();
        formWxPayMiniArg.setPayer(payer);
        payer.setDisplayName("消费者");
        payer.setWxOpenId("o3TSW4gFGu46J7dT6MGTvp3SsxU0");
        payer.setWxAppId("wxf70ac4798732b66f");
        formWxPayMiniArg.setAmount(new BigDecimal("0.01"));
        formWxPayMiniArg.setDescribeTitle("test");
        formWxPayMiniArg.setDescribeContent("test content");
        formWxPayMiniArg.setBusinessId("busitestpay1234");
        System.out.println(JSON.toJSONString(receiveMoneyService.formWxPayMini(formWxPayMiniArg)));
    }


    @Test
    public void testQueryReceive() {
        QueryTransferDetailForReceipts.Arg arg = new QueryTransferDetailForReceipts.Arg();
        arg.setTenantId("89386");
        arg.setBusinessId("SP:652CD47B9C053A0001B715AA");

        System.out.println(JSON.toJSONString(receiveMoneyService.queryTransferDetailForReceipts(arg)));
    }

    @Test
    public void closeOrder() {
        CloseWXOrder.Arg arg = new CloseWXOrder.Arg();
        arg.setTenantId("89386");
        arg.setBusinessId("SP:652CD47B9C053A0001B715AA");
        System.out.println(JSON.toJSONString(receiveMoneyService.closeWXOrder(arg)));
    }


    @Test
    public void testQueryISV() {
        QueryEnterpriseUnionISV.Arg arg = new QueryEnterpriseUnionISV.Arg();
        arg.setTenantId("89386");
        System.out.println(JSON.toJSONString(receiveMoneyService.queryEnterpriseUnionISV(arg)));
        arg.setTenantId("82958");
        System.out.println(JSON.toJSONString(receiveMoneyService.queryEnterpriseUnionISV(arg)));
        arg.setTenantId("89150");
        System.out.println(JSON.toJSONString(receiveMoneyService.queryEnterpriseUnionISV(arg)));
    }

    @Test
    public void testPay() {
        List<IObjectData> result = serviceFacade.aggregateFindBySearchQuery("82958", SearchQueryUtil.formSimpleQuery(0, 100, Lists.newArrayList()), ApiNames.RED_PACKET_RECORD_OBJ, RedPacketRecordObjFields.ACTIVITY_ID, "sum", RedPacketRecordObjFields.REWARD_AMOUNT);
        System.out.println(JSON.toJSONString(result));
    }
}
